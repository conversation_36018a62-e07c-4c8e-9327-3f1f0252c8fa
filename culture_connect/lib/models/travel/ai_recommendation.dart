import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/itinerary_item.dart';

/// A recommendation from the AI
class AIRecommendation {
  /// The unique identifier
  final String id;

  /// The recommended item
  final ItineraryItem item;

  /// The confidence score (0-100)
  final double confidenceScore;

  /// The reason for the recommendation
  final String reason;

  /// The category of the recommendation
  final RecommendationCategory category;

  /// Whether the recommendation has been accepted
  final bool isAccepted;

  /// Whether the recommendation has been rejected
  final bool isRejected;

  /// Creates a new AI recommendation
  AIRecommendation({
    required this.id,
    required this.item,
    required this.confidenceScore,
    required this.reason,
    required this.category,
    this.isAccepted = false,
    this.isRejected = false,
  });

  /// Get the confidence level
  ConfidenceLevel get confidenceLevel {
    if (confidenceScore >= 90) return ConfidenceLevel.veryHigh;
    if (confidenceScore >= 75) return ConfidenceLevel.high;
    if (confidenceScore >= 60) return ConfidenceLevel.medium;
    if (confidenceScore >= 40) return ConfidenceLevel.low;
    return ConfidenceLevel.veryLow;
  }

  /// Create a copy of this recommendation with the given fields replaced
  AIRecommendation copyWith({
    String? id,
    ItineraryItem? item,
    double? confidenceScore,
    String? reason,
    RecommendationCategory? category,
    bool? isAccepted,
    bool? isRejected,
  }) {
    return AIRecommendation(
      id: id ?? this.id,
      item: item ?? this.item,
      confidenceScore: confidenceScore ?? this.confidenceScore,
      reason: reason ?? this.reason,
      category: category ?? this.category,
      isAccepted: isAccepted ?? this.isAccepted,
      isRejected: isRejected ?? this.isRejected,
    );
  }

  /// Accept this recommendation
  AIRecommendation accept() {
    return copyWith(isAccepted: true, isRejected: false);
  }

  /// Reject this recommendation
  AIRecommendation reject() {
    return copyWith(isAccepted: false, isRejected: true);
  }

  /// Reset this recommendation
  AIRecommendation reset() {
    return copyWith(isAccepted: false, isRejected: false);
  }

  /// Create an AI recommendation from JSON
  factory AIRecommendation.fromJson(Map<String, dynamic> json) {
    return AIRecommendation(
      id: json['id'],
      item: ItineraryItem.fromJson(json['item']),
      confidenceScore: json['confidenceScore'],
      reason: json['reason'],
      category: RecommendationCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => RecommendationCategory.general,
      ),
      isAccepted: json['isAccepted'] ?? false,
      isRejected: json['isRejected'] ?? false,
    );
  }

  /// Convert this AI recommendation to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'item': item.toJson(),
      'confidenceScore': confidenceScore,
      'reason': reason,
      'category': category.name,
      'isAccepted': isAccepted,
      'isRejected': isRejected,
    };
  }
}

/// Category of a recommendation
enum RecommendationCategory {
  /// General recommendation
  general,

  /// Based on user preferences
  userPreferences,

  /// Based on user history
  userHistory,

  /// Based on popularity
  popularity,

  /// Based on location
  location,

  /// Based on weather
  weather,

  /// Based on budget
  budget,

  /// Based on time constraints
  timeConstraints,
}

/// Extension for recommendation categories
extension RecommendationCategoryExtension on RecommendationCategory {
  /// Get the display name for the recommendation category
  String get displayName {
    switch (this) {
      case RecommendationCategory.general:
        return 'General';
      case RecommendationCategory.userPreferences:
        return 'Based on Your Preferences';
      case RecommendationCategory.userHistory:
        return 'Based on Your History';
      case RecommendationCategory.popularity:
        return 'Popular Choice';
      case RecommendationCategory.location:
        return 'Based on Location';
      case RecommendationCategory.weather:
        return 'Weather Appropriate';
      case RecommendationCategory.budget:
        return 'Budget Friendly';
      case RecommendationCategory.timeConstraints:
        return 'Time Optimized';
    }
  }

  /// Get the icon for the recommendation category
  IconData get icon {
    switch (this) {
      case RecommendationCategory.general:
        return Icons.recommend;
      case RecommendationCategory.userPreferences:
        return Icons.favorite;
      case RecommendationCategory.userHistory:
        return Icons.history;
      case RecommendationCategory.popularity:
        return Icons.trending_up;
      case RecommendationCategory.location:
        return Icons.location_on;
      case RecommendationCategory.weather:
        return Icons.wb_sunny;
      case RecommendationCategory.budget:
        return Icons.savings;
      case RecommendationCategory.timeConstraints:
        return Icons.schedule;
    }
  }

  /// Get the color for the recommendation category
  Color get color {
    switch (this) {
      case RecommendationCategory.general:
        return Colors.blue;
      case RecommendationCategory.userPreferences:
        return Colors.red;
      case RecommendationCategory.userHistory:
        return Colors.purple;
      case RecommendationCategory.popularity:
        return Colors.orange;
      case RecommendationCategory.location:
        return Colors.green;
      case RecommendationCategory.weather:
        return Colors.amber;
      case RecommendationCategory.budget:
        return Colors.teal;
      case RecommendationCategory.timeConstraints:
        return Colors.indigo;
    }
  }
}

/// Confidence level of a recommendation
enum ConfidenceLevel {
  /// Very low confidence
  veryLow,

  /// Low confidence
  low,

  /// Medium confidence
  medium,

  /// High confidence
  high,

  /// Very high confidence
  veryHigh,
}

/// Extension for confidence levels
extension ConfidenceLevelExtension on ConfidenceLevel {
  /// Get the display name for the confidence level
  String get displayName {
    switch (this) {
      case ConfidenceLevel.veryLow:
        return 'Very Low';
      case ConfidenceLevel.low:
        return 'Low';
      case ConfidenceLevel.medium:
        return 'Medium';
      case ConfidenceLevel.high:
        return 'High';
      case ConfidenceLevel.veryHigh:
        return 'Very High';
    }
  }

  /// Get the color for the confidence level
  Color get color {
    switch (this) {
      case ConfidenceLevel.veryLow:
        return Colors.red;
      case ConfidenceLevel.low:
        return Colors.orange;
      case ConfidenceLevel.medium:
        return Colors.amber;
      case ConfidenceLevel.high:
        return Colors.lightGreen;
      case ConfidenceLevel.veryHigh:
        return Colors.green;
    }
  }
}
