import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/travel.dart';

/// Sort options for flights
enum FlightSortOption {
  /// Sort by price (low to high)
  priceLowToHigh,

  /// Sort by price (high to low)
  priceHighToLow,

  /// Sort by duration (shortest first)
  duration,

  /// Sort by departure time (earliest first)
  departureTime,

  /// Sort by arrival time (earliest first)
  arrivalTime,

  /// Sort by airline name
  airline,
}

/// Extension for flight sort options
extension FlightSortOptionExtension on FlightSortOption {
  /// Get the display name for the sort option
  String get displayName {
    switch (this) {
      case FlightSortOption.priceLowToHigh:
        return 'Price: Low to High';
      case FlightSortOption.priceHighToLow:
        return 'Price: High to Low';
      case FlightSortOption.duration:
        return 'Duration: Shortest First';
      case FlightSortOption.departureTime:
        return 'Departure: Earliest First';
      case FlightSortOption.arrivalTime:
        return 'Arrival: Earliest First';
      case FlightSortOption.airline:
        return 'Airline';
    }
  }

  /// Get the icon for the sort option
  IconData get icon {
    switch (this) {
      case FlightSortOption.priceLowToHigh:
        return Icons.arrow_upward;
      case FlightSortOption.priceHighToLow:
        return Icons.arrow_downward;
      case FlightSortOption.duration:
        return Icons.timelapse;
      case FlightSortOption.departureTime:
        return Icons.flight_takeoff;
      case FlightSortOption.arrivalTime:
        return Icons.flight_land;
      case FlightSortOption.airline:
        return Icons.airlines;
    }
  }
}

/// Time of day range for filtering
class TimeRange {
  /// Start time in minutes from midnight
  final int startMinutes;

  /// End time in minutes from midnight
  final int endMinutes;

  /// Creates a new time range
  const TimeRange({
    required this.startMinutes,
    required this.endMinutes,
  });

  /// Creates a time range from TimeOfDay objects
  factory TimeRange.fromTimeOfDay({
    required TimeOfDay start,
    required TimeOfDay end,
  }) {
    return TimeRange(
      startMinutes: start.hour * 60 + start.minute,
      endMinutes: end.hour * 60 + end.minute,
    );
  }

  /// Get the start time as TimeOfDay
  TimeOfDay get startTime {
    return TimeOfDay(
      hour: startMinutes ~/ 60,
      minute: startMinutes % 60,
    );
  }

  /// Get the end time as TimeOfDay
  TimeOfDay get endTime {
    return TimeOfDay(
      hour: endMinutes ~/ 60,
      minute: endMinutes % 60,
    );
  }

  /// Check if a time (in minutes from midnight) is within this range
  bool contains(int minutes) {
    return minutes >= startMinutes && minutes <= endMinutes;
  }

  /// Check if a TimeOfDay is within this range
  bool containsTimeOfDay(TimeOfDay time) {
    final minutes = time.hour * 60 + time.minute;
    return contains(minutes);
  }

  /// Get the formatted start time
  String get formattedStartTime {
    final hour = startMinutes ~/ 60;
    final minute = startMinutes % 60;
    final period = hour < 12 ? 'AM' : 'PM';
    final displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    return '$displayHour:${minute.toString().padLeft(2, '0')} $period';
  }

  /// Get the formatted end time
  String get formattedEndTime {
    final hour = endMinutes ~/ 60;
    final minute = endMinutes % 60;
    final period = hour < 12 ? 'AM' : 'PM';
    final displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    return '$displayHour:${minute.toString().padLeft(2, '0')} $period';
  }

  /// Get the formatted time range
  String get formattedRange {
    return '$formattedStartTime - $formattedEndTime';
  }
}

/// Filter options for flights
class FlightFilter {
  /// Price range (min, max)
  final RangeValues? priceRange;

  /// Departure time range
  final TimeRange? departureTimeRange;

  /// Arrival time range
  final TimeRange? arrivalTimeRange;

  /// Maximum duration in minutes
  final int? maxDurationMinutes;

  /// Flight types to include
  final List<FlightType>? flightTypes;

  /// Airlines to include
  final List<String>? airlines;

  /// Flight classes to include
  final List<FlightClass>? flightClasses;

  /// Sort option
  final FlightSortOption sortOption;

  /// Creates a new flight filter
  const FlightFilter({
    this.priceRange,
    this.departureTimeRange,
    this.arrivalTimeRange,
    this.maxDurationMinutes,
    this.flightTypes,
    this.airlines,
    this.flightClasses,
    this.sortOption = FlightSortOption.priceLowToHigh,
  });

  /// Create a copy with some fields replaced
  FlightFilter copyWith({
    RangeValues? priceRange,
    TimeRange? departureTimeRange,
    TimeRange? arrivalTimeRange,
    int? maxDurationMinutes,
    List<FlightType>? flightTypes,
    List<String>? airlines,
    List<FlightClass>? flightClasses,
    FlightSortOption? sortOption,
  }) {
    return FlightFilter(
      priceRange: priceRange ?? this.priceRange,
      departureTimeRange: departureTimeRange ?? this.departureTimeRange,
      arrivalTimeRange: arrivalTimeRange ?? this.arrivalTimeRange,
      maxDurationMinutes: maxDurationMinutes ?? this.maxDurationMinutes,
      flightTypes: flightTypes ?? this.flightTypes,
      airlines: airlines ?? this.airlines,
      flightClasses: flightClasses ?? this.flightClasses,
      sortOption: sortOption ?? this.sortOption,
    );
  }

  /// Reset all filters
  FlightFilter reset() {
    return const FlightFilter(
      sortOption: FlightSortOption.priceLowToHigh,
    );
  }

  /// Apply the filter to a list of flights
  List<Flight> apply(List<Flight> flights) {
    var filtered = List<Flight>.from(flights);

    // Filter by price range
    if (priceRange != null) {
      filtered = filtered
          .where((flight) =>
              flight.price >= priceRange!.start &&
              flight.price <= priceRange!.end)
          .toList();
    }

    // Filter by departure time range
    if (departureTimeRange != null) {
      filtered = filtered.where((flight) {
        final departureMinutes =
            flight.departureTime.hour * 60 + flight.departureTime.minute;
        return departureTimeRange!.contains(departureMinutes);
      }).toList();
    }

    // Filter by arrival time range
    if (arrivalTimeRange != null) {
      filtered = filtered.where((flight) {
        final arrivalMinutes =
            flight.arrivalTime.hour * 60 + flight.arrivalTime.minute;
        return arrivalTimeRange!.contains(arrivalMinutes);
      }).toList();
    }

    // Filter by maximum duration
    if (maxDurationMinutes != null) {
      filtered = filtered
          .where((flight) => flight.totalDurationMinutes <= maxDurationMinutes!)
          .toList();
    }

    // Filter by flight types
    if (flightTypes != null && flightTypes!.isNotEmpty) {
      filtered = filtered
          .where((flight) => flightTypes!.contains(flight.flightType))
          .toList();
    }

    // Filter by airlines
    if (airlines != null && airlines!.isNotEmpty) {
      filtered = filtered
          .where(
              (flight) => airlines!.contains(flight.segments.first.airlineName))
          .toList();
    }

    // Filter by flight classes
    if (flightClasses != null && flightClasses!.isNotEmpty) {
      filtered = filtered
          .where((flight) => flightClasses!.contains(flight.flightClass))
          .toList();
    }

    // Sort the results
    switch (sortOption) {
      case FlightSortOption.priceLowToHigh:
        filtered.sort((a, b) => a.price.compareTo(b.price));
        break;
      case FlightSortOption.priceHighToLow:
        filtered.sort((a, b) => b.price.compareTo(a.price));
        break;
      case FlightSortOption.duration:
        filtered.sort(
            (a, b) => a.totalDurationMinutes.compareTo(b.totalDurationMinutes));
        break;
      case FlightSortOption.departureTime:
        filtered.sort((a, b) => a.departureTime.compareTo(b.departureTime));
        break;
      case FlightSortOption.arrivalTime:
        filtered.sort((a, b) => a.arrivalTime.compareTo(b.arrivalTime));
        break;
      case FlightSortOption.airline:
        filtered.sort((a, b) => a.segments.first.airlineName
            .compareTo(b.segments.first.airlineName));
        break;
    }

    return filtered;
  }
}
