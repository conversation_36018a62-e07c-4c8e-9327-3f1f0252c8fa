import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/services/ar_experience_service.dart';
import 'package:culture_connect/services/location_service.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Provider for the AR experience service
final arExperienceServiceProvider = Provider<ARExperienceService>((ref) {
  final loggingService = ref.watch(loggingServiceProvider);
  final locationService = ref.watch(locationServiceProvider);

  return ARExperienceService(loggingService, locationService);
});

/// Provider for nearby AR experiences
final nearbyARExperiencesProvider =
    FutureProvider<List<Experience>>((ref) async {
  final arService = ref.watch(arExperienceServiceProvider);
  return arService.getNearbyARExperiences();
});

/// Provider for AR experience loading state
final arExperienceLoadingProvider = StateNotifierProvider<
    ARExperienceLoadingNotifier, ARExperienceLoadingState>((ref) {
  final arService = ref.watch(arExperienceServiceProvider);
  return ARExperienceLoadingNotifier(arService);
});

/// State for AR experience loading
class ARExperienceLoadingState {
  final bool isInitialized;
  final bool isLoading;
  final String? error;
  final double progress;
  final Experience? currentExperience;

  const ARExperienceLoadingState({
    this.isInitialized = false,
    this.isLoading = false,
    this.error,
    this.progress = 0.0,
    this.currentExperience,
  });

  ARExperienceLoadingState copyWith({
    bool? isInitialized,
    bool? isLoading,
    String? error,
    double? progress,
    Experience? currentExperience,
  }) {
    return ARExperienceLoadingState(
      isInitialized: isInitialized ?? this.isInitialized,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      progress: progress ?? this.progress,
      currentExperience: currentExperience ?? this.currentExperience,
    );
  }
}

/// Notifier for AR experience loading state
class ARExperienceLoadingNotifier
    extends StateNotifier<ARExperienceLoadingState> {
  final ARExperienceService _arService;

  ARExperienceLoadingNotifier(this._arService)
      : super(const ARExperienceLoadingState());

  /// Initialize AR
  Future<void> initialize() async {
    if (state.isInitialized) return;
    if (state.isLoading) return;

    try {
      state = state.copyWith(isLoading: true, error: null, progress: 0.1);

      // Initialize AR service
      await _arService.initialize();

      // Update progress
      state = state.copyWith(progress: 0.5);

      // Start AR
      await _arService.startAR();

      // Update state
      state = state.copyWith(
        isInitialized: true,
        isLoading: false,
        progress: 1.0,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
        progress: 0.0,
      );
    }
  }

  /// Load AR experience
  Future<void> loadExperience(Experience experience) async {
    if (!state.isInitialized) {
      await initialize();
    }

    if (!experience.hasARContent) {
      state = state.copyWith(
        error: 'This experience does not have AR content',
        currentExperience: null,
      );
      return;
    }

    try {
      state = state.copyWith(
        isLoading: true,
        error: null,
        progress: 0.1,
        currentExperience: experience,
      );

      // Simulate loading progress
      await Future.delayed(const Duration(milliseconds: 500));
      state = state.copyWith(progress: 0.3);

      // Get AR model
      final modelPath = await _arService.getARModelForExperience(experience);

      if (modelPath == null) {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to load AR model',
          progress: 0.0,
        );
        return;
      }

      // Update progress
      state = state.copyWith(progress: 0.7);

      // Simulate final loading
      await Future.delayed(const Duration(milliseconds: 500));

      // Update state
      state = state.copyWith(
        isLoading: false,
        progress: 1.0,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
        progress: 0.0,
      );
    }
  }

  /// Clear current experience
  void clearExperience() {
    state = state.copyWith(currentExperience: null, progress: 0.0);
  }

  /// Dispose AR resources
  @override
  void dispose() {
    _arService.dispose();
    state = const ARExperienceLoadingState();
    super.dispose();
  }
}
