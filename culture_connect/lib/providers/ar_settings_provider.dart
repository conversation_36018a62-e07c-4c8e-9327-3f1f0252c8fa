import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:culture_connect/providers/shared_preferences_provider.dart';

/// AR settings model
class ARSettings {
  /// Quality level (low, medium, high)
  final String quality;

  /// Whether to enable animations
  final bool enableAnimations;

  /// Whether to enable offline mode
  final bool enableOfflineMode;

  /// Whether to enable location tracking
  final bool enableLocationTracking;

  /// Whether to enable background downloads
  final bool enableBackgroundDownloads;

  /// Whether to enable haptic feedback
  final bool enableHapticFeedback;

  /// Maximum cache size in MB
  final double maxCacheSize;

  /// Maximum number of visible landmarks
  final int maxVisibleLandmarks;

  /// Render distance in meters
  final double renderDistance;

  /// Target frame rate
  final double targetFrameRate;

  /// Whether to enable frustum culling
  final bool enableFrustumCulling;

  /// Whether to enable level of detail
  final bool enableLOD;

  /// Theme (auto, light, dark)
  final String theme;

  /// Whether to show debug info
  final bool showDebugInfo;

  /// Whether to show performance metrics
  final bool showPerformanceMetrics;

  /// Creates a new AR settings instance
  const ARSettings({
    this.quality = 'medium',
    this.enableAnimations = true,
    this.enableOfflineMode = true,
    this.enableLocationTracking = true,
    this.enableBackgroundDownloads = true,
    this.enableHapticFeedback = true,
    this.maxCacheSize = 500,
    this.maxVisibleLandmarks = 10,
    this.renderDistance = 1000,
    this.targetFrameRate = 30,
    this.enableFrustumCulling = true,
    this.enableLOD = true,
    this.theme = 'auto',
    this.showDebugInfo = false,
    this.showPerformanceMetrics = false,
  });

  /// Creates a copy of this AR settings with the given fields replaced with the new values
  ARSettings copyWith({
    String? quality,
    bool? enableAnimations,
    bool? enableOfflineMode,
    bool? enableLocationTracking,
    bool? enableBackgroundDownloads,
    bool? enableHapticFeedback,
    double? maxCacheSize,
    int? maxVisibleLandmarks,
    double? renderDistance,
    double? targetFrameRate,
    bool? enableFrustumCulling,
    bool? enableLOD,
    String? theme,
    bool? showDebugInfo,
    bool? showPerformanceMetrics,
  }) {
    return ARSettings(
      quality: quality ?? this.quality,
      enableAnimations: enableAnimations ?? this.enableAnimations,
      enableOfflineMode: enableOfflineMode ?? this.enableOfflineMode,
      enableLocationTracking:
          enableLocationTracking ?? this.enableLocationTracking,
      enableBackgroundDownloads:
          enableBackgroundDownloads ?? this.enableBackgroundDownloads,
      enableHapticFeedback: enableHapticFeedback ?? this.enableHapticFeedback,
      maxCacheSize: maxCacheSize ?? this.maxCacheSize,
      maxVisibleLandmarks: maxVisibleLandmarks ?? this.maxVisibleLandmarks,
      renderDistance: renderDistance ?? this.renderDistance,
      targetFrameRate: targetFrameRate ?? this.targetFrameRate,
      enableFrustumCulling: enableFrustumCulling ?? this.enableFrustumCulling,
      enableLOD: enableLOD ?? this.enableLOD,
      theme: theme ?? this.theme,
      showDebugInfo: showDebugInfo ?? this.showDebugInfo,
      showPerformanceMetrics:
          showPerformanceMetrics ?? this.showPerformanceMetrics,
    );
  }
}

/// AR settings notifier
class ARSettingsNotifier extends StateNotifier<ARSettings> {
  final SharedPreferences _preferences;

  /// Creates a new AR settings notifier
  ARSettingsNotifier(this._preferences) : super(const ARSettings()) {
    _loadSettings();
  }

  /// Load settings from shared preferences
  void _loadSettings() {
    state = ARSettings(
      quality: _preferences.getString('ar_quality') ?? 'medium',
      enableAnimations: _preferences.getBool('ar_enable_animations') ?? true,
      enableOfflineMode: _preferences.getBool('ar_enable_offline_mode') ?? true,
      enableLocationTracking:
          _preferences.getBool('ar_enable_location_tracking') ?? true,
      enableBackgroundDownloads:
          _preferences.getBool('ar_enable_background_downloads') ?? true,
      enableHapticFeedback:
          _preferences.getBool('ar_enable_haptic_feedback') ?? true,
      maxCacheSize: _preferences.getDouble('ar_max_cache_size') ?? 500,
      maxVisibleLandmarks:
          _preferences.getInt('ar_max_visible_landmarks') ?? 10,
      renderDistance: _preferences.getDouble('ar_render_distance') ?? 1000,
      targetFrameRate: _preferences.getDouble('ar_target_frame_rate') ?? 30,
      enableFrustumCulling:
          _preferences.getBool('ar_enable_frustum_culling') ?? true,
      enableLOD: _preferences.getBool('ar_enable_lod') ?? true,
      theme: _preferences.getString('ar_theme') ?? 'auto',
      showDebugInfo: _preferences.getBool('ar_show_debug_info') ?? false,
      showPerformanceMetrics:
          _preferences.getBool('ar_show_performance_metrics') ?? false,
    );
  }

  /// Save settings to shared preferences
  Future<void> _saveSettings() async {
    await _preferences.setString('ar_quality', state.quality);
    await _preferences.setBool('ar_enable_animations', state.enableAnimations);
    await _preferences.setBool(
        'ar_enable_offline_mode', state.enableOfflineMode);
    await _preferences.setBool(
        'ar_enable_location_tracking', state.enableLocationTracking);
    await _preferences.setBool(
        'ar_enable_background_downloads', state.enableBackgroundDownloads);
    await _preferences.setBool(
        'ar_enable_haptic_feedback', state.enableHapticFeedback);
    await _preferences.setDouble('ar_max_cache_size', state.maxCacheSize);
    await _preferences.setInt(
        'ar_max_visible_landmarks', state.maxVisibleLandmarks);
    await _preferences.setDouble('ar_render_distance', state.renderDistance);
    await _preferences.setDouble('ar_target_frame_rate', state.targetFrameRate);
    await _preferences.setBool(
        'ar_enable_frustum_culling', state.enableFrustumCulling);
    await _preferences.setBool('ar_enable_lod', state.enableLOD);
    await _preferences.setString('ar_theme', state.theme);
    await _preferences.setBool('ar_show_debug_info', state.showDebugInfo);
    await _preferences.setBool(
        'ar_show_performance_metrics', state.showPerformanceMetrics);
  }

  /// Update settings
  Future<void> updateSettings({
    String? quality,
    bool? enableAnimations,
    bool? enableOfflineMode,
    bool? enableLocationTracking,
    bool? enableBackgroundDownloads,
    bool? enableHapticFeedback,
    double? maxCacheSize,
    int? maxVisibleLandmarks,
    double? renderDistance,
    double? targetFrameRate,
    bool? enableFrustumCulling,
    bool? enableLOD,
    String? theme,
    bool? showDebugInfo,
    bool? showPerformanceMetrics,
  }) async {
    state = state.copyWith(
      quality: quality,
      enableAnimations: enableAnimations,
      enableOfflineMode: enableOfflineMode,
      enableLocationTracking: enableLocationTracking,
      enableBackgroundDownloads: enableBackgroundDownloads,
      enableHapticFeedback: enableHapticFeedback,
      maxCacheSize: maxCacheSize,
      maxVisibleLandmarks: maxVisibleLandmarks,
      renderDistance: renderDistance,
      targetFrameRate: targetFrameRate,
      enableFrustumCulling: enableFrustumCulling,
      enableLOD: enableLOD,
      theme: theme,
      showDebugInfo: showDebugInfo,
      showPerformanceMetrics: showPerformanceMetrics,
    );

    await _saveSettings();
  }

  /// Reset settings to defaults
  Future<void> resetToDefaults() async {
    state = const ARSettings();
    await _saveSettings();
  }
}

/// Provider for AR settings
final arSettingsProvider =
    StateNotifierProvider<ARSettingsNotifier, ARSettings>((ref) {
  final preferences = ref.watch(sharedPreferencesProvider);
  return ARSettingsNotifier(preferences);
});
