import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/models/offline/bandwidth_usage.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/database/translation_database_helper.dart';

/// Provider for bandwidth settings
final bandwidthSettingsProvider = StateNotifierProvider<
    BandwidthSettingsNotifier, AsyncValue<BandwidthSettings>>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  return BandwidthSettingsNotifier(prefs);
});

/// Notifier for bandwidth settings
class BandwidthSettingsNotifier
    extends StateNotifier<AsyncValue<BandwidthSettings>> {
  /// The shared preferences instance
  final SharedPreferences _prefs;

  /// Creates a new bandwidth settings notifier
  BandwidthSettingsNotifier(this._prefs) : super(const AsyncValue.loading()) {
    _loadSettings();
  }

  /// Loads the settings from shared preferences
  Future<void> _loadSettings() async {
    try {
      final settingsJson = _prefs.getString('bandwidth_settings');
      if (settingsJson != null) {
        final settingsMap = jsonDecode(settingsJson) as Map<String, dynamic>;
        state = AsyncValue.data(BandwidthSettings.fromJson(settingsMap));
      } else {
        state = const AsyncValue.data(BandwidthSettings());
      }
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Updates the settings
  Future<void> update(BandwidthSettings settings) async {
    try {
      await _prefs.setString(
          'bandwidth_settings', jsonEncode(settings.toJson()));
      state = AsyncValue.data(settings);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
}

/// Provider for bandwidth usage
final bandwidthUsageProvider = FutureProvider<BandwidthUsage>((ref) async {
  final prefs = ref.watch(sharedPreferencesProvider);
  final usageJson = prefs.getString('bandwidth_usage');

  if (usageJson != null) {
    try {
      final usageMap = jsonDecode(usageJson) as Map<String, dynamic>;
      return BandwidthUsage.fromJson(usageMap);
    } catch (e) {
      // Return default usage if there's an error
      return BandwidthUsage(
        id: 'default',
        date: DateTime.now(),
        networkType: NetworkType.unknown,
        contentType: 'unknown',
        bytesDownloaded: 0,
        bytesUploaded: 0,
      );
    }
  } else {
    // Return default usage if none exists
    return BandwidthUsage(
      id: 'default',
      date: DateTime.now(),
      networkType: NetworkType.unknown,
      contentType: 'unknown',
      bytesDownloaded: 0,
      bytesUploaded: 0,
    );
  }
});

/// Provider for pending translations count
final pendingTranslationsProvider = FutureProvider<int>((ref) async {
  final dbHelper = ref.watch(translationDatabaseHelperProvider);
  final db = await dbHelper.database;

  final result = await db.rawQuery('''
    SELECT COUNT(*) as count
    FROM group_message_translations
    WHERE sync_status = 'pending'
  ''');

  return result.first['count'] as int? ?? 0;
});

/// Provider for the translation database helper
final translationDatabaseHelperProvider =
    Provider<TranslationDatabaseHelper>((ref) {
  return TranslationDatabaseHelper();
});
