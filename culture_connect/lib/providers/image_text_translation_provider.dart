import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:culture_connect/models/translation/image_text_translation_model.dart';
import 'package:culture_connect/services/image_text_translation_service.dart';

/// State for the image text translation provider
class ImageTextTranslationState {
  /// The current translation
  final ImageTextTranslationModel? currentTranslation;

  /// Whether the service is loading
  final bool isLoading;

  /// The error message
  final String? errorMessage;

  /// The translation history
  final List<ImageTextTranslationModel> translationHistory;

  /// Creates a new image text translation state
  const ImageTextTranslationState({
    this.currentTranslation,
    this.isLoading = false,
    this.errorMessage,
    this.translationHistory = const [],
  });

  /// Creates a copy of this state with the given fields replaced
  ImageTextTranslationState copyWith({
    ImageTextTranslationModel? currentTranslation,
    bool? isLoading,
    String? errorMessage,
    List<ImageTextTranslationModel>? translationHistory,
  }) {
    return ImageTextTranslationState(
      currentTranslation: currentTranslation ?? this.currentTranslation,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      translationHistory: translationHistory ?? this.translationHistory,
    );
  }
}

/// Notifier for the image text translation provider
class ImageTextTranslationNotifier
    extends StateNotifier<ImageTextTranslationState> {
  /// The image text translation service
  final ImageTextTranslationService _service;

  /// Creates a new image text translation notifier
  ImageTextTranslationNotifier(this._service)
      : super(ImageTextTranslationState(
          translationHistory: _service.translationHistory,
        ));

  /// Pick an image from the gallery and translate it
  Future<void> pickImageFromGallery(String targetLanguage) async {
    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        await _processImage(pickedFile.path, targetLanguage);
      } else {
        state = state.copyWith(isLoading: false);
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to pick image: $e',
      );
    }
  }

  /// Take a photo with the camera and translate it
  Future<void> takePhoto(String targetLanguage) async {
    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        await _processImage(pickedFile.path, targetLanguage);
      } else {
        state = state.copyWith(isLoading: false);
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to take photo: $e',
      );
    }
  }

  /// Process an image for translation
  Future<void> _processImage(String imagePath, String targetLanguage) async {
    try {
      // Copy the image to the app's documents directory
      final appDir = await getApplicationDocumentsDirectory();
      final fileName =
          'image_text_translation_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final targetPath = '${appDir.path}/image_translations/$fileName';

      // Create the directory if it doesn't exist
      final directory = Directory('${appDir.path}/image_translations');
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      // Copy the image
      await File(imagePath).copy(targetPath);

      // Translate the image
      final translation =
          await _service.translateImageText(targetPath, targetLanguage);

      // Update state
      state = state.copyWith(
        currentTranslation: translation,
        isLoading: false,
        translationHistory: _service.translationHistory,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to process image: $e',
      );
    }
  }

  /// Translate an existing image
  Future<void> translateImage(String imagePath, String targetLanguage) async {
    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      final translation =
          await _service.translateImageText(imagePath, targetLanguage);

      state = state.copyWith(
        currentTranslation: translation,
        isLoading: false,
        translationHistory: _service.translationHistory,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to translate image: $e',
      );
    }
  }

  /// Toggle favorite status of a translation
  Future<void> toggleFavorite(String id) async {
    try {
      final updatedTranslation = await _service.toggleFavorite(id);

      // Update current translation if it's the same one
      if (state.currentTranslation?.id == id) {
        state = state.copyWith(
          currentTranslation: updatedTranslation,
          translationHistory: _service.translationHistory,
        );
      } else {
        state = state.copyWith(
          translationHistory: _service.translationHistory,
        );
      }
    } catch (e) {
      state = state.copyWith(
        errorMessage: 'Failed to toggle favorite: $e',
      );
    }
  }

  /// Delete a translation
  Future<void> deleteTranslation(String id) async {
    try {
      await _service.deleteTranslation(id);

      // Clear current translation if it's the same one
      if (state.currentTranslation?.id == id) {
        state = state.copyWith(
          currentTranslation: null,
          translationHistory: _service.translationHistory,
        );
      } else {
        state = state.copyWith(
          translationHistory: _service.translationHistory,
        );
      }
    } catch (e) {
      state = state.copyWith(
        errorMessage: 'Failed to delete translation: $e',
      );
    }
  }

  /// Clear translation history
  Future<void> clearHistory() async {
    try {
      await _service.clearHistory();

      state = state.copyWith(
        currentTranslation: null,
        translationHistory: [],
      );
    } catch (e) {
      state = state.copyWith(
        errorMessage: 'Failed to clear history: $e',
      );
    }
  }

  /// Set whether to use offline mode
  Future<void> setUseOfflineMode(bool value) async {
    await _service.setUseOfflineMode(value);
  }

  /// Set whether to use custom vocabulary
  Future<void> setUseCustomVocabulary(bool value) async {
    await _service.setUseCustomVocabulary(value);
  }

  /// Load a translation from history
  void loadTranslation(String id) {
    final translation = state.translationHistory.firstWhere(
      (item) => item.id == id,
      orElse: () => throw Exception('Translation not found'),
    );

    state = state.copyWith(
      currentTranslation: translation,
      errorMessage: null,
    );
  }

  /// Clear the current translation
  void clearCurrentTranslation() {
    state = state.copyWith(
      currentTranslation: null,
      errorMessage: null,
    );
  }

  /// Clear the error message
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }
}

/// Provider for the image text translation state
final imageTextTranslationProvider = StateNotifierProvider<
    ImageTextTranslationNotifier, ImageTextTranslationState>((ref) {
  final service = ref.watch(imageTextTranslationServiceProvider);
  return ImageTextTranslationNotifier(service);
});
