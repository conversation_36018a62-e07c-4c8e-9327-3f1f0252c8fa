import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/loyalty/loyalty_points_transaction.dart';
import 'package:culture_connect/models/loyalty/loyalty_program_model.dart';
import 'package:culture_connect/models/loyalty/loyalty_reward.dart';
import 'package:culture_connect/services/loyalty_service.dart';

/// Provider for the loyalty program
final loyaltyProgramProvider = StreamProvider<LoyaltyProgramModel?>((ref) {
  return ref.watch(loyaltyServiceProvider).loyaltyProgramStream;
});

/// Provider for loyalty rewards
final loyaltyRewardsProvider = StreamProvider<List<LoyaltyReward>>((ref) {
  return ref.watch(loyaltyServiceProvider).loyaltyRewardsStream;
});

/// Provider for a specific loyalty reward
final loyaltyRewardProvider =
    Provider.family<LoyaltyReward?, String>((ref, id) {
  return ref.watch(loyaltyServiceProvider).getLoyaltyReward(id);
});

/// Provider for available loyalty rewards
final availableLoyaltyRewardsProvider = Provider<List<LoyaltyReward>>((ref) {
  final rewards = ref.watch(loyaltyRewardsProvider).value ?? [];
  return rewards
      .where((reward) => reward.status == LoyaltyRewardStatus.available)
      .toList();
});

/// Provider for redeemed loyalty rewards
final redeemedLoyaltyRewardsProvider = Provider<List<LoyaltyReward>>((ref) {
  final rewards = ref.watch(loyaltyRewardsProvider).value ?? [];
  return rewards
      .where((reward) => reward.status == LoyaltyRewardStatus.redeemed)
      .toList();
});

/// Provider for expired loyalty rewards
final expiredLoyaltyRewardsProvider = Provider<List<LoyaltyReward>>((ref) {
  final rewards = ref.watch(loyaltyRewardsProvider).value ?? [];
  return rewards
      .where((reward) => reward.status == LoyaltyRewardStatus.expired)
      .toList();
});

/// Provider for loyalty rewards by type
final loyaltyRewardsByTypeProvider =
    Provider.family<List<LoyaltyReward>, LoyaltyRewardType>((ref, type) {
  final rewards = ref.watch(loyaltyRewardsProvider).value ?? [];
  return rewards.where((reward) => reward.type == type).toList();
});

/// Provider for loyalty rewards by minimum tier
final loyaltyRewardsByTierProvider =
    Provider.family<List<LoyaltyReward>, LoyaltyTier>((ref, tier) {
  final rewards = ref.watch(loyaltyRewardsProvider).value ?? [];
  return rewards
      .where((reward) =>
          reward.minimumTier == null || reward.minimumTier!.index <= tier.index)
      .toList();
});

/// Provider for loyalty points transactions
final loyaltyPointsTransactionsProvider =
    Provider<List<LoyaltyPointsTransaction>>((ref) {
  final program = ref.watch(loyaltyProgramProvider).value;
  return program?.pointsHistory ?? [];
});

/// Provider for earning loyalty points transactions
final earningLoyaltyPointsTransactionsProvider =
    Provider<List<LoyaltyPointsTransaction>>((ref) {
  final transactions = ref.watch(loyaltyPointsTransactionsProvider);
  return transactions.where((transaction) => transaction.isEarning).toList();
});

/// Provider for spending loyalty points transactions
final spendingLoyaltyPointsTransactionsProvider =
    Provider<List<LoyaltyPointsTransaction>>((ref) {
  final transactions = ref.watch(loyaltyPointsTransactionsProvider);
  return transactions.where((transaction) => !transaction.isEarning).toList();
});

/// Provider for loyalty points transactions by type
final loyaltyPointsTransactionsByTypeProvider = Provider.family<
    List<LoyaltyPointsTransaction>, LoyaltyPointsTransactionType>((ref, type) {
  final transactions = ref.watch(loyaltyPointsTransactionsProvider);
  return transactions.where((transaction) => transaction.type == type).toList();
});

/// Provider for loyalty tier
final loyaltyTierProvider = Provider<LoyaltyTier?>((ref) {
  final program = ref.watch(loyaltyProgramProvider).value;
  return program?.tier;
});

/// Provider for next loyalty tier
final nextLoyaltyTierProvider = Provider<LoyaltyTier?>((ref) {
  final program = ref.watch(loyaltyProgramProvider).value;
  return program?.nextTier;
});

/// Provider for points to next tier
final pointsToNextTierProvider = Provider<int?>((ref) {
  final program = ref.watch(loyaltyProgramProvider).value;
  return program?.pointsToNextTier;
});

/// Provider for progress to next tier
final progressToNextTierProvider = Provider<double?>((ref) {
  final program = ref.watch(loyaltyProgramProvider).value;
  return program?.progressToNextTier;
});

/// Provider for loyalty tier benefits
final loyaltyTierBenefitsProvider =
    Provider.family<List<String>, LoyaltyTier>((ref, tier) {
  return tier.benefits;
});

/// Provider for current loyalty tier benefits
final currentLoyaltyTierBenefitsProvider = Provider<List<String>>((ref) {
  final tier = ref.watch(loyaltyTierProvider);
  return tier?.benefits ?? [];
});

/// Provider for next loyalty tier benefits
final nextLoyaltyTierBenefitsProvider = Provider<List<String>>((ref) {
  final nextTier = ref.watch(nextLoyaltyTierProvider);
  return nextTier?.benefits ?? [];
});

/// Provider for additional benefits in next tier
final additionalBenefitsInNextTierProvider = Provider<List<String>>((ref) {
  final currentBenefits = ref.watch(currentLoyaltyTierBenefitsProvider);
  final nextBenefits = ref.watch(nextLoyaltyTierBenefitsProvider);

  return nextBenefits
      .where((benefit) => !currentBenefits.contains(benefit))
      .toList();
});

/// Notifier for earning loyalty points
class EarnLoyaltyPointsNotifier
    extends StateNotifier<AsyncValue<LoyaltyProgramModel?>> {
  final LoyaltyService _loyaltyService;

  EarnLoyaltyPointsNotifier(this._loyaltyService)
      : super(const AsyncValue.data(null));

  /// Earn points for a booking
  Future<void> earnPointsForBooking({
    required String bookingId,
    required String bookingDescription,
    required double bookingAmount,
    required String currency,
  }) async {
    try {
      state = const AsyncValue.loading();

      final updatedProgram = await _loyaltyService.earnPointsForBooking(
        bookingId: bookingId,
        bookingDescription: bookingDescription,
        bookingAmount: bookingAmount,
        currency: currency,
      );

      state = AsyncValue.data(updatedProgram);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Reset the state
  void reset() {
    state = const AsyncValue.data(null);
  }
}

/// Provider for the earn loyalty points notifier
final earnLoyaltyPointsNotifierProvider = StateNotifierProvider<
    EarnLoyaltyPointsNotifier, AsyncValue<LoyaltyProgramModel?>>((ref) {
  final loyaltyService = ref.watch(loyaltyServiceProvider);
  return EarnLoyaltyPointsNotifier(loyaltyService);
});

/// Notifier for redeeming loyalty rewards
class RedeemLoyaltyRewardNotifier
    extends StateNotifier<AsyncValue<LoyaltyProgramModel?>> {
  final LoyaltyService _loyaltyService;

  RedeemLoyaltyRewardNotifier(this._loyaltyService)
      : super(const AsyncValue.data(null));

  /// Redeem a reward
  Future<void> redeemReward(String rewardId) async {
    try {
      state = const AsyncValue.loading();

      final updatedProgram = await _loyaltyService.redeemReward(rewardId);

      state = AsyncValue.data(updatedProgram);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Reset the state
  void reset() {
    state = const AsyncValue.data(null);
  }
}

/// Provider for the redeem loyalty reward notifier
final redeemLoyaltyRewardNotifierProvider = StateNotifierProvider<
    RedeemLoyaltyRewardNotifier, AsyncValue<LoyaltyProgramModel?>>((ref) {
  final loyaltyService = ref.watch(loyaltyServiceProvider);
  return RedeemLoyaltyRewardNotifier(loyaltyService);
});
