import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/car_rental_filter.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/travel/travel_services_provider.dart';

/// Provider for the car rental filter
final carRentalFilterProvider = StateProvider<CarRentalFilter>((ref) {
  return const CarRentalFilter();
});

/// Provider for filtered car rentals
final filteredCarRentalsProvider = Provider<AsyncValue<List<CarRental>>>((ref) {
  final carRentalsAsyncValue = ref.watch(carRentalsProvider);
  final filter = ref.watch(carRentalFilterProvider);

  return carRentalsAsyncValue.when(
    data: (carRentals) {
      final filtered = filter.apply(carRentals);
      return AsyncValue.data(filtered);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});
