import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/document/document_reminder.dart';
import 'package:culture_connect/models/travel/document/travel_document_base.dart';
import 'package:culture_connect/services/travel/document/document_reminder_service.dart';

/// Provider for document reminders
class DocumentReminderProvider extends ChangeNotifier {
  // Services
  final DocumentReminderService _reminderService = DocumentReminderService();

  // State
  List<DocumentReminder> _reminders = [];
  bool _isLoading = false;
  String? _error;

  /// Get all reminders
  List<DocumentReminder> get reminders => _reminders;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error
  String? get error => _error;

  /// Get due reminders
  List<DocumentReminder> get dueReminders =>
      _reminders.where((reminder) => reminder.isDue).toList();

  /// Get upcoming reminders
  List<DocumentReminder> get upcomingReminders =>
      _reminders.where((reminder) => reminder.isUpcoming).toList();

  /// Initialize the provider
  Future<void> initialize() async {
    await loadReminders();
  }

  /// Load all reminders
  Future<void> loadReminders() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _reminders = await _reminderService.getUserReminders();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to load reminders: $e';
      notifyListeners();
    }
  }

  /// Get reminders for a document
  Future<List<DocumentReminder>> getDocumentReminders(String documentId) async {
    try {
      return await _reminderService.getDocumentReminders(documentId);
    } catch (e) {
      _error = 'Failed to get document reminders: $e';
      notifyListeners();
      return [];
    }
  }

  /// Add a reminder
  Future<DocumentReminder?> addReminder(DocumentReminder reminder) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final newReminder = await _reminderService.addReminder(reminder);
      _reminders.add(newReminder);
      _isLoading = false;
      notifyListeners();
      return newReminder;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to add reminder: $e';
      notifyListeners();
      return null;
    }
  }

  /// Update a reminder
  Future<DocumentReminder?> updateReminder(DocumentReminder reminder) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final updatedReminder = await _reminderService.updateReminder(reminder);
      final index = _reminders.indexWhere((r) => r.id == reminder.id);
      if (index != -1) {
        _reminders[index] = updatedReminder;
      }
      _isLoading = false;
      notifyListeners();
      return updatedReminder;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to update reminder: $e';
      notifyListeners();
      return null;
    }
  }

  /// Delete a reminder
  Future<bool> deleteReminder(String id) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _reminderService.deleteReminder(id);
      _reminders.removeWhere((r) => r.id == id);
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to delete reminder: $e';
      notifyListeners();
      return false;
    }
  }

  /// Mark a reminder as read
  Future<DocumentReminder?> markReminderAsRead(String id) async {
    try {
      final updatedReminder = await _reminderService.markReminderAsRead(id);
      final index = _reminders.indexWhere((r) => r.id == id);
      if (index != -1) {
        _reminders[index] = updatedReminder;
        notifyListeners();
      }
      return updatedReminder;
    } catch (e) {
      _error = 'Failed to mark reminder as read: $e';
      notifyListeners();
      return null;
    }
  }

  /// Dismiss a reminder
  Future<DocumentReminder?> dismissReminder(String id) async {
    try {
      final updatedReminder = await _reminderService.dismissReminder(id);
      final index = _reminders.indexWhere((r) => r.id == id);
      if (index != -1) {
        _reminders[index] = updatedReminder;
        notifyListeners();
      }
      return updatedReminder;
    } catch (e) {
      _error = 'Failed to dismiss reminder: $e';
      notifyListeners();
      return null;
    }
  }

  /// Create reminders for a document
  Future<List<DocumentReminder>> createDocumentReminders(
      TravelDocument document) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final newReminders =
          await _reminderService.createDocumentReminders(document);
      _reminders.addAll(newReminders);
      _isLoading = false;
      notifyListeners();
      return newReminders;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to create document reminders: $e';
      notifyListeners();
      return [];
    }
  }

  /// Update reminders for a document
  Future<List<DocumentReminder>> updateDocumentReminders(
      TravelDocument document) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Remove old reminders for this document
      _reminders.removeWhere((r) => r.documentId == document.id);

      // Create new reminders
      final newReminders =
          await _reminderService.updateDocumentReminders(document);
      _reminders.addAll(newReminders);
      _isLoading = false;
      notifyListeners();
      return newReminders;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to update document reminders: $e';
      notifyListeners();
      return [];
    }
  }

  /// Delete reminders for a document
  Future<bool> deleteDocumentReminders(String documentId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _reminderService.deleteDocumentReminders(documentId);
      _reminders.removeWhere((r) => r.documentId == documentId);
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to delete document reminders: $e';
      notifyListeners();
      return false;
    }
  }
}
