import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/document/travel_documents.dart';
import 'package:culture_connect/services/travel/document/travel_documents.dart';

/// Provider for travel documents
class TravelDocumentProvider extends ChangeNotifier {
  // Services
  final TravelDocumentService _documentService = TravelDocumentService();

  // State
  List<TravelDocument> _documents = [];
  bool _isLoading = false;
  String? _error;

  /// Get all documents
  List<TravelDocument> get documents => _documents;

  /// Get passports
  List<Passport> get passports => _documents.whereType<Passport>().toList();

  /// Get visas
  List<Visa> get visas => _documents.whereType<Visa>().toList();

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error
  String? get error => _error;

  /// Get valid documents
  List<TravelDocument> get validDocuments =>
      _documents.where((doc) => doc.isValid).toList();

  /// Get expiring documents
  List<TravelDocument> get expiringDocuments =>
      _documents.where((doc) => doc.isExpiringSoon).toList();

  /// Get expired documents
  List<TravelDocument> get expiredDocuments =>
      _documents.where((doc) => doc.isExpired).toList();

  /// Initialize the provider
  Future<void> initialize() async {
    await loadDocuments();
  }

  /// Load documents
  Future<void> loadDocuments() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Use a default user ID for now, in a real app this would come from authentication
      const userId = 'user1';
      _documents = await _documentService.getDocuments(userId);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to load documents: $e';
      notifyListeners();
    }
  }

  /// Get a document by ID
  Future<TravelDocument?> getDocument(String id) async {
    try {
      return await _documentService.getDocument(id);
    } catch (e) {
      _error = 'Failed to get document: $e';
      notifyListeners();
      return null;
    }
  }

  /// Add a passport
  Future<Passport?> addPassport(Passport passport) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final newPassport = await _documentService.addPassport(passport);
      _documents.add(newPassport);
      _isLoading = false;
      notifyListeners();
      return newPassport;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to add passport: $e';
      notifyListeners();
      return null;
    }
  }

  /// Add a visa
  Future<Visa?> addVisa(Visa visa) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final newVisa = await _documentService.addVisa(visa);
      _documents.add(newVisa);
      _isLoading = false;
      notifyListeners();
      return newVisa;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to add visa: $e';
      notifyListeners();
      return null;
    }
  }

  /// Update a document
  Future<TravelDocument?> updateDocument(TravelDocument document) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      TravelDocument updatedDocument;

      // Call the appropriate update method based on document type
      if (document is Passport) {
        updatedDocument = await _documentService.updatePassport(document);
      } else if (document is Visa) {
        updatedDocument = await _documentService.updateVisa(document);
      } else {
        throw Exception('Unsupported document type');
      }

      final index = _documents.indexWhere((doc) => doc.id == document.id);
      if (index != -1) {
        _documents[index] = updatedDocument;
      }
      _isLoading = false;
      notifyListeners();
      return updatedDocument;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to update document: $e';
      notifyListeners();
      return null;
    }
  }

  /// Delete a document
  Future<bool> deleteDocument(String id) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _documentService.deleteDocument(id);
      _documents.removeWhere((doc) => doc.id == id);
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to delete document: $e';
      notifyListeners();
      return false;
    }
  }
}
