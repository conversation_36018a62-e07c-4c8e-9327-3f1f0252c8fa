import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/document/travel_documents.dart';
import 'package:culture_connect/services/travel/document/travel_documents.dart';
import 'package:culture_connect/providers/auth_provider.dart';

/// Provider for the travel document service
final travelDocumentServiceProvider = Provider<TravelDocumentService>((ref) {
  return TravelDocumentService();
});

/// Provider for the visa requirement service
final visaRequirementServiceProvider = Provider<VisaRequirementService>((ref) {
  return VisaRequirementService();
});

/// Provider for the document reminder service
final documentReminderServiceProvider =
    Provider<DocumentReminderService>((ref) {
  return DocumentReminderService();
});

/// Provider for all travel documents for the current user
final userTravelDocumentsProvider =
    FutureProvider<List<TravelDocument>>((ref) async {
  final service = ref.watch(travelDocumentServiceProvider);
  final userId = ref.watch(currentUserIdProvider);

  if (userId == null) {
    return [];
  }

  return service.getDocuments(userId);
});

/// Provider for all passports for the current user
final userPassportsProvider = FutureProvider<List<Passport>>((ref) async {
  final service = ref.watch(travelDocumentServiceProvider);
  final userId = ref.watch(currentUserIdProvider);

  if (userId == null) {
    return [];
  }

  return service.getPassports(userId);
});

/// Provider for all visas for the current user
final userVisasProvider = FutureProvider<List<Visa>>((ref) async {
  final service = ref.watch(travelDocumentServiceProvider);
  final userId = ref.watch(currentUserIdProvider);

  if (userId == null) {
    return [];
  }

  return service.getVisas(userId);
});

/// Provider for a specific travel document
final travelDocumentProvider =
    FutureProvider.family<TravelDocument?, String>((ref, id) async {
  final service = ref.watch(travelDocumentServiceProvider);
  return service.getDocument(id);
});

/// Provider for visa requirements between two countries
final visaRequirementProvider =
    FutureProvider.family<VisaRequirement?, (String, String)>(
        (ref, params) async {
  final service = ref.watch(visaRequirementServiceProvider);
  final (countryFrom, countryTo) = params;
  return service.getVisaRequirement(countryFrom, countryTo);
});

/// Provider for checking if a visa is required
final isVisaRequiredProvider =
    FutureProvider.family<bool, (String, String)>((ref, params) async {
  final service = ref.watch(visaRequirementServiceProvider);
  final (countryFrom, countryTo) = params;
  return service.isVisaRequired(countryFrom, countryTo);
});

/// Provider for all visa requirements for a country
final countryVisaRequirementsProvider =
    FutureProvider.family<List<VisaRequirement>, String>(
        (ref, countryFrom) async {
  final service = ref.watch(visaRequirementServiceProvider);
  return service.getVisaRequirementsForCountry(countryFrom);
});

/// Provider for all document reminders for the current user
final userDocumentRemindersProvider =
    FutureProvider<List<DocumentReminder>>((ref) async {
  final service = ref.watch(documentReminderServiceProvider);
  final userId = ref.watch(currentUserIdProvider);

  if (userId == null) {
    return [];
  }

  return service.getReminders(userId);
});

/// Provider for all reminders for a specific document
final documentRemindersProvider =
    FutureProvider.family<List<DocumentReminder>, String>(
        (ref, documentId) async {
  final service = ref.watch(documentReminderServiceProvider);
  return service.getRemindersForDocument(documentId);
});

/// Provider for a specific document reminder
final documentReminderProvider =
    FutureProvider.family<DocumentReminder?, String>((ref, id) async {
  final service = ref.watch(documentReminderServiceProvider);
  return service.getReminder(id);
});
