import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/itinerary.dart'
    as itinerary_model;
import 'package:culture_connect/models/travel/itinerary_item.dart'
    as itinerary_item_model;
import 'package:culture_connect/models/travel/ai_recommendation.dart';
import 'package:culture_connect/models/travel/travel_service_base.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/providers/preferences_provider.dart';
import 'package:culture_connect/providers/services_providers.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/services/travel/itinerary_service.dart';
import 'package:culture_connect/services/travel/ai_recommendation_service.dart';

// Type aliases to make the code more readable
typedef Itinerary = itinerary_model.Itinerary;
typedef ItineraryStatus = itinerary_model.ItineraryStatus;
typedef ItineraryDay = itinerary_model.ItineraryDay;
typedef ItineraryItem = itinerary_item_model.ItineraryItem;
typedef ItineraryItemType = itinerary_item_model.ItineraryItemType;

// Simple provider for the current user
final userProvider = Provider<UserModel?>((ref) {
  final userAsync = ref.watch(currentUserModelProvider);
  return userAsync.value;
});

/// Provider for the itinerary service
final itineraryServiceProvider = Provider<ItineraryService>((ref) {
  final loggingService = ref.watch(loggingServiceProvider);
  final sharedPreferences = ref.watch(sharedPreferencesProvider);
  return ItineraryService(loggingService, sharedPreferences);
});

/// Provider for the AI recommendation service
final aiRecommendationServiceProvider =
    Provider<AIRecommendationService>((ref) {
  final loggingService = ref.watch(loggingServiceProvider);
  return AIRecommendationService(loggingService);
});

/// Provider for all itineraries for the current user
final userItinerariesProvider = FutureProvider<List<Itinerary>>((ref) async {
  final user = ref.watch(userProvider);
  if (user == null) return [];

  final itineraryService = ref.watch(itineraryServiceProvider);
  return itineraryService.getItineraries(user.id);
});

/// Provider for a specific itinerary by ID
final itineraryProvider =
    FutureProvider.family<Itinerary?, String>((ref, id) async {
  final itineraryService = ref.watch(itineraryServiceProvider);
  return itineraryService.getItinerary(id);
});

/// Provider for the current itinerary being created or edited
final currentItineraryProvider =
    StateNotifierProvider<CurrentItineraryNotifier, AsyncValue<Itinerary?>>(
        (ref) {
  return CurrentItineraryNotifier(ref);
});

/// Notifier for the current itinerary
class CurrentItineraryNotifier extends StateNotifier<AsyncValue<Itinerary?>> {
  final Ref _ref;

  CurrentItineraryNotifier(this._ref) : super(const AsyncValue.loading());

  /// Load an itinerary by ID
  Future<void> loadItinerary(String id) async {
    state = const AsyncValue.loading();

    try {
      final itineraryService = _ref.read(itineraryServiceProvider);
      final itinerary = await itineraryService.getItinerary(id);
      state = AsyncValue.data(itinerary);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Create a new itinerary
  Future<void> createItinerary({
    required String title,
    required String destination,
    required DateTime startDate,
    required DateTime endDate,
    double? budgetAmount,
    String? budgetCurrency,
  }) async {
    state = const AsyncValue.loading();

    try {
      final user = _ref.read(userProvider);
      if (user == null) {
        throw Exception('User not logged in');
      }

      final itineraryService = _ref.read(itineraryServiceProvider);

      final itinerary = Itinerary(
        userId: user.id,
        title: title,
        destination: destination,
        startDate: startDate,
        endDate: endDate,
        budgetAmount: budgetAmount,
        budgetCurrency: budgetCurrency,
      );

      final createdItinerary =
          await itineraryService.createItinerary(itinerary);
      state = AsyncValue.data(createdItinerary);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Update the current itinerary
  Future<void> updateItinerary({
    String? title,
    String? destination,
    DateTime? startDate,
    DateTime? endDate,
    double? budgetAmount,
    String? budgetCurrency,
    ItineraryStatus? status,
    String? notes,
  }) async {
    final currentState = state;
    if (currentState is! AsyncData<Itinerary?> || currentState.value == null) {
      return;
    }

    final currentItinerary = currentState.value!;

    try {
      final itineraryService = _ref.read(itineraryServiceProvider);

      final updatedItinerary = currentItinerary.copyWith(
        title: title,
        destination: destination,
        startDate: startDate,
        endDate: endDate,
        budgetAmount: budgetAmount,
        budgetCurrency: budgetCurrency,
        status: status,
        notes: notes,
      );

      final savedItinerary =
          await itineraryService.updateItinerary(updatedItinerary);
      state = AsyncValue.data(savedItinerary);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Add an item to a day
  Future<void> addItemToDay(String dayId, ItineraryItem item) async {
    final currentState = state;
    if (currentState is! AsyncData<Itinerary?> || currentState.value == null) {
      return;
    }

    final currentItinerary = currentState.value!;

    try {
      final itineraryService = _ref.read(itineraryServiceProvider);

      // Convert ItineraryItem from itinerary_item.dart to itinerary.dart
      final convertedItem = _convertToItineraryModelItem(item);

      final updatedItinerary = await itineraryService.addItemToDay(
        currentItinerary.id,
        dayId,
        convertedItem,
      );

      state = AsyncValue.data(updatedItinerary);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Remove an item from a day
  Future<void> removeItemFromDay(String dayId, String itemId) async {
    final currentState = state;
    if (currentState is! AsyncData<Itinerary?> || currentState.value == null) {
      return;
    }

    final currentItinerary = currentState.value!;

    try {
      final itineraryService = _ref.read(itineraryServiceProvider);

      final updatedItinerary = await itineraryService.removeItemFromDay(
        currentItinerary.id,
        dayId,
        itemId,
      );

      state = AsyncValue.data(updatedItinerary);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Update an item in a day
  Future<void> updateItemInDay(String dayId, ItineraryItem item) async {
    final currentState = state;
    if (currentState is! AsyncData<Itinerary?> || currentState.value == null) {
      return;
    }

    final currentItinerary = currentState.value!;

    try {
      final itineraryService = _ref.read(itineraryServiceProvider);

      // Convert ItineraryItem from itinerary_item.dart to itinerary.dart
      final convertedItem = _convertToItineraryModelItem(item);

      final updatedItinerary = await itineraryService.updateItemInDay(
        currentItinerary.id,
        dayId,
        convertedItem,
      );

      state = AsyncValue.data(updatedItinerary);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Move an item to a different day
  Future<void> moveItemToDay(
      String sourceDayId, String targetDayId, String itemId) async {
    final currentState = state;
    if (currentState is! AsyncData<Itinerary?> || currentState.value == null) {
      return;
    }

    final currentItinerary = currentState.value!;

    try {
      final itineraryService = _ref.read(itineraryServiceProvider);

      final updatedItinerary = await itineraryService.moveItemToDay(
        currentItinerary.id,
        sourceDayId,
        targetDayId,
        itemId,
      );

      state = AsyncValue.data(updatedItinerary);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Clear the current itinerary
  void clearItinerary() {
    state = const AsyncValue.data(null);
  }

  /// Convert ItineraryItem from itinerary_item.dart to itinerary.dart
  itinerary_model.ItineraryItem _convertToItineraryModelItem(
      ItineraryItem item) {
    return itinerary_model.ItineraryItem(
      id: item.id,
      title: item.title,
      description: item.description,
      startTime: item.startTime,
      endTime: item.endTime,
      location: item.location,
      coordinates: item.coordinates != null
          ? {
              'latitude': item.coordinates!.latitude,
              'longitude': item.coordinates!.longitude,
            }
          : null,
      price: item.price,
      currency: item.currency,
      type: _convertItemType(item.type),
      status: _convertItemStatus(item.status),
      serviceId: item.serviceId,
      serviceType: _convertServiceType(item.serviceType),
      bookingId: item.bookingId,
      bookingReference: item.bookingReference,
    );
  }

  /// Convert ItineraryItemType from itinerary_item.dart to itinerary.dart
  itinerary_model.ItineraryItemType _convertItemType(ItineraryItemType type) {
    switch (type) {
      case ItineraryItemType.activity:
        return itinerary_model.ItineraryItemType.activity;
      case ItineraryItemType.food:
        return itinerary_model.ItineraryItemType.food;
      case ItineraryItemType.accommodation:
        return itinerary_model.ItineraryItemType.accommodation;
      case ItineraryItemType.transportation:
        return itinerary_model.ItineraryItemType.transportation;
      case ItineraryItemType.custom:
        return itinerary_model.ItineraryItemType.custom;
    }
  }

  /// Convert ItineraryItemStatus from itinerary_item.dart to itinerary.dart
  itinerary_model.ItineraryItemStatus _convertItemStatus(
      itinerary_item_model.ItineraryItemStatus status) {
    switch (status) {
      case itinerary_item_model.ItineraryItemStatus.planned:
        return itinerary_model.ItineraryItemStatus.planned;
      case itinerary_item_model.ItineraryItemStatus.booked:
        return itinerary_model.ItineraryItemStatus.booked;
      case itinerary_item_model.ItineraryItemStatus.completed:
        return itinerary_model.ItineraryItemStatus.completed;
      case itinerary_item_model.ItineraryItemStatus.cancelled:
        return itinerary_model.ItineraryItemStatus.cancelled;
    }
  }

  /// Convert TravelServiceType from travel_service_base.dart to itinerary.dart
  itinerary_model.TravelServiceType? _convertServiceType(
      TravelServiceType? serviceType) {
    if (serviceType == null) return null;

    switch (serviceType) {
      case TravelServiceType.hotel:
        return itinerary_model.TravelServiceType.hotel;
      case TravelServiceType.flight:
        return itinerary_model.TravelServiceType.flight;
      case TravelServiceType.carRental:
        return itinerary_model.TravelServiceType.carRental;
      case TravelServiceType.restaurant:
        return itinerary_model.TravelServiceType.restaurant;
      case TravelServiceType.cruise:
        return itinerary_model.TravelServiceType.cruise;
      case TravelServiceType.privateSecurity:
        return itinerary_model.TravelServiceType
            .other; // Map to other since privateSecurity doesn't exist in itinerary.dart
      case TravelServiceType.insurance:
        return itinerary_model.TravelServiceType
            .other; // Map to other since insurance doesn't exist in itinerary.dart
      case TravelServiceType.visa:
        return itinerary_model.TravelServiceType
            .other; // Map to other since visa doesn't exist in itinerary.dart
    }
  }
}

/// Provider for AI recommendations for the current itinerary
final aiRecommendationsProvider =
    FutureProvider.family<List<AIRecommendation>, Map<String, dynamic>>(
        (ref, params) async {
  final currentItineraryAsync = ref.watch(currentItineraryProvider);

  return currentItineraryAsync.when(
    data: (itinerary) async {
      if (itinerary == null) return [];

      final aiRecommendationService =
          ref.watch(aiRecommendationServiceProvider);
      final user = ref.watch(userProvider);
      if (user == null) return [];

      final itemTypes = params['itemTypes'] as List<ItineraryItemType>?;
      final limit = params['limit'] as int? ?? 10;

      return aiRecommendationService.generateRecommendations(
        userId: user.id,
        destination: itinerary.destination,
        startDate: itinerary.startDate,
        endDate: itinerary.endDate,
        preferences: _getUserPreferences(ref),
        budgetAmount: itinerary.budgetAmount,
        budgetCurrency: itinerary.budgetCurrency,
        itemTypes: itemTypes,
        limit: limit,
      );
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

/// Provider for AI recommendations for a specific day
final aiDayRecommendationsProvider =
    FutureProvider.family<List<AIRecommendation>, Map<String, dynamic>>(
        (ref, params) async {
  final currentItineraryAsync = ref.watch(currentItineraryProvider);

  return currentItineraryAsync.when(
    data: (itinerary) async {
      if (itinerary == null) return [];

      final dayId = params['dayId'] as String;
      final day = itinerary.days.firstWhere((d) => d.id == dayId);

      final aiRecommendationService =
          ref.watch(aiRecommendationServiceProvider);
      final user = ref.watch(userProvider);
      if (user == null) return [];

      final itemTypes = params['itemTypes'] as List<ItineraryItemType>?;
      final limit = params['limit'] as int? ?? 5;

      // For simplicity, we'll just pass an empty list for now
      // This is a temporary solution until we properly align the item types
      final convertedItems = <itinerary_item_model.ItineraryItem>[];

      return aiRecommendationService.generateDayRecommendations(
        userId: user.id,
        destination: itinerary.destination,
        date: day.date,
        preferences: _getUserPreferences(ref),
        existingItems: convertedItems,
        budgetAmount: itinerary.budgetAmount,
        budgetCurrency: itinerary.budgetCurrency,
        itemTypes: itemTypes,
        limit: limit,
      );
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

/// Get user preferences for AI recommendations
Map<String, dynamic> _getUserPreferences(Ref ref) {
  final preferences = ref.read(userPreferencesProvider);

  return {
    'languages': preferences.languagePreferences,
    'interests': preferences.culturalInterests,
    'themeMode': preferences.themeMode.name,
  };
}
