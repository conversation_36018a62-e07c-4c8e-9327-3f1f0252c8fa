import 'package:flutter_riverpod/flutter_riverpod.dart';

// Models
import 'package:culture_connect/models/travel/transfer/transfer_service.dart'
    as model;
import 'package:culture_connect/models/travel/transfer/transfer_booking.dart'
    as model;
import 'package:culture_connect/models/travel/transfer/transfer_location.dart'
    as model;
import 'package:culture_connect/models/travel/transfer/transfer_vehicle.dart'
    as model;
import 'package:culture_connect/models/travel/flight/flight_info.dart'
    as model_flight;

// Services
import 'package:culture_connect/services/travel/transfer/transfer_service.dart';
import 'package:culture_connect/services/travel/transfer/transfer_location_service.dart';
import 'package:culture_connect/services/travel/transfer/flight_integration_service.dart';

// Type aliases to make the code more readable
typedef TransferServiceModel = model.TransferService;
typedef TransferBookingModel = model.TransferBooking;
typedef TransferLocationType = model.TransferLocationType;
typedef TransferLocation = model.TransferLocation;
typedef TransferVehicleType = model.TransferVehicleType;
typedef FlightInfo = model_flight.FlightInfo;

/// Provider for the transfer service
final transferServiceProvider = Provider<TransferServiceManager>((ref) {
  return TransferServiceManager();
});

/// Provider for the transfer location service
final transferLocationServiceProvider =
    Provider<TransferLocationService>((ref) {
  return TransferLocationService();
});

/// Provider for the flight integration service
final flightIntegrationServiceProvider =
    Provider<FlightIntegrationService>((ref) {
  return FlightIntegrationService();
});

/// Provider for all available transfer services
final transfersProvider =
    FutureProvider<List<TransferServiceModel>>((ref) async {
  final service = ref.watch(transferServiceProvider);
  return service
      .getTransfers()
      .then((list) => list.cast<TransferServiceModel>());
});

/// Provider for a specific transfer service
final transferProvider =
    FutureProvider.family<TransferServiceModel?, String>((ref, id) async {
  final service = ref.watch(transferServiceProvider);
  return service.getTransfer(id).then((result) => result);
});

/// Provider for searching transfer services
final transferSearchProvider =
    FutureProvider.family<List<TransferServiceModel>, Map<String, dynamic>>(
        (ref, params) async {
  final service = ref.watch(transferServiceProvider);
  final results = await service.searchTransfers(
    location: params['location'] as String?,
    date: params['date'] as DateTime?,
    passengerCount: params['passengerCount'] as int?,
    luggageCount: params['luggageCount'] as int?,
    vehicleType: params['vehicleType'] as TransferVehicleType?,
    isPrivate: params['isPrivate'] as bool?,
    maxPrice: params['maxPrice'] as double?,
    sortBy: params['sortBy'] as String?,
    sortAscending: params['sortAscending'] as bool?,
  );
  return results.cast<TransferServiceModel>();
});

/// Provider for all bookings for the current user
final userTransferBookingsProvider =
    FutureProvider<List<TransferBookingModel>>((ref) async {
  final service = ref.watch(transferServiceProvider);
  // Use a dummy user ID for now
  const userId = 'user1';

  return service
      .getBookings(userId)
      .then((list) => list.cast<TransferBookingModel>());
});

/// Provider for a specific booking
final transferBookingProvider =
    FutureProvider.family<TransferBookingModel?, String>((ref, id) async {
  final service = ref.watch(transferServiceProvider);
  return service.getBooking(id);
});

/// Provider for calculating the price of a transfer
final transferPriceProvider =
    FutureProvider.family<double, Map<String, dynamic>>((ref, params) async {
  final service = ref.watch(transferServiceProvider);
  return service.calculatePrice(
    params['transferId'] as String,
    params['pickupLocation'] as TransferLocation,
    params['dropoffLocation'] as TransferLocation,
    params['pickupDateTime'] as DateTime,
    params['passengerCount'] as int,
    params['luggageCount'] as int,
  );
});

/// Provider for all transfer locations
final transferLocationsProvider =
    FutureProvider<List<TransferLocation>>((ref) async {
  final service = ref.watch(transferLocationServiceProvider);
  // Use the existing methods in the service
  final commonLocations = await service.getCommonLocations();
  final savedLocations = await service.getSavedLocations();
  return [...commonLocations, ...savedLocations].cast<TransferLocation>();
});

/// Provider for a specific transfer location
final transferLocationProvider =
    FutureProvider.family<TransferLocation?, String>((ref, id) async {
  final service = ref.watch(transferLocationServiceProvider);
  return service.getLocation(id);
});

/// Provider for transfer locations by type
final transferLocationsByTypeProvider =
    FutureProvider.family<List<TransferLocation>, TransferLocationType>(
        (ref, type) async {
  final service = ref.watch(transferLocationServiceProvider);
  // Use the existing methods in the service
  final commonLocations = await service.getCommonLocations();
  final savedLocations = await service.getSavedLocations();
  return [...commonLocations, ...savedLocations]
      .where((location) => location.type == type)
      .toList()
      .cast<TransferLocation>();
});

/// Provider for searching transfer locations
final transferLocationSearchProvider =
    FutureProvider.family<List<TransferLocation>, String>((ref, query) async {
  final service = ref.watch(transferLocationServiceProvider);
  return service.searchLocations(query);
});

/// Provider for flight information
final flightInfoProvider =
    FutureProvider.family<FlightInfo?, (String, DateTime)>((ref, params) async {
  final service = ref.watch(flightIntegrationServiceProvider);
  final (flightNumber, date) = params;
  final result = await service.getFlightInfo(flightNumber, date);
  // Convert from service FlightInfo to model FlightInfo if needed
  return result != null ? FlightInfo.fromJson(result.toJson()) : null;
});

/// Provider for tracking a flight
final flightTrackingProvider =
    FutureProvider.family<FlightInfo?, (String, DateTime)>((ref, params) async {
  final service = ref.watch(flightIntegrationServiceProvider);
  final (flightNumber, date) = params;
  final result = await service.trackFlight(flightNumber, date);
  // Convert from service FlightInfo to model FlightInfo if needed
  return result != null ? FlightInfo.fromJson(result.toJson()) : null;
});

/// Provider for searching flights
final flightSearchProvider =
    FutureProvider.family<List<FlightInfo>, Map<String, dynamic>>(
        (ref, params) async {
  final service = ref.watch(flightIntegrationServiceProvider);
  final results = await service.searchFlights(
    departureAirportCode: params['departureAirportCode'] as String,
    arrivalAirportCode: params['arrivalAirportCode'] as String,
    date: params['date'] as DateTime,
    airlineCode: params['airlineCode'] as String?,
  );
  // Convert from service FlightInfo to model FlightInfo
  return results.map((result) => FlightInfo.fromJson(result.toJson())).toList();
});
