import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/translation/language_pack_model.dart';
import 'package:culture_connect/models/translation/custom_vocabulary_model.dart';
import 'package:culture_connect/models/translation/conversation_model.dart';
import 'package:culture_connect/services/voice_translation/language_pack_service.dart';
import 'package:culture_connect/services/voice_translation/custom_vocabulary_service.dart';
import 'package:culture_connect/services/voice_translation/conversation_service.dart';
import 'package:culture_connect/services/voice_translation/voice_translation_service.dart';

/// Provider for language packs
final languagePacksProvider = StreamProvider<List<LanguagePackModel>>((ref) {
  final service = ref.watch(languagePackServiceProvider);
  return service.languagePacksStream;
});

/// Provider for a specific language pack
final languagePackProvider =
    Provider.family<LanguagePackModel?, String>((ref, languageCode) {
  final service = ref.watch(languagePackServiceProvider);
  return service.getLanguagePack(languageCode);
});

/// Provider for the primary language pack
final primaryLanguagePackProvider = Provider<LanguagePackModel?>((ref) {
  final service = ref.watch(languagePackServiceProvider);
  return service.getPrimaryLanguagePack();
});

/// Provider for downloaded language packs
final downloadedLanguagePacksProvider =
    Provider<List<LanguagePackModel>>((ref) {
  final languagePacks = ref.watch(languagePacksProvider).value ?? [];
  return languagePacks
      .where((pack) => pack.status == LanguagePackStatus.downloaded)
      .toList();
});

/// Provider for downloading language packs
final downloadLanguagePackProvider =
    FutureProvider.family<LanguagePackModel?, String>(
        (ref, languageCode) async {
  final service = ref.watch(languagePackServiceProvider);
  return service.downloadLanguagePack(languageCode);
});

/// Provider for the download progress of a language pack
final languagePackDownloadProgressProvider =
    StreamProvider.family<double, String>((ref, languageCode) {
  final service = ref.watch(languagePackServiceProvider);
  return service.getDownloadProgressStream(languageCode) ?? Stream.value(0.0);
});

/// Provider for custom vocabulary terms
final customVocabularyTermsProvider =
    StreamProvider<List<CustomVocabularyModel>>((ref) {
  final service = ref.watch(customVocabularyServiceProvider);
  return service.vocabularyTermsStream;
});

/// Provider for a specific custom vocabulary term
final customVocabularyTermProvider =
    Provider.family<CustomVocabularyModel?, String>((ref, id) {
  final service = ref.watch(customVocabularyServiceProvider);
  return service.getVocabularyTerm(id);
});

/// Provider for custom vocabulary terms by category
final customVocabularyTermsByCategoryProvider =
    Provider.family<List<CustomVocabularyModel>, VocabularyCategory>(
        (ref, category) {
  final service = ref.watch(customVocabularyServiceProvider);
  return service.getVocabularyTermsByCategory(category);
});

/// Provider for custom vocabulary terms by language
final customVocabularyTermsByLanguageProvider =
    Provider.family<List<CustomVocabularyModel>, String>((ref, languageCode) {
  final service = ref.watch(customVocabularyServiceProvider);
  return service.getVocabularyTermsByLanguage(languageCode);
});

/// Provider for favorite custom vocabulary terms
final favoriteCustomVocabularyTermsProvider =
    Provider<List<CustomVocabularyModel>>((ref) {
  final service = ref.watch(customVocabularyServiceProvider);
  return service.getFavoriteVocabularyTerms();
});

/// Provider for most used custom vocabulary terms
final mostUsedCustomVocabularyTermsProvider =
    Provider<List<CustomVocabularyModel>>((ref) {
  final service = ref.watch(customVocabularyServiceProvider);
  return service.getMostUsedVocabularyTerms();
});

/// Provider for recently added custom vocabulary terms
final recentlyAddedCustomVocabularyTermsProvider =
    Provider<List<CustomVocabularyModel>>((ref) {
  final service = ref.watch(customVocabularyServiceProvider);
  return service.getRecentlyAddedVocabularyTerms();
});

/// Provider for conversations
final conversationsProvider = StreamProvider<List<ConversationModel>>((ref) {
  final service = ref.watch(conversationServiceProvider);
  return service.conversationsStream;
});

/// Provider for the current conversation
final currentConversationProvider = StreamProvider<ConversationModel?>((ref) {
  final service = ref.watch(conversationServiceProvider);
  return service.currentConversationStream;
});

/// Provider for a specific conversation
final conversationProvider =
    Provider.family<ConversationModel?, String>((ref, id) {
  final service = ref.watch(conversationServiceProvider);
  return service.getConversation(id);
});

/// Provider for active conversations
final activeConversationsProvider = Provider<List<ConversationModel>>((ref) {
  final service = ref.watch(conversationServiceProvider);
  return service.getActiveConversations();
});

/// Provider for favorite conversations
final favoriteConversationsProvider = Provider<List<ConversationModel>>((ref) {
  final service = ref.watch(conversationServiceProvider);
  return service.getFavoriteConversations();
});

/// Provider for offline mode setting
final useOfflineModeProvider = StateProvider<bool>((ref) {
  final service = ref.watch(voiceTranslationServiceProvider);
  return service.useOfflineMode;
});

/// Provider for dialect recognition setting
final useDialectRecognitionProvider = StateProvider<bool>((ref) {
  final service = ref.watch(voiceTranslationServiceProvider);
  return service.useDialectRecognition;
});

/// Provider for custom vocabulary setting
final useCustomVocabularyProvider = StateProvider<bool>((ref) {
  final service = ref.watch(voiceTranslationServiceProvider);
  return service.useCustomVocabulary;
});

/// Notifier for custom vocabulary actions
class CustomVocabularyNotifier
    extends StateNotifier<AsyncValue<CustomVocabularyModel?>> {
  final CustomVocabularyService _service;

  CustomVocabularyNotifier(this._service) : super(const AsyncValue.data(null));

  /// Add a new vocabulary term
  Future<void> addVocabularyTerm({
    required String originalTerm,
    required String originalLanguageCode,
    required Map<String, String> translations,
    required VocabularyCategory category,
    String? customCategory,
    String? description,
  }) async {
    state = const AsyncValue.loading();

    try {
      final term = await _service.addVocabularyTerm(
        originalTerm: originalTerm,
        originalLanguageCode: originalLanguageCode,
        translations: translations,
        category: category,
        customCategory: customCategory,
        description: description,
      );

      state = AsyncValue.data(term);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Update a vocabulary term
  Future<void> updateVocabularyTerm({
    required String id,
    String? originalTerm,
    String? originalLanguageCode,
    Map<String, String>? translations,
    VocabularyCategory? category,
    String? customCategory,
    String? description,
  }) async {
    state = const AsyncValue.loading();

    try {
      final term = await _service.updateVocabularyTerm(
        id: id,
        originalTerm: originalTerm,
        originalLanguageCode: originalLanguageCode,
        translations: translations,
        category: category,
        customCategory: customCategory,
        description: description,
      );

      state = AsyncValue.data(term);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Delete a vocabulary term
  Future<void> deleteVocabularyTerm(String id) async {
    state = const AsyncValue.loading();

    try {
      final success = await _service.deleteVocabularyTerm(id);

      if (success) {
        state = const AsyncValue.data(null);
      } else {
        state = AsyncValue.error(
            'Failed to delete vocabulary term', StackTrace.current);
      }
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Toggle favorite status
  Future<void> toggleFavorite(String id) async {
    try {
      final term = await _service.toggleFavorite(id);
      state = AsyncValue.data(term);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Reset the state
  void reset() {
    state = const AsyncValue.data(null);
  }
}

/// Provider for the custom vocabulary notifier
final customVocabularyNotifierProvider = StateNotifierProvider<
    CustomVocabularyNotifier, AsyncValue<CustomVocabularyModel?>>((ref) {
  final service = ref.watch(customVocabularyServiceProvider);
  return CustomVocabularyNotifier(service);
});

/// Notifier for conversation actions
class ConversationNotifier
    extends StateNotifier<AsyncValue<ConversationModel?>> {
  final ConversationService _service;

  ConversationNotifier(this._service) : super(const AsyncValue.data(null));

  /// Start a new conversation
  Future<void> startConversation({
    required String userLanguageCode,
    required String otherLanguageCode,
    String? title,
    bool autoDetectLanguage = false,
    bool speakerIdentification = false,
  }) async {
    state = const AsyncValue.loading();

    try {
      final conversation = await _service.startConversation(
        userLanguageCode: userLanguageCode,
        otherLanguageCode: otherLanguageCode,
        title: title,
        autoDetectLanguage: autoDetectLanguage,
        speakerIdentification: speakerIdentification,
      );

      state = AsyncValue.data(conversation);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Add a turn to the conversation
  Future<void> addConversationTurn({
    required String conversationId,
    required ConversationRole role,
    required String audioPath,
    required String sourceLanguageCode,
    required String targetLanguageCode,
  }) async {
    state = const AsyncValue.loading();

    try {
      final turn = await _service.addConversationTurn(
        conversationId: conversationId,
        role: role,
        audioPath: audioPath,
        sourceLanguageCode: sourceLanguageCode,
        targetLanguageCode: targetLanguageCode,
      );

      if (turn != null) {
        final conversation = _service.getConversation(conversationId);
        state = AsyncValue.data(conversation);
      } else {
        state = AsyncValue.error(
            'Failed to add conversation turn', StackTrace.current);
      }
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// End a conversation
  Future<void> endConversation(String conversationId) async {
    try {
      final conversation = await _service.endConversation(conversationId);
      state = AsyncValue.data(conversation);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Pause a conversation
  Future<void> pauseConversation(String conversationId) async {
    try {
      final conversation = await _service.pauseConversation(conversationId);
      state = AsyncValue.data(conversation);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Resume a conversation
  Future<void> resumeConversation(String conversationId) async {
    try {
      final conversation = await _service.resumeConversation(conversationId);
      state = AsyncValue.data(conversation);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Delete a conversation
  Future<void> deleteConversation(String conversationId) async {
    try {
      final success = await _service.deleteConversation(conversationId);

      if (success) {
        state = const AsyncValue.data(null);
      } else {
        state = AsyncValue.error(
            'Failed to delete conversation', StackTrace.current);
      }
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Toggle favorite status
  Future<void> toggleFavorite(String conversationId) async {
    try {
      final conversation = await _service.toggleFavorite(conversationId);
      state = AsyncValue.data(conversation);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Update conversation settings
  Future<void> updateConversationSettings({
    required String conversationId,
    String? title,
    String? userLanguageCode,
    String? otherLanguageCode,
    bool? autoDetectLanguage,
    bool? speakerIdentification,
  }) async {
    try {
      final conversation = await _service.updateConversationSettings(
        conversationId: conversationId,
        title: title,
        userLanguageCode: userLanguageCode,
        otherLanguageCode: otherLanguageCode,
        autoDetectLanguage: autoDetectLanguage,
        speakerIdentification: speakerIdentification,
      );

      state = AsyncValue.data(conversation);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Reset the state
  void reset() {
    state = const AsyncValue.data(null);
  }
}

/// Provider for the conversation notifier
final conversationNotifierProvider =
    StateNotifierProvider<ConversationNotifier, AsyncValue<ConversationModel?>>(
        (ref) {
  final service = ref.watch(conversationServiceProvider);
  return ConversationNotifier(service);
});
