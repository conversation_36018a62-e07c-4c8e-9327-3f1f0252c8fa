// Flutter imports
import 'package:flutter/material.dart';

// Third-party imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/models/performance/performance_metric.dart';
import 'package:culture_connect/services/performance/performance_metrics_service.dart';
import 'package:culture_connect/widgets/performance/performance_chart_widget.dart';
import 'package:culture_connect/widgets/analytics/travel_stats_widget.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/custom_button.dart';

/// Screen for displaying comprehensive performance analytics and monitoring
class PerformanceDashboardScreen extends ConsumerStatefulWidget {
  /// Creates a new performance dashboard screen
  const PerformanceDashboardScreen({super.key});

  @override
  ConsumerState<PerformanceDashboardScreen> createState() =>
      _PerformanceDashboardScreenState();
}

class _PerformanceDashboardScreenState
    extends ConsumerState<PerformanceDashboardScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  List<PerformanceMetric> _allMetrics = [];
  bool _isLoading = true;
  String? _error;

  // Filter options
  String _selectedTimeRange = 'Last 24 Hours';
  final List<String> _timeRanges = [
    'Last Hour',
    'Last 24 Hours',
    'Last 7 Days',
    'Last 30 Days',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _fadeController = AnimationController(
      duration: AppTheme.mediumAnimation,
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _loadPerformanceData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  Future<void> _loadPerformanceData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final performanceService = ref.read(performanceMetricsServiceProvider);
      final metrics = performanceService.getCachedMetrics();

      if (mounted) {
        setState(() {
          _allMetrics = metrics;
          _isLoading = false;
        });
        _fadeController.forward();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'Failed to load performance data: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Performance Dashboard',
        showBackButton: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPerformanceData,
            tooltip: 'Refresh Data',
          ),
        ],
      ),
      body: _isLoading
          ? _buildLoadingState()
          : _error != null
              ? _buildErrorState(theme)
              : _buildDashboard(theme),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: AppTheme.spacingMedium),
          Text('Loading performance data...'),
        ],
      ),
    );
  }

  Widget _buildErrorState(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingLarge),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            Text(
              'Error Loading Data',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.error,
              ),
            ),
            const SizedBox(height: AppTheme.spacingSmall),
            Text(
              _error!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingLarge),
            CustomButton(
              text: 'Retry',
              onPressed: _loadPerformanceData,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDashboard(ThemeData theme) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Column(
        children: [
          _buildTimeRangeSelector(theme),
          _buildOverviewCards(theme),
          Expanded(
            child: _buildTabView(theme),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeRangeSelector(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withAlpha(51),
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            'Time Range:',
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: AppTheme.spacingMedium),
          Expanded(
            child: DropdownButton<String>(
              value: _selectedTimeRange,
              isExpanded: true,
              underline: const SizedBox.shrink(),
              items: _timeRanges.map((range) {
                return DropdownMenuItem<String>(
                  value: range,
                  child: Text(range),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedTimeRange = value;
                  });
                  _loadPerformanceData();
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewCards(ThemeData theme) {
    final startupMetrics =
        _allMetrics.where((m) => m.type == MetricType.startup).toList();
    final apiMetrics =
        _allMetrics.where((m) => m.type == MetricType.api).toList();
    final memoryMetrics =
        _allMetrics.where((m) => m.type == MetricType.memory).toList();
    final batteryMetrics =
        _allMetrics.where((m) => m.type == MetricType.battery).toList();

    final avgStartupTime = startupMetrics.isNotEmpty
        ? startupMetrics.map((m) => m.value).reduce((a, b) => a + b) /
            startupMetrics.length
        : 0.0;

    final avgApiTime = apiMetrics.isNotEmpty
        ? apiMetrics.map((m) => m.value).reduce((a, b) => a + b) /
            apiMetrics.length
        : 0.0;

    final avgMemoryUsage = memoryMetrics.isNotEmpty
        ? memoryMetrics.map((m) => m.value).reduce((a, b) => a + b) /
            memoryMetrics.length
        : 0.0;

    final avgBatteryUsage = batteryMetrics.isNotEmpty
        ? batteryMetrics.map((m) => m.value).reduce((a, b) => a + b) /
            batteryMetrics.length
        : 0.0;

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      child: Row(
        children: [
          Expanded(
            child: TravelStatsCard(
              title: 'Startup Time',
              value: '${avgStartupTime.toStringAsFixed(0)}ms',
              icon: Icons.rocket_launch,
              color: theme.colorScheme.primary,
              subtitle: 'Average',
              showTrend: startupMetrics.length > 1,
              isPositiveTrend: false, // Lower is better for startup time
              onTap: () => _tabController.animateTo(0),
            ),
          ),
          const SizedBox(width: AppTheme.spacingSmall),
          Expanded(
            child: TravelStatsCard(
              title: 'API Response',
              value: '${avgApiTime.toStringAsFixed(0)}ms',
              icon: Icons.api,
              color: theme.colorScheme.secondary,
              subtitle: 'Average',
              showTrend: apiMetrics.length > 1,
              isPositiveTrend: false, // Lower is better for API time
              onTap: () => _tabController.animateTo(2),
            ),
          ),
          const SizedBox(width: AppTheme.spacingSmall),
          Expanded(
            child: TravelStatsCard(
              title: 'Memory',
              value: '${avgMemoryUsage.toStringAsFixed(0)}MB',
              icon: Icons.memory,
              color: Colors.orange,
              subtitle: 'Average',
              showTrend: memoryMetrics.length > 1,
              isPositiveTrend: false, // Lower is better for memory
              onTap: () => _tabController.animateTo(3),
            ),
          ),
          const SizedBox(width: AppTheme.spacingSmall),
          Expanded(
            child: TravelStatsCard(
              title: 'Battery',
              value: '${avgBatteryUsage.toStringAsFixed(1)}%/h',
              icon: Icons.battery_std,
              color: Colors.green,
              subtitle: 'Usage rate',
              showTrend: batteryMetrics.length > 1,
              isPositiveTrend: false, // Lower is better for battery usage
              onTap: () => _tabController.animateTo(4),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabView(ThemeData theme) {
    return Column(
      children: [
        TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'Startup', icon: Icon(Icons.rocket_launch, size: 16)),
            Tab(text: 'Navigation', icon: Icon(Icons.navigation, size: 16)),
            Tab(text: 'API', icon: Icon(Icons.api, size: 16)),
            Tab(text: 'Memory', icon: Icon(Icons.memory, size: 16)),
            Tab(text: 'Battery', icon: Icon(Icons.battery_std, size: 16)),
          ],
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildMetricTab(MetricType.startup),
              _buildMetricTab(MetricType.transition),
              _buildMetricTab(MetricType.api),
              _buildMetricTab(MetricType.memory),
              _buildMetricTab(MetricType.battery),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMetricTab(MetricType metricType) {
    final metrics = _allMetrics.where((m) => m.type == metricType).toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      child: Column(
        children: [
          PerformanceChartWidget(
            metrics: metrics,
            chartType: PerformanceChartType.line,
            title: '${metricType.displayName} Trends',
            onMetricTapped: _showMetricDetails,
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          PerformanceChartWidget(
            metrics: metrics,
            chartType: PerformanceChartType.bar,
            title: '${metricType.displayName} Distribution',
            showLegend: false,
            onMetricTapped: _showMetricDetails,
          ),
          if (metrics.isNotEmpty) ...[
            const SizedBox(height: AppTheme.spacingMedium),
            PerformanceChartWidget(
              metrics: [metrics.last],
              chartType: PerformanceChartType.gauge,
              title: 'Current ${metricType.displayName}',
              showLegend: false,
              onMetricTapped: _showMetricDetails,
            ),
          ],
        ],
      ),
    );
  }

  void _showMetricDetails(PerformanceMetric metric) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(metric.type.displayName),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Value: ${metric.value.toStringAsFixed(2)} ${metric.unit}'),
            const SizedBox(height: AppTheme.spacingSmall),
            Text('Timestamp: ${metric.timestamp.toString()}'),
            const SizedBox(height: AppTheme.spacingSmall),
            Text('Session: ${metric.sessionId}'),
            if (metric.metadata.isNotEmpty) ...[
              const SizedBox(height: AppTheme.spacingSmall),
              const Text('Metadata:'),
              ...metric.metadata.entries.map(
                (entry) => Text('  ${entry.key}: ${entry.value}'),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
