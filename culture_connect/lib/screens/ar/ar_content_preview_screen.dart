import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/models/ar/ar_content_marker.dart';
import 'package:culture_connect/providers/ar/ar_content_providers.dart';
import 'package:culture_connect/providers/ar_experience_provider.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/ar/ar_controls.dart';
import 'package:culture_connect/widgets/ar/ar_info_overlay.dart';

/// A screen for previewing AR content
class ARContentPreviewScreen extends ConsumerStatefulWidget {
  /// The AR content ID
  final String arContentId;

  /// Creates a new AR content preview screen
  const ARContentPreviewScreen({
    super.key,
    required this.arContentId,
  });

  @override
  ConsumerState<ARContentPreviewScreen> createState() =>
      _ARContentPreviewScreenState();
}

class _ARContentPreviewScreenState extends ConsumerState<ARContentPreviewScreen>
    with SingleTickerProviderStateMixin {
  /// The animation controller
  late AnimationController _animationController;

  /// The rotation animation
  late Animation<double> _rotationAnimation;

  /// Whether the AR content is loading
  bool _isLoading = true;

  /// Whether to show the info overlay
  bool _showInfoOverlay = true;

  /// The current scale
  double _scale = 1.0;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 10000),
    );

    // Create rotation animation
    _rotationAnimation = Tween<double>(begin: 0, end: 360).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.linear),
    );

    // Start the animation
    _animationController.repeat();

    // Load AR content
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(currentARContentMarkerProvider.notifier)
          .loadARContentMarker(widget.arContentId);

      // Initialize AR
      _initializeAR();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();

    // Dispose AR resources
    ref.read(arExperienceLoadingProvider.notifier).dispose();

    super.dispose();
  }

  /// Initialize AR
  Future<void> _initializeAR() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(arExperienceLoadingProvider.notifier).initialize();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Build the AR content preview
  Widget _buildARContentPreview(ARContentMarker marker) {
    // This is a simplified implementation
    // In a real app, this would use ARCore/ARKit or a similar framework

    return GestureDetector(
      onScaleStart: (details) {
        // Store the initial scale
      },
      onScaleUpdate: (details) {
        setState(() {
          _scale = details.scale.clamp(0.5, 3.0);
        });
      },
      child: Center(
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform(
              alignment: Alignment.center,
              transform: Matrix4.identity()
                ..setEntry(3, 2, 0.001) // perspective
                ..rotateY((_rotationAnimation.value * 2 * 3.14159))
                ..scale(_scale),
              child: child,
            );
          },
          child: Container(
            width: 200,
            height: 200,
            decoration: const BoxDecoration(
              color: Color.fromRGBO(100, 100, 255, 0.1),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(
                marker.contentType.icon,
                size: 100,
                color: marker.contentType.color,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Build the AR controls
  Widget _buildARControls(ARContentMarker marker) {
    return ARControls(
      onInfoToggle: () {
        setState(() {
          _showInfoOverlay = !_showInfoOverlay;
        });
      },
      onShare: () {
        // Share AR content
      },
      onDownload: () {
        if (marker.isAvailableOffline) {
          _removeARContentFromOfflineStorage();
        } else {
          _downloadARContentForOfflineUse();
        }
      },
      isAvailableOffline: marker.isAvailableOffline,
    );
  }

  /// Build the AR info overlay
  Widget _buildARInfoOverlay(ARContentMarker marker) {
    if (!_showInfoOverlay) {
      return const SizedBox.shrink();
    }

    return ARInfoOverlay(
      title: marker.title,
      description: marker.description,
      contentType: marker.contentType,
      onClose: () {
        setState(() {
          _showInfoOverlay = false;
        });
      },
    );
  }

  /// Download AR content for offline use
  Future<void> _downloadARContentForOfflineUse() async {
    final result = await ref
        .read(currentARContentMarkerProvider.notifier)
        .downloadForOfflineUse();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            result
                ? 'AR content downloaded for offline use'
                : 'Failed to download AR content',
          ),
          backgroundColor: result ? Colors.green : Colors.red,
        ),
      );
    }
  }

  /// Remove AR content from offline storage
  Future<void> _removeARContentFromOfflineStorage() async {
    final result = await ref
        .read(currentARContentMarkerProvider.notifier)
        .removeFromOfflineStorage();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            result
                ? 'AR content removed from offline storage'
                : 'Failed to remove AR content',
          ),
          backgroundColor: result ? Colors.green : Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final arContentMarkerAsync = ref.watch(currentARContentMarkerProvider);
    final arLoadingState = ref.watch(arExperienceLoadingProvider);

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: const CustomAppBar(
        title: 'AR Preview',
        showBackButton: true,
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: arContentMarkerAsync.when(
        data: (marker) {
          if (marker == null) {
            return const Center(
              child: Text('AR content not found'),
            );
          }

          if (_isLoading || arLoadingState.isLoading) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const LoadingIndicator(),
                  const SizedBox(height: 16),
                  const Text(
                    'Loading AR content...',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  if (arLoadingState.progress > 0) ...[
                    SizedBox(
                      width: 200,
                      child: LinearProgressIndicator(
                        value: arLoadingState.progress,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(
                            marker.contentType.color),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${(arLoadingState.progress * 100).toInt()}%',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ],
              ),
            );
          }

          if (arLoadingState.error != null) {
            return Center(
              child: ErrorView(
                error: arLoadingState.error!,
                onRetry: () {
                  _initializeAR();
                },
              ),
            );
          }

          return Stack(
            children: [
              // AR content preview
              _buildARContentPreview(marker),

              // AR controls
              Positioned(
                bottom: 16,
                left: 0,
                right: 0,
                child: _buildARControls(marker),
              ),

              // AR info overlay
              _buildARInfoOverlay(marker),
            ],
          );
        },
        loading: () => const Center(child: LoadingIndicator()),
        error: (error, stackTrace) => Center(
          child: ErrorView(
            error: error.toString(),
            onRetry: () {
              ref
                  .read(currentARContentMarkerProvider.notifier)
                  .loadARContentMarker(widget.arContentId);
            },
          ),
        ),
      ),
    );
  }
}
