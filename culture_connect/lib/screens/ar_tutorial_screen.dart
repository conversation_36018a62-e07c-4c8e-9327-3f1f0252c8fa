import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'ar_explore_screen.dart';

/// A tutorial screen that guides users on how to use the AR features.
class ARTutorialScreen extends StatefulWidget {
  const ARTutorialScreen({super.key});

  @override
  State<ARTutorialScreen> createState() => _ARTutorialScreenState();
}

class _ARTutorialScreenState extends State<ARTutorialScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<TutorialStep> _tutorialSteps = [
    const TutorialStep(
      title: 'Welcome to AR Explore',
      description:
          'Discover cultural landmarks around you in augmented reality. Learn about their history and significance in an immersive way.',
      imagePath: 'assets/images/ar_tutorial_1.png',
      backgroundColor: Color(0xFF6200EE),
      textColor: Colors.white,
    ),
    const TutorialStep(
      title: 'Scan Your Surroundings',
      description:
          'Point your camera at flat surfaces to detect the environment. The app will place virtual landmarks in your real-world space.',
      imagePath: 'assets/images/ar_tutorial_2.png',
      backgroundColor: Color(0xFF03DAC5),
      textColor: Colors.black,
    ),
    const TutorialStep(
      title: 'Interact with Landmarks',
      description:
          'Tap on landmarks to view detailed information. You can rotate, zoom, and explore 3D models of cultural sites.',
      imagePath: 'assets/images/ar_tutorial_3.png',
      backgroundColor: Color(0xFFFF0266),
      textColor: Colors.white,
    ),
    const TutorialStep(
      title: 'Navigate in AR',
      description:
          'Follow AR directions to find your way to landmarks. The app will guide you with arrows and distance indicators.',
      imagePath: 'assets/images/ar_tutorial_4.png',
      backgroundColor: Color(0xFF3700B3),
      textColor: Colors.white,
    ),
    const TutorialStep(
      title: 'Ready to Explore!',
      description:
          'You\'re all set to discover the rich cultural heritage around you. Enjoy your immersive journey!',
      imagePath: 'assets/images/ar_tutorial_5.png',
      backgroundColor: Color(0xFF018786),
      textColor: Colors.white,
    ),
  ];

  @override
  void initState() {
    super.initState();

    // Set system UI overlay style based on the first page
    _setSystemUIOverlayStyle(
        _tutorialSteps[0].backgroundColor, _tutorialSteps[0].textColor);

    // Listen for page changes
    _pageController.addListener(() {
      final page = _pageController.page?.round() ?? 0;
      if (page != _currentPage) {
        setState(() {
          _currentPage = page;
        });

        // Update system UI overlay style
        _setSystemUIOverlayStyle(
          _tutorialSteps[_currentPage].backgroundColor,
          _tutorialSteps[_currentPage].textColor,
        );
      }
    });
  }

  void _setSystemUIOverlayStyle(Color backgroundColor, Color textColor) {
    final brightness =
        textColor == Colors.white ? Brightness.dark : Brightness.light;

    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: brightness,
      systemNavigationBarColor: backgroundColor,
      systemNavigationBarIconBrightness: brightness,
    ));
  }

  @override
  void dispose() {
    _pageController.dispose();

    // Reset system UI overlay style
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.light);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Page view
          PageView.builder(
            controller: _pageController,
            itemCount: _tutorialSteps.length,
            itemBuilder: (context, index) {
              final step = _tutorialSteps[index];
              return _buildTutorialPage(step);
            },
          ),

          // Skip button
          Positioned(
            top: 48,
            right: 16,
            child: TextButton(
              onPressed: _onSkip,
              child: Text(
                'Skip',
                style: TextStyle(
                  color: _tutorialSteps[_currentPage].textColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

          // Page indicator
          Positioned(
            bottom: 120,
            left: 0,
            right: 0,
            child: Center(
              child: SmoothPageIndicator(
                controller: _pageController,
                count: _tutorialSteps.length,
                effect: WormEffect(
                  dotHeight: 10,
                  dotWidth: 10,
                  spacing: 8,
                  activeDotColor: _tutorialSteps[_currentPage].textColor,
                  dotColor:
                      _tutorialSteps[_currentPage].textColor.withAlpha(77),
                ),
              ),
            ),
          ),

          // Navigation buttons
          Positioned(
            bottom: 48,
            left: 16,
            right: 16,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Back button (hidden on first page)
                _currentPage > 0
                    ? TextButton(
                        onPressed: _onBack,
                        child: Row(
                          children: [
                            Icon(
                              Icons.arrow_back,
                              color: _tutorialSteps[_currentPage].textColor,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Back',
                              style: TextStyle(
                                color: _tutorialSteps[_currentPage].textColor,
                              ),
                            ),
                          ],
                        ),
                      )
                    : const SizedBox(width: 80),

                // Next/Start button
                ElevatedButton(
                  onPressed: _onNext,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _tutorialSteps[_currentPage].textColor,
                    foregroundColor:
                        _tutorialSteps[_currentPage].backgroundColor,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 16,
                    ),
                  ),
                  child: Text(
                    _currentPage == _tutorialSteps.length - 1
                        ? 'Get Started'
                        : 'Next',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTutorialPage(TutorialStep step) {
    return Container(
      color: step.backgroundColor,
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Image placeholder (in a real app, this would be an actual image)
          Container(
            height: 250,
            width: double.infinity,
            decoration: BoxDecoration(
              color: step.textColor.withAlpha(26),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: Icon(
                Icons.view_in_ar,
                size: 80,
                color: step.textColor.withAlpha(128),
              ),
            ),
          ),
          const SizedBox(height: 48),

          // Title
          Text(
            step.title,
            style: TextStyle(
              color: step.textColor,
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),

          // Description
          Text(
            step.description,
            style: TextStyle(
              color: step.textColor.withAlpha(204),
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _onBack() {
    _pageController.previousPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _onNext() {
    if (_currentPage < _tutorialSteps.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _startARExperience();
    }
  }

  void _onSkip() {
    _startARExperience();
  }

  void _startARExperience() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => const ARExploreScreen(),
      ),
    );
  }
}

/// A class representing a step in the AR tutorial.
class TutorialStep {
  final String title;
  final String description;
  final String imagePath;
  final Color backgroundColor;
  final Color textColor;

  const TutorialStep({
    required this.title,
    required this.description,
    required this.imagePath,
    required this.backgroundColor,
    required this.textColor,
  });
}
