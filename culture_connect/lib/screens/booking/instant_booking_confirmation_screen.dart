import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lottie/lottie.dart';
import 'package:culture_connect/models/instant_booking_model.dart';
import 'package:culture_connect/providers/instant_booking_provider.dart';
import 'package:culture_connect/screens/booking/booking_details_screen.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/payment/payment_summary.dart';
import 'package:url_launcher/url_launcher.dart';

/// A screen that displays an instant booking confirmation
class InstantBookingConfirmationScreen extends ConsumerStatefulWidget {
  /// The instant booking to display
  final InstantBookingModel booking;

  /// Creates a new instant booking confirmation screen
  const InstantBookingConfirmationScreen({
    super.key,
    required this.booking,
  });

  @override
  ConsumerState<InstantBookingConfirmationScreen> createState() =>
      _InstantBookingConfirmationScreenState();
}

class _InstantBookingConfirmationScreenState
    extends ConsumerState<InstantBookingConfirmationScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    if (widget.booking.status == InstantBookingStatus.success) {
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _viewReceipt() async {
    if (widget.booking.transaction?.receiptUrl == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Receipt not available'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final url = Uri.parse(widget.booking.transaction!.receiptUrl!);
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not open receipt'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _viewBookingDetails() {
    if (widget.booking.booking == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Booking details not available'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BookingDetailsScreen(
          bookingId: widget.booking.booking!.id,
        ),
      ),
    );
  }

  Future<void> _cancelBooking() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Booking'),
        content: const Text(
            'Are you sure you want to cancel this booking? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Yes, Cancel'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref
            .read(instantBookingNotifierProvider.notifier)
            .cancelInstantBooking(widget.booking.id);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Booking cancelled successfully'),
              backgroundColor: Colors.green,
            ),
          );

          Navigator.pop(context);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error cancelling booking: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isSuccessful = widget.booking.status == InstantBookingStatus.success;

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Booking ${isSuccessful ? 'Confirmation' : 'Failed'}',
        showBackButton: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(height: 16),

            // Status animation
            if (isSuccessful)
              Lottie.asset(
                'assets/animations/booking_success.json',
                controller: _animationController,
                width: 200,
                height: 200,
                fit: BoxFit.contain,
              )
            else
              Lottie.asset(
                'assets/animations/booking_failed.json',
                width: 200,
                height: 200,
                fit: BoxFit.contain,
                repeat: false,
              ),

            const SizedBox(height: 16),

            // Status text
            Text(
              isSuccessful ? 'Booking Successful!' : 'Booking Failed',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: isSuccessful ? Colors.green[700] : Colors.red,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 8),

            // Status message
            Text(
              isSuccessful
                  ? 'Your booking has been confirmed.'
                  : widget.booking.errorMessage ??
                      'There was an error processing your booking.',
              style: const TextStyle(
                fontSize: 16,
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 24),

            // Booking details
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Booking Details',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildDetailRow('Booking ID', widget.booking.id),
                  _buildDetailRow('Service', widget.booking.serviceName),
                  _buildDetailRow('Date', widget.booking.formattedServiceDate),
                  _buildDetailRow('Participants',
                      widget.booking.participantCount.toString()),
                  _buildDetailRow('Status', widget.booking.status.displayName),
                  _buildDetailRow(
                      'Payment Method', widget.booking.paymentMethod.name),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Payment summary
            PaymentSummary(
              baseAmount: widget.booking.totalAmount,
              currency: widget.booking.currency,
              title: widget.booking.serviceName,
              imageUrl: widget.booking.serviceImageUrl,
              additionalDetails: {
                'Date': widget.booking.formattedServiceDate,
                'Participants': widget.booking.participantCount.toString(),
              },
            ),

            const SizedBox(height: 32),

            // Action buttons
            if (isSuccessful) ...[
              // View receipt button
              if (widget.booking.transaction?.receiptUrl != null)
                OutlinedButton.icon(
                  onPressed: _viewReceipt,
                  icon: const Icon(Icons.receipt),
                  label: const Text('View Receipt'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                    side: const BorderSide(color: AppTheme.primaryColor),
                  ),
                ),

              const SizedBox(height: 16),

              // View booking details button
              if (widget.booking.booking != null)
                OutlinedButton.icon(
                  onPressed: _viewBookingDetails,
                  icon: const Icon(Icons.info_outline),
                  label: const Text('View Booking Details'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                    side: const BorderSide(color: AppTheme.primaryColor),
                  ),
                ),

              const SizedBox(height: 16),

              // Cancel booking button
              OutlinedButton.icon(
                onPressed: _cancelBooking,
                icon: const Icon(Icons.cancel),
                label: const Text('Cancel Booking'),
                style: OutlinedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  side: const BorderSide(color: Colors.red),
                  foregroundColor: Colors.red,
                ),
              ),

              const SizedBox(height: 16),

              // Done button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text(
                    'Done',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ] else ...[
              // Try again button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text(
                    'Try Again',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
