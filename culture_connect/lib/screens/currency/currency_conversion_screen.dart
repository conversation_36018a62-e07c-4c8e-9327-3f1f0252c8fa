import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/providers/currency/currency_providers.dart';
import 'package:culture_connect/widgets/currency/currency_selection_dropdown.dart';
import 'package:culture_connect/widgets/currency/exchange_rate_history_chart.dart';

/// A screen for converting between currencies
///
/// This screen allows users to:
/// - Convert between different currencies
/// - View historical exchange rate data
/// - Access currency preferences
/// - Works in both online and offline modes
class CurrencyConversionScreen extends ConsumerStatefulWidget {
  /// Creates a new currency conversion screen
  const CurrencyConversionScreen({super.key});

  @override
  ConsumerState<CurrencyConversionScreen> createState() =>
      _CurrencyConversionScreenState();
}

class _CurrencyConversionScreenState
    extends ConsumerState<CurrencyConversionScreen> {
  final _amountController = TextEditingController(text: '100');
  String _fromCurrency = 'USD';
  String _toCurrency = 'EUR';
  bool _showChart = true;
  bool _isOffline = false;

  @override
  void initState() {
    super.initState();

    // Initialize with the user's preferred currency
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      final preferences = ref.read(currentCurrencyPreferencesProvider);
      setState(() {
        _toCurrency = preferences.preferredCurrency;
      });

      // Check connectivity status
      _checkConnectivity();

      // Listen for connectivity changes
      _setupConnectivityListener();
    });
  }

  /// Check the current connectivity status
  void _checkConnectivity() {
    if (!mounted) return;

    // For simplicity, we'll just set a default value
    // In a real implementation, we would check the actual connectivity status
    setState(() {
      _isOffline = false;
    });

    // Listen for offline mode changes
    ref.listenManual(currencyConversionServiceProvider, (previous, current) {
      // This is a simplified implementation
      // In a real app, we would check the actual connectivity status
    });
  }

  /// Setup a listener for connectivity changes
  void _setupConnectivityListener() {
    // In a real implementation, we would listen to connectivity changes
    // For now, we'll just update the UI when the app starts
  }

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  /// Swap the from and to currencies
  void _swapCurrencies() {
    setState(() {
      final temp = _fromCurrency;
      _fromCurrency = _toCurrency;
      _toCurrency = temp;
    });
  }

  /// Toggle the chart visibility
  void _toggleChart() {
    setState(() {
      _showChart = !_showChart;
    });
  }

  @override
  Widget build(BuildContext context) {
    // Get the currencies
    final fromCurrency =
        ref.watch(currencyDataServiceProvider).getCurrencyByCode(_fromCurrency);
    final toCurrency =
        ref.watch(currencyDataServiceProvider).getCurrencyByCode(_toCurrency);

    // Get the exchange rate
    final exchangeRateAsync =
        ref.watch(exchangeRateProvider((_fromCurrency, _toCurrency)));

    // Get the historical rates
    final endDate = DateTime.now();
    final startDate = endDate.subtract(const Duration(days: 30));
    final historicalRatesAsync = ref.watch(historicalRatesProvider(
        (_fromCurrency, _toCurrency, startDate, endDate)));

    // Parse the amount
    double amount;
    try {
      amount = double.parse(_amountController.text);
    } catch (e) {
      amount = 0;
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Currency Converter'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          // Offline indicator
          if (_isOffline)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Chip(
                label: const Text('Offline'),
                backgroundColor: Colors.amber.withAlpha(50),
                labelStyle: const TextStyle(
                  color: Colors.amber,
                  fontWeight: FontWeight.bold,
                ),
                avatar: const Icon(
                  Icons.cloud_off,
                  color: Colors.amber,
                  size: 16,
                ),
                padding: EdgeInsets.zero,
                visualDensity: VisualDensity.compact,
              ),
            ),
          IconButton(
            icon: Icon(
              _showChart ? Icons.show_chart : Icons.bar_chart,
            ),
            onPressed: _toggleChart,
            tooltip: _showChart ? 'Hide Chart' : 'Show Chart',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.pushNamed(context, '/currency-preferences');
            },
            tooltip: 'Currency Preferences',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Amount input
            const Text(
              'Amount',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _amountController,
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
              ],
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Text(
                    fromCurrency?.symbol ?? _fromCurrency,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _amountController.clear();
                  },
                ),
              ),
              onChanged: (value) {
                setState(() {});
              },
            ),

            const SizedBox(height: 16),

            // From currency
            const Text(
              'From',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            CurrencySelectionDropdown(
              selectedCurrencyCode: _fromCurrency,
              onCurrencySelected: (currencyCode) {
                setState(() {
                  _fromCurrency = currencyCode;
                });
              },
              showName: true,
            ),

            const SizedBox(height: 16),

            // Swap button
            Center(
              child: IconButton(
                icon: const Icon(Icons.swap_vert),
                onPressed: _swapCurrencies,
                tooltip: 'Swap Currencies',
                style: IconButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor.withAlpha(25),
                  foregroundColor: AppTheme.primaryColor,
                ),
              ),
            ),

            const SizedBox(height: 16),

            // To currency
            const Text(
              'To',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            CurrencySelectionDropdown(
              selectedCurrencyCode: _toCurrency,
              onCurrencySelected: (currencyCode) {
                setState(() {
                  _toCurrency = currencyCode;
                });
              },
              showName: true,
            ),

            const SizedBox(height: 24),

            // Result
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(13),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Converted Amount',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  exchangeRateAsync.when(
                    data: (exchangeRate) {
                      final convertedAmount = exchangeRate.convert(amount);
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Converted amount
                          Row(
                            children: [
                              if (toCurrency != null) ...[
                                Text(
                                  toCurrency.flag,
                                  style: const TextStyle(fontSize: 20),
                                ),
                                const SizedBox(width: 8),
                              ],
                              Expanded(
                                child: Text(
                                  toCurrency?.formatAmount(convertedAmount) ??
                                      '$_toCurrency${convertedAmount.toStringAsFixed(2)}',
                                  style: const TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 8),

                          // Original amount
                          Row(
                            children: [
                              if (fromCurrency != null) ...[
                                Text(
                                  fromCurrency.flag,
                                  style: const TextStyle(fontSize: 16),
                                ),
                                const SizedBox(width: 8),
                              ],
                              Expanded(
                                child: Text(
                                  fromCurrency?.formatAmount(amount) ??
                                      '$_fromCurrency${amount.toStringAsFixed(2)}',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey[600],
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),

                          // Exchange rate
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  '1 $_fromCurrency = ${exchangeRate.rate.toStringAsFixed(4)} $_toCurrency',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey[600],
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                exchangeRate.isFromCache
                                    ? 'Cached ${exchangeRate.lastUpdated}'
                                    : 'Updated ${exchangeRate.lastUpdated}',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: exchangeRate.isFromCache
                                      ? Colors.amber[700]
                                      : Colors.grey[500],
                                ),
                              ),
                            ],
                          ),

                          // Offline indicator
                          if (_isOffline) ...[
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Icon(
                                  Icons.cloud_off,
                                  size: 14,
                                  color: Colors.amber[700],
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Using cached exchange rates',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.amber[700],
                                    fontStyle: FontStyle.italic,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      );
                    },
                    loading: () => const Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 24),
                        child: CircularProgressIndicator(),
                      ),
                    ),
                    error: (error, stackTrace) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: Column(
                        children: [
                          const Icon(
                            Icons.error_outline,
                            color: Colors.red,
                            size: 48,
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'Error loading exchange rates',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.red,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _isOffline
                                ? 'You are offline and no cached data is available'
                                : error.toString(),
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.red,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          if (!_isOffline)
                            ElevatedButton(
                              onPressed: () {
                                setState(() {});
                              },
                              child: const Text('Retry'),
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Historical chart
            if (_showChart) ...[
              const Text(
                'Exchange Rate History',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              historicalRatesAsync.when(
                data: (historyData) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (historyData.isFromCache) ...[
                        Row(
                          children: [
                            Icon(
                              Icons.cloud_off,
                              size: 14,
                              color: Colors.amber[700],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Using cached historical data',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.amber[700],
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                      ],
                      ExchangeRateHistoryChart(
                        historyData: historyData,
                        height: 250,
                        showTitle: false,
                      ),
                    ],
                  );
                },
                loading: () => const SizedBox(
                  height: 250,
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
                error: (error, stackTrace) => SizedBox(
                  height: 250,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          color: Colors.red,
                          size: 32,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Error loading historical data',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.red,
                          ),
                        ),
                        if (!_isOffline) ...[
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () {
                              setState(() {});
                            },
                            child: const Text('Retry'),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
