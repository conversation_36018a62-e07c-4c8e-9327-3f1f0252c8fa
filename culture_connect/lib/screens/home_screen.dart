import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:culture_connect/screens/safety/safety_center_screen.dart';
import 'package:culture_connect/screens/travel/restaurant/restaurant_list_screen.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_bottom_navigation.dart';
import 'package:culture_connect/widgets/experience_card.dart';
import 'package:culture_connect/widgets/common/refresh_animation_tutorial_dialog.dart';
import 'package:culture_connect/widgets/common/animated_refresh_indicator.dart';
import 'package:culture_connect/providers/refresh_animation_provider.dart';
import 'package:shimmer/shimmer.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen>
    with SingleTickerProviderStateMixin {
  final _searchController = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  bool _isLoading = true;
  bool _isSearchFocused = false;
  final _scrollController = ScrollController();
  double _scrollOffset = 0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    _animationController.forward();
    _simulateLoading();
    _scrollController.addListener(_onScroll);

    // Show the refresh animation tutorial if the user hasn't seen it yet
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndShowRefreshTutorial();
    });
  }

  void _checkAndShowRefreshTutorial() async {
    final hasSeenTutorial = ref.read(hasSeenRefreshTutorialProvider);
    if (!hasSeenTutorial) {
      await Future.delayed(const Duration(seconds: 1));
      if (mounted) {
        RefreshAnimationTutorialDialog.show(context);
      }
    }
  }

  void _onScroll() {
    setState(() {
      _scrollOffset = _scrollController.offset;
    });
  }

  Future<void> _simulateLoading() async {
    await Future.delayed(const Duration(seconds: 2));
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: _isLoading ? _buildLoadingState() : _buildCurrentScreen(),
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(25),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: const CustomBottomNavigation(),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 16),
              Container(
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              const SizedBox(height: 24),
              Container(
                height: 180,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              const SizedBox(height: 32),
              Container(
                height: 100,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentScreen() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: _buildHomeScreen(),
    );
  }

  Widget _buildHomeScreen() {
    final selectedAnimationType = ref.watch(refreshAnimationTypeProvider);

    return AnimatedRefreshIndicator(
      onRefresh: () async {
        setState(() {
          _isLoading = true;
        });
        await _simulateLoading();
      },
      animationType: selectedAnimationType,
      color: AppTheme.primaryColor,
      backgroundColor: Colors.white,
      child: SingleChildScrollView(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 16),

              // Welcome Section with Animation
              _buildWelcomeSection(),

              const SizedBox(height: 24),

              // Enhanced Search Bar
              _buildEnhancedSearchBar(),

              const SizedBox(height: 32),

              // Hero Section with Parallax Effect
              _buildHeroSection(),

              const SizedBox(height: 32),

              // Quick Actions with Hover Effects
              _buildQuickActionsSection(),

              const SizedBox(height: 32),

              // Featured Experiences with Carousel
              _buildFeaturedExperiencesSection(),

              const SizedBox(height: 32),

              // Top Guides with Modern Cards
              _buildTopGuidesSection(),

              const SizedBox(height: 32),

              // Nearby Experiences with Map Preview
              _buildNearbyExperiencesSection(),

              const SizedBox(height: 120),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Row(
      children: [
        const Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Welcome back,',
              style: TextStyle(
                color: AppTheme.textSecondaryColor,
                fontSize: 16,
              ),
            ),
            SizedBox(height: 4),
            Text(
              'John Doe',
              style: TextStyle(
                color: AppTheme.textPrimaryColor,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const Spacer(),
        Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: AppTheme.primaryColor,
              width: 2,
            ),
          ),
          child: const CircleAvatar(
            radius: 24,
            backgroundColor: AppTheme.surfaceColor,
            child: Icon(
              Icons.person,
              color: AppTheme.primaryColor,
              size: 24,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedSearchBar() {
    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Focus(
        onFocusChange: (hasFocus) {
          setState(() {
            _isSearchFocused = hasFocus;
          });
        },
        child: TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'Search experiences, guides, locations...',
            hintStyle: const TextStyle(
              color: AppTheme.textSecondaryColor,
              fontSize: 14,
            ),
            prefixIcon: const Icon(
              Icons.search,
              color: AppTheme.textSecondaryColor,
              size: 20,
            ),
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (_isSearchFocused)
                  IconButton(
                    icon: const Icon(
                      Icons.clear,
                      color: AppTheme.textSecondaryColor,
                      size: 20,
                    ),
                    onPressed: () {
                      _searchController.clear();
                    },
                  ),
                IconButton(
                  icon: const Icon(
                    Icons.mic,
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
                  onPressed: () {
                    // TODO: Implement voice search
                  },
                ),
              ],
            ),
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(vertical: 15),
          ),
        ),
      ),
    );
  }

  Widget _buildHeroSection() {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        return Container(
          constraints: const BoxConstraints(
            minHeight: 180,
            maxHeight: 220,
          ),
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: AppTheme.primaryGradient,
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryColor.withAlpha(76),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Stack(
            children: [
              // Background pattern with parallax effect
              Positioned(
                right: -20 + (_scrollOffset * 0.1),
                bottom: -20 + (_scrollOffset * 0.05),
                child: Icon(
                  Icons.explore,
                  size: 150,
                  color: Colors.white.withAlpha(25),
                ),
              ),

              // Content
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Flexible(
                      child: Text(
                        'Discover Authentic\nCultural Experiences',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          height: 1.3,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Flexible(
                      child: Text(
                        'Connect with local guides and immerse yourself in rich traditions',
                        style: TextStyle(
                          color: Colors.white.withAlpha(204),
                          fontSize: 12,
                          height: 1.5,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: AppTheme.primaryColor,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      onPressed: () {
                        // TODO: Navigate to explore experiences
                      },
                      child: Text(
                        'Explore Now',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildQuickActionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Actions',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildQuickAction(
              icon: FontAwesomeIcons.compass,
              label: 'Explore',
              color: AppTheme.primaryColor,
              onTap: () {
                // Navigate to explore screen
                Navigator.pushNamed(context, '/explore');
              },
            ),
            _buildQuickAction(
              icon: FontAwesomeIcons.mapLocationDot,
              label: 'Safety Center',
              color: AppTheme.secondaryColor,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SafetyCenterScreen(),
                  ),
                );
              },
            ),
            _buildQuickAction(
              icon: FontAwesomeIcons.language,
              label: 'Translate',
              color: AppTheme.accentColor,
              onTap: () {
                Navigator.pushNamed(context, '/voice-translation');
              },
            ),
            _buildQuickAction(
              icon: FontAwesomeIcons.utensils,
              label: 'Restaurants',
              color: Colors.amber,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const RestaurantListScreen(),
                  ),
                );
              },
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildQuickAction(
              icon: FontAwesomeIcons.camera,
              label: 'Image Text',
              color: Colors.purple,
              onTap: () {
                Navigator.pushNamed(context, '/image-text-translation');
              },
            ),
            const SizedBox(width: 16),
            _buildQuickAction(
              icon: FontAwesomeIcons.moneyBillTransfer,
              label: 'Currency',
              color: Colors.green,
              onTap: () {
                Navigator.pushNamed(context, '/currency-converter');
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickAction({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: color.withAlpha(25),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: FaIcon(
                icon,
                color: color,
                size: 24,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturedExperiencesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Featured Experiences',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: Navigate to all featured experiences
              },
              child: Text(
                'See All',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 280,
          child: ListView(
            scrollDirection: Axis.horizontal,
            children: [
              ExperienceCard(
                title: 'Yoruba Cooking Class',
                location: 'Lagos, Nigeria',
                imageUrl:
                    'https://via.placeholder.com/400x300?text=Cooking+Class',
                rating: 4.8,
                price: '\$45',
                isFeatured: true,
                category: 'Culinary',
                reviewCount: 128,
                onTap: () {
                  // TODO: Navigate to experience details
                },
              ),
              const SizedBox(width: 16),
              ExperienceCard(
                title: 'Maasai Dance Workshop',
                location: 'Nairobi, Kenya',
                imageUrl:
                    'https://via.placeholder.com/400x300?text=Dance+Workshop',
                rating: 4.6,
                price: '\$35',
                category: 'Dance',
                reviewCount: 89,
                onTap: () {
                  // TODO: Navigate to experience details
                },
              ),
              const SizedBox(width: 16),
              ExperienceCard(
                title: 'Cape Town Cultural Tour',
                location: 'Cape, South Africa',
                imageUrl:
                    'https://via.placeholder.com/400x300?text=Cultural+Tour',
                rating: 4.9,
                price: '\$60',
                isFeatured: true,
                category: 'Tour',
                reviewCount: 256,
                onTap: () {
                  // TODO: Navigate to experience details
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTopGuidesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Top Guides',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: Navigate to all guides
              },
              child: Text(
                'See All',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 100,
          child: ListView(
            scrollDirection: Axis.horizontal,
            children: [
              _buildGuideItem(
                name: 'Adebayo O.',
                location: 'Lagos',
                imageUrl: 'https://randomuser.me/api/portraits/men/32.jpg',
                rating: 4.9,
              ),
              const SizedBox(width: 16),
              _buildGuideItem(
                name: 'Wanjiku M.',
                location: 'Nairobi',
                imageUrl: 'https://randomuser.me/api/portraits/women/44.jpg',
                rating: 4.8,
              ),
              const SizedBox(width: 16),
              _buildGuideItem(
                name: 'Thabo N.',
                location: 'Cape Town',
                imageUrl: 'https://randomuser.me/api/portraits/men/22.jpg',
                rating: 4.7,
              ),
              const SizedBox(width: 16),
              _buildGuideItem(
                name: 'Amara C.',
                location: 'Accra',
                imageUrl: 'https://randomuser.me/api/portraits/women/67.jpg',
                rating: 4.9,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildGuideItem({
    required String name,
    required String location,
    required String imageUrl,
    required double rating,
  }) {
    return Container(
      width: 200,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Profile image
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              image: DecorationImage(
                image: NetworkImage(imageUrl),
                fit: BoxFit.cover,
              ),
            ),
          ),
          const SizedBox(width: 12),

          // Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  name,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 12,
                      color: AppTheme.textSecondaryColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      location,
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.star,
                      size: 14,
                      color: AppTheme.secondaryColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      rating.toString(),
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNearbyExperiencesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Nearby Experiences',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: Navigate to map view
              },
              child: Text(
                'View Map',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          height: 200,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(25),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: const GoogleMap(
              initialCameraPosition: CameraPosition(
                target: LatLng(6.5244, 3.3792), // Lagos coordinates
                zoom: 12,
              ),
              myLocationEnabled: true,
              myLocationButtonEnabled: true,
              zoomControlsEnabled: false,
              mapToolbarEnabled: false,
            ),
          ),
        ),
        const SizedBox(height: 16),
        ExperienceCard(
          title: 'Traditional Drum Circle Workshop',
          location: 'Ikeja, Lagos',
          imageUrl: 'https://via.placeholder.com/400x300?text=Drum+Workshop',
          rating: 4.7,
          price: '\$30',
          duration: '2 hours',
          isHorizontal: true,
          category: 'Music',
          reviewCount: 45,
          onTap: () {
            // TODO: Navigate to experience details
          },
        ),
        const SizedBox(height: 16),
        ExperienceCard(
          title: 'Authentic Nigerian Cuisine Tour',
          location: 'Victoria Island, Lagos',
          imageUrl: 'https://via.placeholder.com/400x300?text=Cuisine+Tour',
          rating: 4.5,
          price: '\$40',
          duration: '3 hours',
          isHorizontal: true,
          category: 'Food',
          reviewCount: 78,
          onTap: () {
            // TODO: Navigate to experience details
          },
        ),
      ],
    );
  }
}
