import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/loyalty/loyalty_points_transaction.dart';
import 'package:culture_connect/models/loyalty/loyalty_program_model.dart';
import 'package:culture_connect/providers/loyalty_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';

/// A screen that displays loyalty points history
class LoyaltyPointsHistoryScreen extends ConsumerStatefulWidget {
  /// The loyalty program
  final LoyaltyProgramModel loyaltyProgram;

  /// Creates a new loyalty points history screen
  const LoyaltyPointsHistoryScreen({
    super.key,
    required this.loyaltyProgram,
  });

  @override
  ConsumerState<LoyaltyPointsHistoryScreen> createState() =>
      _LoyaltyPointsHistoryScreenState();
}

class _LoyaltyPointsHistoryScreenState
    extends ConsumerState<LoyaltyPointsHistoryScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final transactionsAsync = ref.watch(loyaltyPointsTransactionsProvider);

    // Sort transactions by date (newest first)
    final sortedTransactions =
        List<LoyaltyPointsTransaction>.from(transactionsAsync)
          ..sort((a, b) => b.date.compareTo(a.date));

    // Filter transactions
    final earningTransactions =
        sortedTransactions.where((t) => t.isEarning).toList();
    final spendingTransactions =
        sortedTransactions.where((t) => !t.isEarning).toList();

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Points History',
        showBackButton: true,
      ),
      body: Column(
        children: [
          // Points summary
          Container(
            padding: const EdgeInsets.all(16),
            color: AppTheme.primaryColor.withAlpha(26),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Current Balance',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${widget.loyaltyProgram.pointsBalance}',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Lifetime Points',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${widget.loyaltyProgram.lifetimePoints}',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Tab bar
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'All'),
              Tab(text: 'Earned'),
              Tab(text: 'Spent'),
            ],
            labelColor: AppTheme.primaryColor,
            unselectedLabelColor: AppTheme.textSecondaryColor,
            indicatorColor: AppTheme.primaryColor,
          ),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // All transactions
                _buildTransactionsList(sortedTransactions),

                // Earning transactions
                _buildTransactionsList(earningTransactions),

                // Spending transactions
                _buildTransactionsList(spendingTransactions),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsList(List<LoyaltyPointsTransaction> transactions) {
    if (transactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            const Text(
              'No transactions found',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      );
    }

    // Group transactions by month
    final groupedTransactions = <String, List<LoyaltyPointsTransaction>>{};

    for (final transaction in transactions) {
      final monthYear = DateFormat('MMMM yyyy').format(transaction.date);

      if (!groupedTransactions.containsKey(monthYear)) {
        groupedTransactions[monthYear] = [];
      }

      groupedTransactions[monthYear]!.add(transaction);
    }

    // Sort months (newest first)
    final sortedMonths = groupedTransactions.keys.toList()
      ..sort((a, b) {
        final dateA = DateFormat('MMMM yyyy').parse(a);
        final dateB = DateFormat('MMMM yyyy').parse(b);
        return dateB.compareTo(dateA);
      });

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: sortedMonths.length,
      itemBuilder: (context, index) {
        final month = sortedMonths[index];
        final monthTransactions = groupedTransactions[month]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Month header
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Text(
                month,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ),

            // Month transactions
            ...monthTransactions
                .map((transaction) => _buildTransactionItem(transaction)),

            // Divider
            if (index < sortedMonths.length - 1) const Divider(height: 32),
          ],
        );
      },
    );
  }

  Widget _buildTransactionItem(LoyaltyPointsTransaction transaction) {
    final isEarning = transaction.isEarning;
    final pointsColor = isEarning ? Colors.green : Colors.red;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            // Transaction type icon
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: transaction.type.isEarning
                    ? Colors.green.withAlpha(26)
                    : Colors.red.withAlpha(26),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Icon(
                  transaction.type.icon,
                  size: 20,
                  color: transaction.type.isEarning ? Colors.green : Colors.red,
                ),
              ),
            ),

            const SizedBox(width: 12),

            // Transaction details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    transaction.type.displayName,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    transaction.description,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondaryColor,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatDate(transaction.date),
                    style: const TextStyle(
                      fontSize: 10,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(width: 12),

            // Points
            Text(
              transaction.formattedPoints,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: pointsColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays < 1) {
      return 'Today, ${DateFormat('h:mm a').format(date)}';
    } else if (difference.inDays < 2) {
      return 'Yesterday, ${DateFormat('h:mm a').format(date)}';
    } else if (difference.inDays < 7) {
      return DateFormat('EEEE, h:mm a').format(date);
    } else {
      return DateFormat('MMM d, yyyy, h:mm a').format(date);
    }
  }
}
