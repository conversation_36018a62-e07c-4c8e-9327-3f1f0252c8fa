import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/models/offline/offline_content.dart';
import 'package:culture_connect/models/offline/sync_status.dart';
import 'package:culture_connect/providers/offline_mode_provider.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/offline/sync_status_indicator.dart';
import 'package:culture_connect/widgets/offline/offline_banner.dart';

/// A screen for managing offline content
class OfflineContentManagementScreen extends ConsumerStatefulWidget {
  /// Creates a new offline content management screen
  const OfflineContentManagementScreen({super.key});

  @override
  ConsumerState<OfflineContentManagementScreen> createState() =>
      _OfflineContentManagementScreenState();
}

class _OfflineContentManagementScreenState
    extends ConsumerState<OfflineContentManagementScreen> {
  final List<String> _contentTypes = [
    'All',
    'Itinerary',
    'Experience',
    'Hotel',
    'Flight',
    'Restaurant',
    'Car Rental',
    'Cruise',
  ];

  String _selectedContentType = 'All';

  @override
  Widget build(BuildContext context) {
    // Get the offline content
    final offlineContentAsync = ref.watch(offlineContentNotifierProvider);

    // Get the connectivity status
    final isOnline = ref.watch(isOnlineProvider);

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Offline Content',
        showBackButton: true,
      ),
      body: OfflineBannerWrapper(
        child: Column(
          children: [
            // Content type filter
            _buildContentTypeFilter(),

            // Content list
            Expanded(
              child: offlineContentAsync.when(
                data: (offlineContent) {
                  // Filter content by type
                  final filteredContent = _selectedContentType == 'All'
                      ? offlineContent
                      : offlineContent
                          .where((content) =>
                              content.contentTypeDisplayName ==
                              _selectedContentType)
                          .toList();

                  if (filteredContent.isEmpty) {
                    return _buildEmptyState(isOnline);
                  }

                  return RefreshIndicator(
                    onRefresh: () => ref
                        .read(offlineContentNotifierProvider.notifier)
                        .syncOfflineContent(),
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: filteredContent.length,
                      itemBuilder: (context, index) {
                        final content = filteredContent[index];
                        return _buildContentItem(content);
                      },
                    ),
                  );
                },
                loading: () => const Center(child: LoadingIndicator()),
                error: (error, stackTrace) => Center(
                  child: ErrorView(
                    error: error.toString(),
                    onRetry: () => ref.refresh(offlineContentNotifierProvider),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showDownloadDialog(),
        tooltip: 'Add Content',
        child: const Icon(Icons.add),
      ),
    );
  }

  /// Build the content type filter
  Widget _buildContentTypeFilter() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 opacity = 13 alpha
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: _contentTypes.length,
        itemBuilder: (context, index) {
          final contentType = _contentTypes[index];
          final isSelected = _selectedContentType == contentType;

          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: ChoiceChip(
              label: Text(contentType),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _selectedContentType = contentType;
                  });
                }
              },
              backgroundColor: Colors.grey[200],
              selectedColor: AppColors.primary.withAlpha(51),
              labelStyle: TextStyle(
                color: isSelected ? AppColors.primary : Colors.black,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          );
        },
      ),
    );
  }

  /// Build a content item
  Widget _buildContentItem(OfflineContent content) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _showContentDetails(content),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title and sync status
              Row(
                children: [
                  Icon(
                    IconData(
                      int.parse('0xe${content.contentTypeIcon}', radix: 16),
                      fontFamily: 'MaterialIcons',
                    ),
                    color: AppColors.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          content.title,
                          style: AppTextStyles.subtitle1.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          content.contentTypeDisplayName,
                          style: AppTextStyles.body2.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  SyncStatusIndicator(
                    status: content.syncStatus,
                    size: 20,
                    showLabel: false,
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Details
              Row(
                children: [
                  // Size
                  if (content.contentSize != null) ...[
                    Icon(
                      Icons.sd_storage,
                      color: Colors.grey[600],
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      content.formattedSize,
                      style: AppTextStyles.caption,
                    ),
                    const SizedBox(width: 16),
                  ],

                  // Last sync time
                  Icon(
                    Icons.access_time,
                    color: Colors.grey[600],
                    size: 16,
                  ),

                  const SizedBox(width: 4),

                  Text(
                    content.formattedLastSyncTime,
                    style: AppTextStyles.caption,
                  ),
                ],
              ),

              // Error message
              if (content.syncStatus == SyncStatus.failed &&
                  content.errorMessage != null) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red.withAlpha(26),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          content.errorMessage!,
                          style: AppTextStyles.caption.copyWith(
                            color: Colors.red,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 8),

              // Actions
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // Retry button
                  if (content.syncStatus == SyncStatus.failed)
                    TextButton.icon(
                      onPressed: () => _retrySync(content),
                      icon: const Icon(Icons.refresh),
                      label: const Text('Retry'),
                      style: TextButton.styleFrom(
                        foregroundColor: AppColors.primary,
                      ),
                    ),

                  // Remove button
                  TextButton.icon(
                    onPressed: () => _showRemoveDialog(content),
                    icon: const Icon(Icons.delete),
                    label: const Text('Remove'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.red,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build the empty state
  Widget _buildEmptyState(bool isOnline) {
    if (!isOnline) {
      return const OfflineErrorView(
        title: 'You\'re offline',
        message: 'Connect to the internet to manage your offline content.',
      );
    }

    return EmptyStateView(
      title: 'No Offline Content',
      message: _selectedContentType == 'All'
          ? 'You haven\'t saved any content for offline use yet.'
          : 'You haven\'t saved any $_selectedContentType content for offline use yet.',
      icon: Icons.offline_bolt,
      actionText: 'Download Content',
      onAction: () => _showDownloadDialog(),
    );
  }

  /// Show the content details
  void _showContentDetails(OfflineContent content) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16),
        ),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title
              Text(
                content.title,
                style: AppTextStyles.headline6,
              ),

              const SizedBox(height: 8),

              // Content type
              Row(
                children: [
                  Icon(
                    IconData(
                      int.parse('0xe${content.contentTypeIcon}', radix: 16),
                      fontFamily: 'MaterialIcons',
                    ),
                    color: AppColors.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    content.contentTypeDisplayName,
                    style: AppTextStyles.subtitle2,
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Sync status
              Row(
                children: [
                  SyncStatusIndicator(
                    status: content.syncStatus,
                    size: 20,
                    showLabel: true,
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Details
              _buildDetailItem(
                'Size',
                content.formattedSize,
                Icons.sd_storage,
              ),

              _buildDetailItem(
                'Last Synced',
                content.formattedLastSyncTime,
                Icons.access_time,
              ),

              if (content.expirationTime != null)
                _buildDetailItem(
                  'Expires',
                  content.formattedExpirationTime,
                  Icons.timer,
                ),

              // Error message
              if (content.syncStatus == SyncStatus.failed &&
                  content.errorMessage != null) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withAlpha(26),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red.withAlpha(77)),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Sync Failed',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              content.errorMessage!,
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.red,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 24),

              // Actions
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Retry button
                  if (content.syncStatus == SyncStatus.failed)
                    ElevatedButton.icon(
                      onPressed: () {
                        Navigator.of(context).pop();
                        _retrySync(content);
                      },
                      icon: const Icon(Icons.refresh),
                      label: const Text('Retry'),
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.white,
                        backgroundColor: AppColors.primary,
                      ),
                    ),

                  // Remove button
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.of(context).pop();
                      _showRemoveDialog(content);
                    },
                    icon: const Icon(Icons.delete),
                    label: const Text('Remove'),
                    style: ElevatedButton.styleFrom(
                      foregroundColor: Colors.white,
                      backgroundColor: Colors.red,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  /// Build a detail item
  Widget _buildDetailItem(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            icon,
            color: Colors.grey[600],
            size: 20,
          ),
          const SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.caption.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: AppTextStyles.body2,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Show a dialog to download content
  Future<void> _showDownloadDialog() async {
    // This would be implemented to show a dialog to select content to download
    // For now, we'll just show a message
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
              'This feature is not yet implemented. Look for the download button when viewing content to save it for offline use.'),
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  /// Show a dialog to confirm removing content
  Future<void> _showRemoveDialog(OfflineContent content) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove from Offline Storage'),
        content: Text(
            'Are you sure you want to remove "${content.title}" from offline storage?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Remove'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      // Remove the content from offline storage
      await ref
          .read(offlineContentNotifierProvider.notifier)
          .removeOfflineContent(content.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${content.title} removed from offline storage'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  /// Retry syncing content
  Future<void> _retrySync(OfflineContent content) async {
    await ref
        .read(offlineContentNotifierProvider.notifier)
        .syncOfflineContent();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Sync started'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }
}
