import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';

import 'package:culture_connect/models/offline/device_state.dart';
import 'package:culture_connect/models/offline/storage_usage.dart';
import 'package:culture_connect/models/offline/bandwidth_usage.dart';
import 'package:culture_connect/models/offline/content_conflict.dart';
import 'package:culture_connect/models/offline/content_conflict_resolution.dart';
import 'package:culture_connect/models/offline/offline_settings.dart';
import 'package:culture_connect/providers/offline_mode_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/loading_indicator.dart';

/// A screen that displays a unified dashboard for offline content
class OfflineDashboardScreen extends ConsumerStatefulWidget {
  /// Creates a new offline dashboard screen
  const OfflineDashboardScreen({super.key});

  @override
  ConsumerState<OfflineDashboardScreen> createState() =>
      _OfflineDashboardScreenState();
}

class _OfflineDashboardScreenState extends ConsumerState<OfflineDashboardScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Offline Dashboard',
        showBackButton: true,
      ),
      body: Column(
        children: [
          _buildDeviceStateCard(),
          SizedBox(height: 16),
          TabBar(
            controller: _tabController,
            labelColor: AppTheme.primaryColor,
            unselectedLabelColor: Colors.grey,
            indicatorColor: AppTheme.primaryColor,
            tabs: const [
              Tab(text: 'Overview'),
              Tab(text: 'Storage'),
              Tab(text: 'Bandwidth'),
              Tab(text: 'Conflicts'),
            ],
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildStorageTab(),
                _buildBandwidthTab(),
                _buildConflictsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceStateCard() {
    final deviceStateAsync = ref.watch(deviceStateProvider);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: deviceStateAsync.when(
        data: (deviceState) => _DeviceStateCard(deviceState: deviceState),
        loading: () => const LoadingIndicator(),
        error: (error, stackTrace) => Text('Error: $error'),
      ),
    );
  }

  Widget _buildOverviewTab() {
    final offlineContent = ref.watch(offlineContentProvider);
    final storageUsageAsync = ref.watch(storageUsageProvider);
    final pendingConflicts = ref.watch(pendingConflictsProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Offline Content Overview',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildOverviewCard(
            title: 'Total Content Items',
            value: offlineContent.length.toString(),
            icon: Icons.storage,
            color: AppTheme.primaryColor,
          ),
          const SizedBox(height: 8),
          _buildOverviewCard(
            title: 'Pending Conflicts',
            value: pendingConflicts.length.toString(),
            icon: Icons.warning,
            color: Colors.orange,
          ),
          const SizedBox(height: 8),
          storageUsageAsync.when(
            data: (storageUsage) => _buildOverviewCard(
              title: 'Storage Used',
              value: '${storageUsage.usagePercentage * 100}%',
              subtitle:
                  '${storageUsage.formattedUsedSpace} of ${storageUsage.formattedTotalSpace}',
              icon: Icons.sd_storage,
              color: Colors.blue,
            ),
            loading: () => const LoadingIndicator(),
            error: (error, stackTrace) => Text('Error: $error'),
          ),
          const SizedBox(height: 16),
          const Text(
            'Quick Actions',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          _buildQuickActionButton(
            title: 'Sync All Content',
            icon: Icons.sync,
            onPressed: () {
              final service = ref.read(offlineModeServiceProvider);
              service.syncOfflineContent();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Syncing all content...')),
              );
            },
          ),
          SizedBox(height: 8),
          _buildQuickActionButton(
            title: 'Manage Offline Content',
            icon: Icons.folder,
            onPressed: () {
              Navigator.pushNamed(context, '/offline/content');
            },
          ),
          SizedBox(height: 8),
          _buildQuickActionButton(
            title: 'Offline Settings',
            icon: Icons.settings,
            onPressed: () {
              Navigator.pushNamed(context, '/offline/settings');
            },
          ),
        ],
      ),
    );
  }

  Widget _buildStorageTab() {
    final storageUsageAsync = ref.watch(storageUsageProvider);
    final offlineSettings = ref.watch(offlineSettingsProvider);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: storageUsageAsync.when(
        data: (storageUsage) => _StorageUsageView(
          storageUsage: storageUsage,
          offlineSettings: offlineSettings,
        ),
        loading: () => const Center(child: LoadingIndicator()),
        error: (error, stackTrace) => Center(child: Text('Error: $error')),
      ),
    );
  }

  Widget _buildBandwidthTab() {
    final bandwidthUsage = ref.watch(bandwidthUsageProvider);
    final offlineSettings = ref.watch(offlineSettingsProvider);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: _BandwidthUsageView(
        bandwidthUsage: bandwidthUsage,
        offlineSettings: offlineSettings,
      ),
    );
  }

  Widget _buildConflictsTab() {
    final pendingConflicts = ref.watch(pendingConflictsProvider);
    final offlineSettings = ref.watch(offlineSettingsProvider);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: _ConflictsView(
        conflicts: pendingConflicts,
        defaultResolution: offlineSettings.defaultConflictResolution,
      ),
    );
  }

  Widget _buildOverviewCard({
    required String title,
    required String value,
    String? subtitle,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withAlpha(25),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (subtitle != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton({
    required String title,
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon),
        label: Text(title),
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }
}

/// A widget that displays storage usage statistics with visual representations
class _StorageUsageView extends ConsumerWidget {
  /// The storage usage data
  final StorageUsage storageUsage;

  /// The offline settings
  final OfflineSettings offlineSettings;

  /// Creates a new storage usage view
  const _StorageUsageView({
    required this.storageUsage,
    required this.offlineSettings,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Create a list of content type storage usage objects
    final contentTypeUsages =
        storageUsage.usageByContentType.entries.map((entry) {
      return ContentTypeStorageUsage.fromContentType(
        contentType: entry.key,
        usedSpace: entry.value,
        totalUsedSpace: storageUsage.usedSpace,
      );
    }).toList();

    // Sort by usage (highest first)
    contentTypeUsages.sort((a, b) => b.usedSpace.compareTo(a.usedSpace));

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStorageOverviewCard(),
          const SizedBox(height: 16),
          _buildStoragePieChart(contentTypeUsages),
          const SizedBox(height: 24),
          const Text(
            'Storage by Content Type',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...contentTypeUsages.map((usage) => _buildContentTypeCard(usage)),
          const SizedBox(height: 24),
          _buildStorageManagementSection(),
        ],
      ),
    );
  }

  /// Builds the storage overview card
  Widget _buildStorageOverviewCard() {
    // Calculate storage usage percentage
    final usagePercentage = storageUsage.usagePercentage * 100;

    // Determine color based on usage
    Color statusColor;
    if (usagePercentage < 50) {
      statusColor = Colors.green;
    } else if (usagePercentage < 80) {
      statusColor = Colors.orange;
    } else {
      statusColor = Colors.red;
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Storage Overview',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor.withAlpha(25),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '${usagePercentage.toStringAsFixed(1)}% Used',
                    style: TextStyle(
                      fontSize: 12,
                      color: statusColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: LinearProgressIndicator(
                value: storageUsage.usagePercentage,
                backgroundColor: Colors.grey[200],
                valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                minHeight: 8,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Used: ${storageUsage.formattedUsedSpace}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  'Free: ${storageUsage.formattedFreeSpace}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  'Total: ${storageUsage.formattedTotalSpace}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the storage pie chart
  Widget _buildStoragePieChart(
      List<ContentTypeStorageUsage> contentTypeUsages) {
    // Create pie chart sections
    final sections = contentTypeUsages.map((usage) {
      return PieChartSectionData(
        value: usage.usedSpace.toDouble(),
        color: usage.color,
        title: usage.formattedPercentage,
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();

    // Add a section for free space if there's any content
    if (contentTypeUsages.isNotEmpty) {
      sections.add(
        PieChartSectionData(
          value: storageUsage.freeSpace.toDouble(),
          color: Colors.grey[300]!,
          title:
              '${((storageUsage.freeSpace / storageUsage.totalSpace) * 100).toStringAsFixed(1)}%',
          radius: 60,
          titleStyle: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
      );
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Storage Distribution',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: sections.isEmpty
                  ? Center(
                      child: Text(
                        'No offline content stored',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    )
                  : PieChart(
                      PieChartData(
                        sections: sections,
                        centerSpaceRadius: 40,
                        sectionsSpace: 2,
                        pieTouchData: PieTouchData(enabled: false),
                      ),
                    ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 16,
              runSpacing: 8,
              children: [
                ...contentTypeUsages.map((usage) => _buildLegendItem(usage)),
                _buildLegendItem(
                  ContentTypeStorageUsage(
                    contentType: 'free',
                    displayName: 'Free Space',
                    icon: Icons.space_bar,
                    color: Colors.grey[300]!,
                    usedSpace: storageUsage.freeSpace,
                    percentage:
                        storageUsage.freeSpace / storageUsage.totalSpace,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Builds a legend item for the pie chart
  Widget _buildLegendItem(ContentTypeStorageUsage usage) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: usage.color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          usage.displayName,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[800],
          ),
        ),
      ],
    );
  }

  /// Builds a card for a content type
  Widget _buildContentTypeCard(ContentTypeStorageUsage usage) {
    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: EdgeInsets.all(12),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: usage.color.withAlpha(25),
                shape: BoxShape.circle,
              ),
              child: Icon(
                usage.icon,
                color: usage.color,
                size: 20,
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    usage.displayName,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 4),
                  Row(
                    children: [
                      Expanded(
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(2),
                          child: LinearProgressIndicator(
                            value: usage.percentage,
                            backgroundColor: Colors.grey[200],
                            valueColor:
                                AlwaysStoppedAnimation<Color>(usage.color),
                            minHeight: 4,
                          ),
                        ),
                      ),
                      SizedBox(width: 8),
                      Text(
                        usage.formattedUsedSpace,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the storage management section
  Widget _buildStorageManagementSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Storage Management',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Consumer(
              builder: (context, ref, _) => _buildStorageSettingItem(
                icon: Icons.auto_delete,
                title: 'Auto-cleanup expired content',
                subtitle: 'Automatically remove content that has expired',
                value: offlineSettings.autoCleanupExpiredContent,
                onChanged: (value) {
                  // Update the setting
                  if (value != null) {
                    ref
                        .read(offlineSettingsNotifierProvider.notifier)
                        .setAutoCleanupExpiredContent(value);
                  }
                },
              ),
            ),
            Consumer(
              builder: (context, ref, _) => _buildStorageSettingItem(
                icon: Icons.storage,
                title: 'Auto-cleanup when storage is low',
                subtitle:
                    'Automatically remove low-priority content when storage is low',
                value: offlineSettings.autoCleanupWhenStorageLow,
                onChanged: (value) {
                  // Update the setting
                  if (value != null) {
                    ref
                        .read(offlineSettingsNotifierProvider.notifier)
                        .setAutoCleanupWhenStorageLow(value);
                  }
                },
              ),
            ),
            Consumer(
              builder: (context, ref, _) => _buildStorageSettingItem(
                icon: Icons.delete_sweep,
                title: 'Clear All Offline Content',
                subtitle: 'Remove all downloaded content to free up space',
                isButton: true,
                onPressed: () {
                  // Show confirmation dialog
                  showDialog(
                    context: context,
                    builder: (dialogContext) => AlertDialog(
                      title: const Text('Clear All Offline Content'),
                      content: const Text(
                        'Are you sure you want to remove all offline content? '
                        'This will free up space but you will need to re-download content when needed.',
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(dialogContext),
                          child: const Text('Cancel'),
                        ),
                        TextButton(
                          onPressed: () {
                            // Clear all offline content
                            // In a real implementation, we would get the service
                            // final service = refead(enhancedOfflineModeServiceProvider);
                            // In a real implementation, this would call a method to remove all content
                            // For now, we'll just show the snackbar
                            // serviceemoveAllOfflineContent();

                            // Close the dialog
                            Navigator.pop(dialogContext);

                            // Show a snackbar
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                  content: Text('All offline content cleared')),
                            );
                          },
                          child: const Text('Clear All'),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds a storage setting item
  Widget _buildStorageSettingItem({
    required IconData icon,
    required String title,
    required String subtitle,
    bool? value,
    ValueChanged<bool?>? onChanged,
    bool isButton = false,
    VoidCallback? onPressed,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withAlpha(25),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: AppTheme.primaryColor,
              size: 20,
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          if (isButton)
            TextButton(
              onPressed: onPressed,
              child: const Text('Clear'),
            )
          else
            Switch(
              value: value ?? false,
              onChanged: onChanged,
              activeColor: AppTheme.primaryColor,
            ),
        ],
      ),
    );
  }
}

/// A widget that displays bandwidth usage statistics and controls
class _BandwidthUsageView extends ConsumerWidget {
  /// The bandwidth usage data
  final DailyBandwidthUsage bandwidthUsage;

  /// The offline settings
  final OfflineSettings offlineSettings;

  /// Creates a new bandwidth usage view
  const _BandwidthUsageView({
    required this.bandwidthUsage,
    required this.offlineSettings,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBandwidthOverviewCard(),
          const SizedBox(height: 16),
          _buildNetworkTypeUsageCard(),
          const SizedBox(height: 16),
          _buildContentTypeUsageCard(),
          const SizedBox(height: 24),
          _buildBandwidthControlsCard(ref),
        ],
      ),
    );
  }

  /// Builds the bandwidth overview card
  Widget _buildBandwidthOverviewCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Today\'s Bandwidth Usage',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildBandwidthStat(
                  icon: Icons.download,
                  label: 'Downloaded',
                  value: bandwidthUsage.formattedTotalBytesDownloaded,
                  color: Colors.blue,
                ),
                _buildBandwidthStat(
                  icon: Icons.upload,
                  label: 'Uploaded',
                  value: bandwidthUsage.formattedTotalBytesUploaded,
                  color: Colors.green,
                ),
                _buildBandwidthStat(
                  icon: Icons.data_usage,
                  label: 'Total',
                  value: bandwidthUsage.formattedTotalBytes,
                  color: Colors.purple,
                ),
              ],
            ),
            if (offlineSettings.maxDailyBandwidthUsage > 0) ...[
              SizedBox(height: 16),
              Text(
                'Daily Limit: ${_formatBytes(offlineSettings.maxDailyBandwidthUsage)}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 8),
              ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: LinearProgressIndicator(
                  value: bandwidthUsage.totalBytes /
                      offlineSettings.maxDailyBandwidthUsage,
                  backgroundColor: Colors.grey[200],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    bandwidthUsage.totalBytes >
                            offlineSettings.maxDailyBandwidthUsage * 0.8
                        ? Colors.red
                        : Colors.blue,
                  ),
                  minHeight: 8,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Builds a bandwidth statistic
  Widget _buildBandwidthStat({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Expanded(
      child: Column(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withAlpha(25),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Builds the network type usage card
  Widget _buildNetworkTypeUsageCard() {
    // Get usage by network type
    final wifiUsage = bandwidthUsage.usageByNetworkType[NetworkType.wifi] ?? 0;
    final mobileUsage =
        bandwidthUsage.usageByNetworkType[NetworkType.mobile] ?? 0;
    final unknownUsage =
        bandwidthUsage.usageByNetworkType[NetworkType.unknown] ?? 0;

    // Calculate percentages
    final totalUsage = wifiUsage + mobileUsage + unknownUsage;
    final wifiPercentage = totalUsage > 0 ? wifiUsage / totalUsage : 0.0;
    final mobilePercentage = totalUsage > 0 ? mobileUsage / totalUsage : 0.0;
    final unknownPercentage = totalUsage > 0 ? unknownUsage / totalUsage : 0.0;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Usage by Network Type',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            if (totalUsage == 0)
              Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 16),
                  child: Text(
                    'No bandwidth usage recorded',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              )
            else
              Column(
                children: [
                  _buildNetworkTypeBar(
                    label: 'WiFi',
                    icon: Icons.wifi,
                    percentage: wifiPercentage,
                    value: _formatBytes(wifiUsage),
                    color: Colors.blue,
                  ),
                  SizedBox(height: 8),
                  _buildNetworkTypeBar(
                    label: 'Mobile',
                    icon: Icons.network_cell,
                    percentage: mobilePercentage,
                    value: _formatBytes(mobileUsage),
                    color: Colors.orange,
                  ),
                  if (unknownUsage > 0) ...[
                    SizedBox(height: 8),
                    _buildNetworkTypeBar(
                      label: 'Unknown',
                      icon: Icons.device_unknown,
                      percentage: unknownPercentage,
                      value: _formatBytes(unknownUsage),
                      color: Colors.grey,
                    ),
                  ],
                ],
              ),
          ],
        ),
      ),
    );
  }

  /// Builds a network type usage bar
  Widget _buildNetworkTypeBar({
    required String label,
    required IconData icon,
    required double percentage,
    required String value,
    required Color color,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: color,
          size: 16,
        ),
        SizedBox(width: 8),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[800],
          ),
        ),
        SizedBox(width: 8),
        Expanded(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(2),
            child: LinearProgressIndicator(
              value: percentage,
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(color),
              minHeight: 8,
            ),
          ),
        ),
        SizedBox(width: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// Builds the content type usage card
  Widget _buildContentTypeUsageCard() {
    // Get top content types by usage
    final sortedContentTypes = bandwidthUsage.usageByContentType.entries
        .toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    // Take top 5 content types
    final topContentTypes = sortedContentTypes.take(5).toList();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Top Content Types',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            if (topContentTypes.isEmpty)
              Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 16),
                  child: Text(
                    'No content usage recorded',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              )
            else
              Column(
                children: topContentTypes.map((entry) {
                  // Get content type info
                  final contentTypeUsage =
                      ContentTypeStorageUsage.fromContentType(
                    contentType: entry.key,
                    usedSpace: entry.value,
                    totalUsedSpace: bandwidthUsage.totalBytes,
                  );

                  return Padding(
                    padding: EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: contentTypeUsage.color.withAlpha(25),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            contentTypeUsage.icon,
                            color: contentTypeUsage.color,
                            size: 12,
                          ),
                        ),
                        SizedBox(width: 8),
                        Text(
                          contentTypeUsage.displayName,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[800],
                          ),
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(2),
                            child: LinearProgressIndicator(
                              value: contentTypeUsage.percentage,
                              backgroundColor: Colors.grey[200],
                              valueColor: AlwaysStoppedAnimation<Color>(
                                  contentTypeUsage.color),
                              minHeight: 4,
                            ),
                          ),
                        ),
                        SizedBox(width: 8),
                        Text(
                          _formatBytes(entry.value),
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }

  /// Builds the bandwidth controls card
  Widget _buildBandwidthControlsCard(WidgetRef ref) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Bandwidth Controls',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            _buildBandwidthSettingItem(
              icon: Icons.wifi,
              title: 'Sync only on WiFi',
              subtitle: 'Only download content when connected to WiFi',
              value: offlineSettings.syncOnlyOnWifi,
              onChanged: (value) {
                if (value != null) {
                  ref
                      .read(offlineSettingsNotifierProvider.notifier)
                      .setSyncOnlyOnWifi(value);
                }
              },
            ),
            _buildBandwidthSettingItem(
              icon: Icons.battery_charging_full,
              title: 'Sync only when charging',
              subtitle: 'Only download content when device is charging',
              value: offlineSettings.syncOnlyWhenCharging,
              onChanged: (value) {
                if (value != null) {
                  ref
                      .read(offlineSettingsNotifierProvider.notifier)
                      .setSyncOnlyWhenCharging(value);
                }
              },
            ),
            _buildBandwidthLimitSetting(ref),
          ],
        ),
      ),
    );
  }

  /// Builds a bandwidth setting item
  Widget _buildBandwidthSettingItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool?> onChanged,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withAlpha(25),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: AppTheme.primaryColor,
              size: 20,
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.primaryColor,
          ),
        ],
      ),
    );
  }

  /// Builds the bandwidth limit setting
  Widget _buildBandwidthLimitSetting(WidgetRef ref) {
    // Define bandwidth limit options
    final limitOptions = [
      0, // Unlimited
      100 * 1024 * 1024, // 100 MB
      250 * 1024 * 1024, // 250 MB
      500 * 1024 * 1024, // 500 MB
      1024 * 1024 * 1024, // 1 GB
    ];

    // Get the current limit
    final currentLimit = offlineSettings.maxDailyBandwidthUsage;

    // Find the closest option
    final selectedOption = limitOptions.contains(currentLimit)
        ? currentLimit
        : limitOptions.reduce((a, b) =>
            (a - currentLimit).abs() < (b - currentLimit).abs() ? a : b);

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withAlpha(25),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.data_usage,
              color: AppTheme.primaryColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Daily Bandwidth Limit',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Limit the amount of data used for syncing each day',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(height: 8),
                DropdownButtonFormField<int>(
                  value: selectedOption,
                  decoration: InputDecoration(
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  items: limitOptions.map((limit) {
                    return DropdownMenuItem<int>(
                      value: limit,
                      child: Text(
                        limit == 0 ? 'Unlimited' : _formatBytes(limit),
                        style: TextStyle(fontSize: 14),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      ref
                          .read(offlineSettingsNotifierProvider.notifier)
                          .setMaxDailyBandwidthUsage(value);
                    }
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Format bytes to a human-readable string
  String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      final kb = bytes / 1024;
      return '${kb.toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      final mb = bytes / (1024 * 1024);
      return '${mb.toStringAsFixed(1)} MB';
    } else {
      final gb = bytes / (1024 * 1024 * 1024);
      return '${gb.toStringAsFixed(1)} GB';
    }
  }
}

/// A widget that displays content conflicts and provides resolution options
class _ConflictsView extends ConsumerWidget {
  /// The list of content conflicts
  final List<ContentConflict> conflicts;

  /// The default conflict resolution strategy
  final ContentConflictResolution defaultResolution;

  /// Creates a new conflicts view
  const _ConflictsView({
    required this.conflicts,
    required this.defaultResolution,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return conflicts.isEmpty
        ? _buildEmptyState()
        : SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildConflictSummary(),
                SizedBox(height: 16),
                ...conflicts.map(
                    (conflict) => _buildConflictCard(context, ref, conflict)),
              ],
            ),
          );
  }

  /// Builds the empty state when there are no conflicts
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle_outline,
            size: 64,
            color: Colors.green,
          ),
          SizedBox(height: 16),
          Text(
            'No Conflicts',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'All content is in sync',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the conflict summary
  Widget _buildConflictSummary() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Conflict Summary',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            Row(
              children: [
                _buildConflictStat(
                  count: conflicts.length,
                  label: 'Total Conflicts',
                  icon: Icons.warning,
                  color: Colors.orange,
                ),
                _buildConflictStat(
                  count: conflicts.where((c) => c.isPending).length,
                  label: 'Pending',
                  icon: Icons.pending,
                  color: Colors.red,
                ),
                _buildConflictStat(
                  count: conflicts.where((c) => c.isResolved).length,
                  label: 'Resolved',
                  icon: Icons.check_circle,
                  color: Colors.green,
                ),
              ],
            ),
            SizedBox(height: 16),
            Text(
              'Default Resolution: ${_getResolutionDisplayName(defaultResolution)}',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds a conflict statistic
  Widget _buildConflictStat({
    required int count,
    required String label,
    required IconData icon,
    required Color color,
  }) {
    return Expanded(
      child: Column(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withAlpha(25),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            count.toString(),
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds a card for a content conflict
  Widget _buildConflictCard(
      BuildContext context, WidgetRef ref, ContentConflict conflict) {
    // Get the status color
    Color statusColor;
    switch (conflict.status) {
      case ContentConflictStatus.pending:
        statusColor = Colors.orange;
        break;
      case ContentConflictStatus.resolved:
        statusColor = Colors.green;
        break;
      case ContentConflictStatus.deferred:
        statusColor = Colors.grey;
        break;
    }

    return Card(
      elevation: 2,
      margin: EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: statusColor.withAlpha(25),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    conflict.status.icon,
                    color: statusColor,
                    size: 20,
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        conflict.contentTitle,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'Type: ${_getContentTypeDisplayName(conflict.contentType)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor.withAlpha(25),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    _getStatusDisplayName(conflict.status),
                    style: TextStyle(
                      fontSize: 12,
                      color: statusColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildConflictDetails(conflict),
            if (conflict.isPending) ...[
              const SizedBox(height: 16),
              _buildResolutionOptions(context, ref, conflict),
            ] else if (conflict.isResolved && conflict.resolution != null) ...[
              const SizedBox(height: 16),
              _buildResolutionInfo(conflict),
            ],
          ],
        ),
      ),
    );
  }

  /// Builds the conflict details
  Widget _buildConflictDetails(ContentConflict conflict) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Conflict Details',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8),
        _buildDetailRow(
          label: 'Server Version',
          value: conflict.serverVersion.toString(),
          icon: Icons.cloud,
        ),
        SizedBox(height: 4),
        _buildDetailRow(
          label: 'Client Version',
          value: conflict.clientVersion.toString(),
          icon: Icons.smartphone,
        ),
        SizedBox(height: 4),
        _buildDetailRow(
          label: 'Server Modified',
          value: conflict.formattedServerModifiedTime,
          icon: Icons.update,
        ),
        SizedBox(height: 4),
        _buildDetailRow(
          label: 'Client Modified',
          value: conflict.formattedClientModifiedTime,
          icon: Icons.edit,
        ),
      ],
    );
  }

  /// Builds a detail row
  Widget _buildDetailRow({
    required String label,
    required String value,
    required IconData icon,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey[600],
        ),
        SizedBox(width: 8),
        Text(
          '$label:',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        SizedBox(width: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// Builds the resolution options
  Widget _buildResolutionOptions(
      BuildContext context, WidgetRef ref, ContentConflict conflict) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Resolution Options',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _resolveConflict(
                  context,
                  ref,
                  conflict,
                  ContentConflictResolution.serverWins,
                ),
                icon: const Icon(Icons.cloud_download),
                label: const Text('Use Server'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                ),
              ),
            ),
            SizedBox(width: 8),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _resolveConflict(
                  context,
                  ref,
                  conflict,
                  ContentConflictResolution.clientWins,
                ),
                icon: const Icon(Icons.cloud_upload),
                label: const Text('Use Local'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _resolveConflict(
                  context,
                  ref,
                  conflict,
                  ContentConflictResolution.merge,
                ),
                icon: const Icon(Icons.merge_type),
                label: const Text('Merge'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                ),
              ),
            ),
            SizedBox(width: 8),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _resolveConflict(
                  context,
                  ref,
                  conflict,
                  ContentConflictResolution
                      .askUser, // Use askUser instead of defer
                ),
                icon: const Icon(Icons.schedule),
                label: const Text('Defer'),
                style: OutlinedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 8),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Builds the resolution info
  Widget _buildResolutionInfo(ContentConflict conflict) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Resolution',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8),
        _buildDetailRow(
          label: 'Strategy',
          value: _getResolutionDisplayName(conflict.resolution!),
          icon: Icons.check_circle,
        ),
        if (conflict.resolutionTime != null) ...[
          SizedBox(height: 4),
          _buildDetailRow(
            label: 'Resolved At',
            value: conflict.formattedResolutionTime!,
            icon: Icons.access_time,
          ),
        ],
      ],
    );
  }

  /// Resolves a conflict
  void _resolveConflict(
    BuildContext context,
    WidgetRef ref,
    ContentConflict conflict,
    ContentConflictResolution resolution,
  ) {
    // In a real implementation, this would call a method to resolve the conflict
    // For now, we'll just show a snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Conflict resolved with strategy: ${_getResolutionDisplayName(resolution)}',
        ),
      ),
    );
  }

  /// Gets the display name for a content type
  String _getContentTypeDisplayName(String contentType) {
    switch (contentType) {
      case 'itinerary':
        return 'Itinerary';
      case 'experience':
        return 'Experience';
      case 'hotel':
        return 'Hotel';
      case 'flight':
        return 'Flight';
      case 'restaurant':
        return 'Restaurant';
      case 'car_rental':
        return 'Car Rental';
      case 'cruise':
        return 'Cruise';
      case 'ar_content':
        return 'AR Content';
      case 'timeline':
        return 'Timeline';
      default:
        return contentType;
    }
  }

  /// Gets the display name for a conflict status
  String _getStatusDisplayName(ContentConflictStatus status) {
    switch (status) {
      case ContentConflictStatus.pending:
        return 'Pending';
      case ContentConflictStatus.resolved:
        return 'Resolved';
      case ContentConflictStatus.deferred:
        return 'Deferred';
    }
  }

  /// Gets the display name for a conflict resolution
  String _getResolutionDisplayName(ContentConflictResolution resolution) {
    switch (resolution) {
      case ContentConflictResolution.serverWins:
        return 'Server Version';
      case ContentConflictResolution.clientWins:
        return 'Local Version';
      case ContentConflictResolution.merge:
        return 'Merged';
      // No defer option in the enum, using askUser for this functionality
      case ContentConflictResolution.askUser:
        return 'Ask User';
    }
  }
}

/// A card that displays the device state
class _DeviceStateCard extends StatelessWidget {
  /// The device state
  final DeviceState deviceState;

  /// Creates a new device state card
  const _DeviceStateCard({required this.deviceState});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Device Status',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: deviceState.isConnected
                        ? Colors.green.withAlpha(25)
                        : Colors.red.withAlpha(25),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        deviceState.isConnected ? Icons.wifi : Icons.wifi_off,
                        size: 16,
                        color:
                            deviceState.isConnected ? Colors.green : Colors.red,
                      ),
                      SizedBox(width: 4),
                      Text(
                        deviceState.isConnected ? 'Online' : 'Offline',
                        style: TextStyle(
                          fontSize: 12,
                          color: deviceState.isConnected
                              ? Colors.green
                              : Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildStatusItem(
                  icon: deviceState.networkTypeIcon,
                  label: 'Network',
                  value: deviceState.networkTypeDisplayName,
                  color: deviceState.networkTypeColor,
                ),
                _buildStatusItem(
                  icon: deviceState.batteryIcon,
                  label: 'Battery',
                  value: deviceState.formattedBatteryLevel,
                  color: deviceState.batteryColor,
                ),
                _buildStatusItem(
                  icon: Icons.sd_storage,
                  label: 'Storage',
                  value: deviceState.formattedAvailableStorage,
                  color: Colors.blue,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Expanded(
      child: Column(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withAlpha(25),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
