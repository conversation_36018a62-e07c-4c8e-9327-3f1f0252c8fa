import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:culture_connect/models/safety_model.dart';
import 'package:culture_connect/services/location_service.dart';
import 'package:culture_connect/services/safety_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';

class SafeZonesScreen extends ConsumerStatefulWidget {
  const SafeZonesScreen({super.key});

  @override
  ConsumerState<SafeZonesScreen> createState() => _SafeZonesScreenState();
}

class _SafeZonesScreenState extends ConsumerState<SafeZonesScreen> {
  final Completer<GoogleMapController> _mapController = Completer();
  Set<Marker> _markers = {};
  Set<Circle> _circles = {};
  List<SafeZone> _safeZones = [];
  bool _isLoading = true;
  String? _error;
  LatLng? _currentLocation;
  SafeZone? _selectedSafeZone;
  bool _isMapReady = false;

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();
  }

  Future<void> _getCurrentLocation() async {
    try {
      final position =
          await ref.read(locationServiceProvider).getCurrentPosition();
      setState(() {
        _currentLocation = LatLng(position.latitude, position.longitude);
      });
      _loadSafeZones();
    } catch (e) {
      setState(() {
        _error = 'Could not get current location: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadSafeZones() async {
    if (_currentLocation == null) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final safeZones = await ref
          .read(safetyServiceProvider)
          .getSafeZones(_currentLocation!, 5.0); // 5km radius

      setState(() {
        _safeZones = safeZones;
        _isLoading = false;
      });

      _updateMapMarkers();
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _updateMapMarkers() {
    if (!_isMapReady) return;

    final markers = <Marker>{};
    final circles = <Circle>{};

    // Add current location marker
    if (_currentLocation != null) {
      markers.add(
        Marker(
          markerId: const MarkerId('current_location'),
          position: _currentLocation!,
          icon:
              BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure),
          infoWindow: const InfoWindow(title: 'Your Location'),
        ),
      );
    }

    // Add safe zone markers and circles
    for (final safeZone in _safeZones) {
      markers.add(
        Marker(
          markerId: MarkerId(safeZone.id),
          position: safeZone.location,
          icon:
              BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
          infoWindow: InfoWindow(title: safeZone.name),
          onTap: () {
            setState(() {
              _selectedSafeZone = safeZone;
            });
          },
        ),
      );

      circles.add(
        Circle(
          circleId: CircleId(safeZone.id),
          center: safeZone.location,
          radius: safeZone.radius,
          fillColor: Colors.green.withAlpha(51), // 0.2 * 255 = 51
          strokeColor: Colors.green,
          strokeWidth: 1,
        ),
      );
    }

    setState(() {
      _markers = markers;
      _circles = circles;
    });
  }

  Future<void> _animateToLocation(LatLng location) async {
    final controller = await _mapController.future;
    controller.animateCamera(CameraUpdate.newLatLngZoom(location, 15));
  }

  Future<void> _openDirections(LatLng destination) async {
    if (_currentLocation == null) return;

    final origin =
        '${_currentLocation!.latitude},${_currentLocation!.longitude}';
    final dest = '${destination.latitude},${destination.longitude}';
    final url =
        'https://www.google.com/maps/dir/?api=1&origin=$origin&destination=$dest&travelmode=walking';

    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not open directions'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _callSafeZone(String phoneNumber) async {
    final Uri uri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not launch phone app'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _openWebsite(String website) async {
    final Uri uri = Uri.parse(website);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not open website'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Safe Zones',
        showBackButton: true,
      ),
      body: Stack(
        children: [
          // Map
          _buildMap(),

          // Loading indicator
          if (_isLoading)
            Container(
              color:
                  Colors.black.withAlpha(77), // 0.3 * 255 = 76.5, rounded to 77
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),

          // Error message
          if (_error != null)
            Container(
              color: Colors.black
                  .withAlpha(179), // 0.7 * 255 = 178.5, rounded to 179
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: Colors.white,
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Error',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _error!,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _error = null;
                        });
                        _getCurrentLocation();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: AppTheme.primaryColor,
                      ),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            ),

          // Selected safe zone details
          if (_selectedSafeZone != null) _buildSafeZoneDetails(),

          // List toggle button
          Positioned(
            top: 16,
            right: 16,
            child: FloatingActionButton(
              heroTag: 'list_button',
              mini: true,
              backgroundColor: Colors.white,
              onPressed: _showSafeZonesList,
              child: const Icon(
                Icons.list,
                color: AppTheme.primaryColor,
              ),
            ),
          ),

          // Current location button
          if (_currentLocation != null)
            Positioned(
              bottom: _selectedSafeZone != null ? 280 : 16,
              right: 16,
              child: FloatingActionButton(
                heroTag: 'location_button',
                mini: true,
                backgroundColor: Colors.white,
                onPressed: () => _animateToLocation(_currentLocation!),
                child: const Icon(
                  Icons.my_location,
                  color: AppTheme.primaryColor,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMap() {
    if (_currentLocation == null) {
      return const Center(child: Text('Getting your location...'));
    }

    return GoogleMap(
      initialCameraPosition: CameraPosition(
        target: _currentLocation!,
        zoom: 14,
      ),
      markers: _markers,
      circles: _circles,
      myLocationEnabled: true,
      myLocationButtonEnabled: false,
      zoomControlsEnabled: false,
      mapToolbarEnabled: false,
      onMapCreated: (controller) {
        _mapController.complete(controller);
        setState(() {
          _isMapReady = true;
        });
        _updateMapMarkers();
      },
    );
  }

  Widget _buildSafeZoneDetails() {
    final safeZone = _selectedSafeZone!;

    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color:
                  Colors.black.withAlpha(26), // 0.1 * 255 = 25.5, rounded to 26
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with close button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  safeZone.name,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () {
                    setState(() {
                      _selectedSafeZone = null;
                    });
                  },
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Description
            Text(
              safeZone.description,
              style: const TextStyle(
                fontSize: 16,
                color: AppTheme.textSecondaryColor,
              ),
            ),

            const SizedBox(height: 16),

            // Address
            if (safeZone.address != null)
              _buildInfoRow(
                icon: Icons.location_on,
                text: safeZone.address!,
              ),

            // Phone
            if (safeZone.phoneNumber != null)
              _buildInfoRow(
                icon: Icons.phone,
                text: safeZone.phoneNumber!,
                onTap: () => _callSafeZone(safeZone.phoneNumber!),
              ),

            // Website
            if (safeZone.website != null)
              _buildInfoRow(
                icon: Icons.language,
                text: safeZone.website!,
                onTap: () => _openWebsite(safeZone.website!),
              ),

            const SizedBox(height: 16),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _animateToLocation(safeZone.location),
                    icon: const Icon(Icons.center_focus_strong),
                    label: const Text('Show on Map'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _openDirections(safeZone.location),
                    icon: const Icon(Icons.directions),
                    label: const Text('Directions'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String text,
    VoidCallback? onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onTap,
        child: Row(
          children: [
            Icon(
              icon,
              size: 18,
              color: AppTheme.textSecondaryColor,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                text,
                style: TextStyle(
                  fontSize: 16,
                  color: onTap != null
                      ? AppTheme.primaryColor
                      : AppTheme.textPrimaryColor,
                  decoration: onTap != null ? TextDecoration.underline : null,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSafeZonesList() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              margin: const EdgeInsets.symmetric(vertical: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Title
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Nearby Safe Zones',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
            ),

            // List
            Expanded(
              child: _safeZones.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.location_off,
                            size: 64,
                            color: Colors.grey[300],
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'No safe zones found nearby',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.textPrimaryColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Try expanding your search area',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: _safeZones.length,
                      itemBuilder: (context, index) {
                        final safeZone = _safeZones[index];
                        return _buildSafeZoneListItem(safeZone);
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSafeZoneListItem(SafeZone safeZone) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          Navigator.pop(context);
          setState(() {
            _selectedSafeZone = safeZone;
          });
          _animateToLocation(safeZone.location);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              CircleAvatar(
                backgroundColor: Colors.green
                    .withAlpha(26), // 0.1 * 255 = 25.5, rounded to 26
                child: const Icon(
                  Icons.shield,
                  color: Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      safeZone.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    if (safeZone.address != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        safeZone.address!,
                        style: const TextStyle(
                          fontSize: 14,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              const Icon(
                Icons.chevron_right,
                color: AppTheme.textSecondaryColor,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
