import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lottie/lottie.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/startup_optimization_provider.dart';
import 'package:culture_connect/services/auth_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'onboarding_screen.dart';
import 'main_navigation.dart';
import 'verification_screen.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isInitialized = false;
  bool _animationCompleted = false;
  bool _hasNavigated = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    // Start the animation
    _animationController.forward();

    // Add a listener to mark animation as completed
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _animationCompleted = true;
        });
      }
    });

    // Add a timeout fallback to prevent infinite loading
    Future.delayed(const Duration(seconds: 10), () {
      if (!_hasNavigated && mounted) {
        // Force navigation to onboarding if still stuck after 10 seconds
        _navigateBasedOnAuthStatus(AuthStatus.unauthenticated);
      }
    });
  }

  void _navigateBasedOnAuthStatus(AuthStatus authStatus) {
    // Prevent multiple navigations
    if (_hasNavigated || !mounted) return;

    switch (authStatus) {
      case AuthStatus.authenticated:
        _hasNavigated = true;
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const MainNavigation()),
        );
        break;
      case AuthStatus.verificationPending:
        _hasNavigated = true;
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const VerificationScreen()),
        );
        break;
      case AuthStatus.unauthenticated:
        _hasNavigated = true;
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const OnboardingScreen()),
        );
        break;
      case AuthStatus.initial:
        // Wait for the next state update
        break;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get initialization state
    final initState = ref.watch(appInitializationStateProvider);

    // Listen to initialization state changes
    ref.listen(appInitializationStateProvider, (previous, next) {
      if (next.isInitialized && _animationCompleted && !_isInitialized) {
        setState(() {
          _isInitialized = true;
        });
      } else if (next.error != null) {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Initialization error: ${next.error}'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: () {
                ref
                    .read(appInitializationStateProvider.notifier)
                    .retryInitialization();
              },
            ),
          ),
        );
      }
    });

    // Listen to auth state changes when initialized
    ref.listen(authStatusProvider, (previous, next) {
      if (_animationCompleted && initState.isInitialized && !_hasNavigated) {
        next.when(
          data: (authStatus) {
            // Add a small delay to ensure UI is ready
            Future.delayed(const Duration(milliseconds: 100), () {
              if (!_hasNavigated && mounted) {
                _navigateBasedOnAuthStatus(authStatus);
              }
            });
          },
          loading: () {
            // Show loading indicator - but don't wait indefinitely
            Future.delayed(const Duration(seconds: 5), () {
              if (!_hasNavigated && mounted) {
                // Force navigation to onboarding if auth is still loading after 5 seconds
                _navigateBasedOnAuthStatus(AuthStatus.unauthenticated);
              }
            });
          },
          error: (error, stackTrace) {
            // Show error message and navigate to onboarding
            if (!_hasNavigated && mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Authentication error: $error'),
                  backgroundColor: Colors.red,
                ),
              );
              _navigateBasedOnAuthStatus(AuthStatus.unauthenticated);
            }
          },
        );
      }
    });

    return Scaffold(
      backgroundColor: AppTheme.primaryColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App logo or animation
            SizedBox(
              width: 200,
              height: 200,
              child: Lottie.asset(
                'assets/animations/splash_animation.json',
                controller: _animationController,
                fit: BoxFit.contain,
              ),
            ),
            const SizedBox(height: 24),

            // App name
            const Text(
              'CultureConnect',
              style: TextStyle(
                color: Colors.white,
                fontSize: 32,
                fontWeight: FontWeight.bold,
                letterSpacing: 1.2,
              ),
            ),

            const SizedBox(height: 8),

            // Tagline
            Text(
              'Connect with authentic cultural experiences',
              style: TextStyle(
                color: Colors.white.withAlpha(204), // 0.8 opacity
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 32),

            // Initialization status
            if (initState.error != null)
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 32, vertical: 8),
                child: Text(
                  'Error: ${initState.error}',
                  style: TextStyle(
                    color: Colors.red[300],
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

            const SizedBox(height: 16),

            // Loading indicator with progress
            SizedBox(
              width: 200,
              child: Column(
                children: [
                  // Progress indicator
                  LinearProgressIndicator(
                    value: initState.progress,
                    backgroundColor: Colors.white.withAlpha(51),
                    valueColor:
                        const AlwaysStoppedAnimation<Color>(Colors.white),
                    borderRadius: BorderRadius.circular(8),
                  ),

                  const SizedBox(height: 8),

                  // Progress text
                  Text(
                    'Loading... ${(initState.progress * 100).toInt()}%',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
