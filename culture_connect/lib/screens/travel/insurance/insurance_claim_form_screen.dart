import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

// Package imports
import 'package:image_picker/image_picker.dart';

// Project imports
import 'package:culture_connect/models/travel/insurance/insurance.dart'
    hide InsuranceCoverageType, InsuranceClaimStatus;
import 'package:culture_connect/models/travel/insurance/insurance_coverage_type.dart';
import 'package:culture_connect/providers/travel/insurance/insurance_providers.dart';

import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';

/// A screen for filing an insurance claim
class InsuranceClaimFormScreen extends ConsumerStatefulWidget {
  /// The ID of the policy to file a claim for (optional)
  final String? policyId;

  /// Creates a new insurance claim form screen
  const InsuranceClaimFormScreen({
    super.key,
    this.policyId,
  });

  @override
  ConsumerState<InsuranceClaimFormScreen> createState() =>
      _InsuranceClaimFormScreenState();
}

class _InsuranceClaimFormScreenState
    extends ConsumerState<InsuranceClaimFormScreen> {
  // Form key
  final _formKey = GlobalKey<FormState>();

  // Form data
  String? _selectedPolicyId;
  InsuranceCoverageType? _selectedCoverageType;
  DateTime _incidentDate = DateTime.now();
  final _incidentDescriptionController = TextEditingController();
  final _incidentLocationController = TextEditingController();
  final _claimAmountController = TextEditingController();
  String _currency = 'USD';
  final List<File> _documents = [];

  // Loading state
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();

    // Initialize selected policy ID if provided
    _selectedPolicyId = widget.policyId;
  }

  @override
  void dispose() {
    _incidentDescriptionController.dispose();
    _incidentLocationController.dispose();
    _claimAmountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final activePoliciesAsync = ref.watch(activePoliciesProvider);

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'File a Claim',
        showBackButton: true,
      ),
      body: activePoliciesAsync.when(
        data: (policies) {
          if (policies.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.policy_outlined,
                    size: 64,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No active policies found',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'You need an active policy to file a claim.',
                    style: theme.textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pushNamed(context, '/travel/insurance/search');
                    },
                    icon: const Icon(Icons.search),
                    label: const Text('Find Insurance'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                    ),
                  ),
                ],
              ),
            );
          }

          // If policyId is provided but not in the list of active policies, show error
          if (_selectedPolicyId != null &&
              !policies.any((p) => p.id == _selectedPolicyId)) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: theme.colorScheme.error,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Policy not found',
                    style: theme.textTheme.titleMedium,
                  ),
                  SizedBox(height: 8),
                  Text(
                    'The selected policy is not active or does not exist.',
                    style: theme.textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: const Text('Go Back'),
                  ),
                ],
              ),
            );
          }

          // If policyId is not provided and there's only one active policy, select it
          if (_selectedPolicyId == null && policies.length == 1) {
            _selectedPolicyId = policies.first.id;
          }

          return _buildClaimForm(theme, policies);
        },
        loading: () => const Center(
          child: LoadingIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: ErrorView(
            error: error.toString(),
            onRetry: () => ref.refresh(activePoliciesProvider),
          ),
        ),
      ),
    );
  }

  Widget _buildClaimForm(ThemeData theme, List<InsurancePolicy> policies) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Policy selector
            Text(
              'Select Policy',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'Policy',
                border: OutlineInputBorder(),
              ),
              value: _selectedPolicyId,
              items: policies.map((policy) {
                return DropdownMenuItem<String>(
                  value: policy.id,
                  child: Text('${policy.name} (${policy.policyNumber})'),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedPolicyId = value;
                  _selectedCoverageType =
                      null; // Reset coverage type when policy changes
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please select a policy';
                }
                return null;
              },
            ),
            SizedBox(height: 16),

            // Coverage type selector
            if (_selectedPolicyId != null) ...[
              Text(
                'Claim Type',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 8),
              _buildCoverageTypeSelector(theme, policies),
              SizedBox(height: 16),
            ],

            // Incident details
            Text(
              'Incident Details',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Incident Date',
                border: OutlineInputBorder(),
                suffixIcon: Icon(Icons.calendar_today),
              ),
              readOnly: true,
              initialValue: DateFormat('MMM d, yyyy').format(_incidentDate),
              onTap: () => _selectIncidentDate(context),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please select an incident date';
                }
                return null;
              },
            ),
            SizedBox(height: 16),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Incident Location',
                border: OutlineInputBorder(),
              ),
              controller: _incidentLocationController,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter the incident location';
                }
                return null;
              },
            ),
            SizedBox(height: 16),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Incident Description',
                border: OutlineInputBorder(),
                alignLabelWithHint: true,
              ),
              controller: _incidentDescriptionController,
              maxLines: 5,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a description of the incident';
                }
                return null;
              },
            ),
            SizedBox(height: 16),

            // Claim amount
            Text(
              'Claim Amount',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Amount',
                      border: OutlineInputBorder(),
                      prefixText: '\$',
                    ),
                    controller: _claimAmountController,
                    keyboardType:
                        const TextInputType.numberWithOptions(decimal: true),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a claim amount';
                      }
                      if (double.tryParse(value) == null) {
                        return 'Please enter a valid number';
                      }
                      if (double.parse(value) <= 0) {
                        return 'Amount must be greater than zero';
                      }
                      return null;
                    },
                  ),
                ),
                SizedBox(width: 16),
                SizedBox(
                  width: 100,
                  child: DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Currency',
                      border: OutlineInputBorder(),
                    ),
                    value: _currency,
                    items: ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD']
                        .map((currency) {
                      return DropdownMenuItem<String>(
                        value: currency,
                        child: Text(currency),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _currency = value;
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),

            // Supporting documents
            Text(
              'Supporting Documents',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Please upload any documents that support your claim (receipts, medical reports, police reports, etc.)',
              style: theme.textTheme.bodyMedium,
            ),
            SizedBox(height: 8),
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: () => _pickDocument(ImageSource.camera),
                  icon: const Icon(Icons.camera_alt),
                  label: const Text('Take Photo'),
                ),
                SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: () => _pickDocument(ImageSource.gallery),
                  icon: const Icon(Icons.photo_library),
                  label: const Text('Upload File'),
                ),
              ],
            ),
            SizedBox(height: 8),
            if (_documents.isNotEmpty) ...[
              Text(
                'Uploaded Documents (${_documents.length})',
                style: theme.textTheme.titleSmall,
              ),
              SizedBox(height: 8),
              SizedBox(
                height: 100,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _documents.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: EdgeInsets.only(right: 8),
                      child: Stack(
                        children: [
                          Container(
                            width: 100,
                            height: 100,
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: theme.colorScheme.outline,
                              ),
                              borderRadius: BorderRadius.circular(8),
                              image: DecorationImage(
                                image: FileImage(_documents[index]),
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                          Positioned(
                            top: 4,
                            right: 4,
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  _documents.removeAt(index);
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color: Colors.black
                                      .withAlpha(128), // 0.5 * 255 = 128
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.close,
                                  size: 16,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ] else ...[
              Text(
                'No documents uploaded yet',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.error,
                ),
              ),
            ],
            SizedBox(height: 24),

            // Error message
            if (_error != null)
              Padding(
                padding: EdgeInsets.only(bottom: 16),
                child: Text(
                  _error!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.error,
                  ),
                ),
              ),

            // Submit button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _submitClaim,
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                  padding: EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isLoading
                    ? SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: theme.colorScheme.onPrimary,
                        ),
                      )
                    : const Text('Submit Claim'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCoverageTypeSelector(
      ThemeData theme, List<InsurancePolicy> policies) {
    // Find the selected policy
    final selectedPolicy = policies.firstWhere(
      (policy) => policy.id == _selectedPolicyId,
      orElse: () => policies.first,
    );

    // Get available coverage types from the policy
    final availableCoverageTypes = selectedPolicy.coverages
        .where((coverage) => coverage.isIncluded)
        .map((coverage) => coverage.type)
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: availableCoverageTypes.map((type) {
            final isSelected = _selectedCoverageType == type;
            return ChoiceChip(
              label: Text(type.displayName),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedCoverageType = selected ? type : null;
                });
              },
              avatar: Icon(
                type.icon,
                size: 16,
                color: isSelected
                    ? theme.colorScheme.onPrimary
                    : theme.colorScheme.primary,
              ),
            );
          }).toList(),
        ),
        if (_selectedCoverageType == null)
          Padding(
            padding: EdgeInsets.only(top: 8),
            child: Text(
              'Please select a claim type',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.error,
              ),
            ),
          ),
      ],
    );
  }

  Future<void> _selectIncidentDate(BuildContext context) async {
    final initialDate = _incidentDate;
    final firstDate = DateTime.now().subtract(const Duration(days: 365));
    final lastDate = DateTime.now();

    final newDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
    );

    if (newDate != null && mounted) {
      setState(() {
        _incidentDate = newDate;
      });
    }
  }

  Future<void> _pickDocument(ImageSource source) async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: source);

      if (pickedFile != null && mounted) {
        setState(() {
          _documents.add(File(pickedFile.path));
        });
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _error = 'Error picking document: $e';
      });
      debugPrint('Error picking document: $e');
    }
  }

  Future<void> _submitClaim() async {
    // Validate form
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Check if coverage type is selected
    if (_selectedCoverageType == null) {
      setState(() {
        _error = 'Please select a claim type';
      });
      return;
    }

    // Check if documents are uploaded
    if (_documents.isEmpty) {
      setState(() {
        _error = 'Please upload at least one supporting document';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // File claim
      final insuranceService = ref.read(insuranceServiceProvider);
      final claim = await insuranceService.fileClaim(
        policyId: _selectedPolicyId!,
        coverageType: _selectedCoverageType!,
        incidentDate: _incidentDate,
        incidentDescription: _incidentDescriptionController.text,
        incidentLocation: _incidentLocationController.text,
        claimAmount: double.parse(_claimAmountController.text),
        currency: _currency,
        documents: _documents,
      );

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      // Show success dialog
      _showClaimSuccessDialog(claim);
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _error = e.toString();
      });
      debugPrint('Error submitting claim: $e');
    }
  }

  void _showClaimSuccessDialog(InsuranceClaim claim) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return AlertDialog(
          title: const Text('Claim Submitted'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Your claim has been successfully submitted.'),
              const SizedBox(height: 16),
              Text('Reference Number: ${claim.referenceNumber}'),
              const SizedBox(height: 8),
              Text('Status: ${claim.status.displayName}'),
              const SizedBox(height: 8),
              Text('Amount: ${claim.formattedClaimAmount}'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pushReplacementNamed(
                  '/travel/insurance/claim',
                  arguments: claim.id,
                );
              },
              child: const Text('View Claim'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pushReplacementNamed('/travel/insurance');
              },
              child: const Text('Done'),
            ),
          ],
        );
      },
    );
  }
}
