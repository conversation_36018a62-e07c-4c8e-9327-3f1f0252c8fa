import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:culture_connect/models/travel/insurance/insurance.dart'
    hide InsuranceCoverageType, InsuranceClaimStatus;
import 'package:culture_connect/models/travel/insurance/insurance_claim_status.dart';
import 'package:culture_connect/providers/travel/insurance/insurance_providers.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';

/// A screen for updating an insurance claim
class InsuranceClaimUpdateScreen extends ConsumerStatefulWidget {
  /// The ID of the claim to update
  final String claimId;

  /// Creates a new insurance claim update screen
  const InsuranceClaimUpdateScreen({
    super.key,
    required this.claimId,
  });

  @override
  ConsumerState<InsuranceClaimUpdateScreen> createState() =>
      _InsuranceClaimUpdateScreenState();
}

class _InsuranceClaimUpdateScreenState
    extends ConsumerState<InsuranceClaimUpdateScreen> {
  // Form key
  final _formKey = GlobalKey<FormState>();

  // Form data
  final _additionalInfoController = TextEditingController();
  final List<File> _additionalDocuments = [];

  // Loading state
  bool _isLoading = false;
  String? _error;

  @override
  void dispose() {
    _additionalInfoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final claimAsync = ref.watch(claimProvider(widget.claimId));

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Update Claim',
        showBackButton: true,
      ),
      body: claimAsync.when(
        data: (claim) {
          if (claim == null) {
            return Center(
              child: Text(
                'Claim not found',
                style: theme.textTheme.titleMedium,
              ),
            );
          }

          // Only claims with status 'infoRequested' can be updated
          if (claim.status != InsuranceClaimStatus.infoRequested) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: theme.colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Cannot Update Claim',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'This claim cannot be updated because its status is ${claim.status.displayName}.',
                    style: theme.textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: const Text('Go Back'),
                  ),
                ],
              ),
            );
          }

          return _buildUpdateForm(theme, claim);
        },
        loading: () => const Center(
          child: LoadingIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: ErrorView(
            error: error.toString(),
            onRetry: () => ref.refresh(claimProvider(widget.claimId)),
          ),
        ),
      ),
    );
  }

  Widget _buildUpdateForm(ThemeData theme, InsuranceClaim claim) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Claim summary
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Claim Summary',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Reference Number',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                claim.referenceNumber,
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Coverage Type',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                claim.coverageType.displayName,
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Claim Amount',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                claim.formattedClaimAmount,
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Status',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: claim.status.color.withAlpha(26),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  claim.status.displayName,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: claim.status.color,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Additional information requested
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.amber.withAlpha(26),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.amber,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.amber[800],
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Additional Information Requested',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.amber[800],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    claim.additionalInfoRequested ??
                        'Please provide additional information about your claim.',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: Colors.amber[800],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Additional information
            Text(
              'Your Response',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Additional Information',
                border: OutlineInputBorder(),
                alignLabelWithHint: true,
              ),
              controller: _additionalInfoController,
              maxLines: 5,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please provide additional information';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),

            // Additional documents
            Text(
              'Additional Documents',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Please upload any additional documents requested by the insurance provider.',
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: () => _pickDocument(ImageSource.camera),
                  icon: const Icon(Icons.camera_alt),
                  label: const Text('Take Photo'),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: () => _pickDocument(ImageSource.gallery),
                  icon: const Icon(Icons.photo_library),
                  label: const Text('Upload File'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_additionalDocuments.isNotEmpty) ...[
              Text(
                'Uploaded Documents (${_additionalDocuments.length})',
                style: theme.textTheme.titleSmall,
              ),
              const SizedBox(height: 8),
              SizedBox(
                height: 100,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _additionalDocuments.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Stack(
                        children: [
                          Container(
                            width: 100,
                            height: 100,
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: theme.colorScheme.outline,
                              ),
                              borderRadius: BorderRadius.circular(8),
                              image: DecorationImage(
                                image: FileImage(_additionalDocuments[index]),
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                          Positioned(
                            top: 4,
                            right: 4,
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  _additionalDocuments.removeAt(index);
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color: Colors.black.withAlpha(128),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.close,
                                  size: 16,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ] else ...[
              Text(
                'No additional documents uploaded yet',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.error,
                ),
              ),
            ],
            const SizedBox(height: 24),

            // Error message
            if (_error != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Text(
                  _error!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.error,
                  ),
                ),
              ),

            // Submit button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : () => _updateClaim(claim),
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isLoading
                    ? SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: theme.colorScheme.onPrimary,
                        ),
                      )
                    : const Text('Submit Update'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickDocument(ImageSource source) async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: source);

      if (pickedFile != null) {
        setState(() {
          _additionalDocuments.add(File(pickedFile.path));
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error picking document: $e';
      });
    }
  }

  Future<void> _updateClaim(InsuranceClaim claim) async {
    // Validate form
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Check if documents are uploaded
    if (_additionalDocuments.isEmpty) {
      setState(() {
        _error = 'Please upload at least one additional document';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Update claim
      final insuranceService = ref.read(insuranceServiceProvider);

      // In a real app, this would upload documents to a server and get URLs
      final documentUrls = _additionalDocuments
          .map((doc) =>
              'https://example.com/documents/${doc.path.split('/').last}')
          .toList();

      final updatedClaim = await insuranceService.updateClaim(
        claimId: claim.id,
        status: InsuranceClaimStatus.inReview, // Reset to in review
        incidentDescription:
            '${claim.incidentDescription}\n\nAdditional Information: ${_additionalInfoController.text}',
        additionalDocumentUrls: documentUrls,
      );

      setState(() {
        _isLoading = false;
      });

      // Show success dialog
      _showUpdateSuccessDialog(updatedClaim);
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = e.toString();
      });
    }
  }

  void _showUpdateSuccessDialog(InsuranceClaim claim) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return AlertDialog(
          title: const Text('Update Submitted'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Your claim update has been successfully submitted.'),
              const SizedBox(height: 16),
              const Text(
                  'The insurance provider will review your additional information and documents.'),
              const SizedBox(height: 8),
              Text('New Status: ${claim.status.displayName}'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pushReplacementNamed(
                  '/travel/insurance/claim',
                  arguments: claim.id,
                );
              },
              child: const Text('View Claim'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pushReplacementNamed('/travel/insurance');
              },
              child: const Text('Done'),
            ),
          ],
        );
      },
    );
  }
}
