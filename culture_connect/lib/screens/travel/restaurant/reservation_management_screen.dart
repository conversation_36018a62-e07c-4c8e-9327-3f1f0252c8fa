// Dart imports
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/restaurant_reservation.dart';
import 'package:culture_connect/providers/travel/restaurant_reservation_provider.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/widgets/common/empty_state.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';

/// Screen for managing restaurant reservations
class ReservationManagementScreen extends ConsumerStatefulWidget {
  /// Creates a new reservation management screen
  const ReservationManagementScreen({super.key});

  @override
  ConsumerState<ReservationManagementScreen> createState() =>
      _ReservationManagementScreenState();
}

class _ReservationManagementScreenState
    extends ConsumerState<ReservationManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final LoggingService _logger = LoggingService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Reservations'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Upcoming'),
            Tab(text: 'Past'),
          ],
          labelColor: theme.colorScheme.primary,
          unselectedLabelColor: theme.colorScheme.onSurface,
          indicatorColor: theme.colorScheme.primary,
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildUpcomingReservations(),
          _buildPastReservations(),
        ],
      ),
    );
  }

  Widget _buildUpcomingReservations() {
    final upcomingReservationsAsync = ref.watch(upcomingReservationsProvider);

    return upcomingReservationsAsync.when(
      data: (reservations) {
        if (reservations.isEmpty) {
          return const EmptyState(
            icon: Icons.calendar_today,
            title: 'No Upcoming Reservations',
            message: 'You don\'t have any upcoming restaurant reservations.',
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: reservations.length,
          itemBuilder: (context, index) {
            return _buildReservationCard(reservations[index], isUpcoming: true);
          },
        );
      },
      loading: () => const Center(child: LoadingIndicator()),
      error: (error, stackTrace) => ErrorView(
        error: 'Failed to load reservations: $error',
        onRetry: () => ref.refresh(upcomingReservationsProvider),
      ),
    );
  }

  Widget _buildPastReservations() {
    final pastReservationsAsync = ref.watch(pastReservationsProvider);

    return pastReservationsAsync.when(
      data: (reservations) {
        if (reservations.isEmpty) {
          return const EmptyState(
            icon: Icons.history,
            title: 'No Past Reservations',
            message: 'You don\'t have any past restaurant reservations.',
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: reservations.length,
          itemBuilder: (context, index) {
            return _buildReservationCard(reservations[index],
                isUpcoming: false);
          },
        );
      },
      loading: () => const Center(child: LoadingIndicator()),
      error: (error, stackTrace) => ErrorView(
        error: 'Failed to load reservations: $error',
        onRetry: () => ref.refresh(pastReservationsProvider),
      ),
    );
  }

  Widget _buildReservationCard(RestaurantReservation reservation,
      {required bool isUpcoming}) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _showReservationDetails(reservation),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Restaurant name and status
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      reservation.restaurantName,
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: reservation.status.color
                          .withAlpha(26), // 26 is equivalent to 0.1 opacity
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          reservation.status.icon,
                          size: 14,
                          color: reservation.status.color,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          reservation.status.displayName,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: reservation.status.color,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Date, time, and party size
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 16,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    DateFormat('EEE, MMM d, yyyy').format(reservation.date),
                    style: theme.textTheme.bodyMedium,
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    reservation.timeSlot.formatted,
                    style: theme.textTheme.bodyMedium,
                  ),
                ],
              ),

              const SizedBox(height: 8),

              Row(
                children: [
                  Icon(
                    Icons.people,
                    size: 16,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${reservation.partySize} ${reservation.partySize == 1 ? 'person' : 'people'}',
                    style: theme.textTheme.bodyMedium,
                  ),
                ],
              ),

              if (isUpcoming) ...[
                const SizedBox(height: 16),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () =>
                          _showModifyReservationDialog(reservation),
                      child: const Text('Modify'),
                    ),
                    const SizedBox(width: 8),
                    TextButton(
                      onPressed: () =>
                          _showCancelReservationDialog(reservation),
                      style: TextButton.styleFrom(
                        foregroundColor: theme.colorScheme.error,
                      ),
                      child: const Text('Cancel'),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _showReservationDetails(RestaurantReservation reservation) {
    final theme = Theme.of(context);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Reservation Details',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),

              const Divider(height: 32),

              // Restaurant name
              Text(
                reservation.restaurantName,
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),

              const SizedBox(height: 24),

              // Reservation details
              _buildDetailRow(context, 'Reservation ID',
                  reservation.id.substring(0, 8).toUpperCase()),
              const SizedBox(height: 16),
              _buildDetailRow(context, 'Date',
                  DateFormat('EEEE, MMMM d, yyyy').format(reservation.date)),
              const SizedBox(height: 16),
              _buildDetailRow(context, 'Time', reservation.timeSlot.formatted),
              const SizedBox(height: 16),
              _buildDetailRow(context, 'Party Size',
                  '${reservation.partySize} ${reservation.partySize == 1 ? 'person' : 'people'}'),

              if (reservation.specialRequests.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildDetailRow(
                    context, 'Special Requests', reservation.specialRequests),
              ],

              const SizedBox(height: 16),
              _buildDetailRow(
                  context, 'Status', reservation.status.displayName),
              const SizedBox(height: 16),
              _buildDetailRow(context, 'Created',
                  DateFormat('MMM d, yyyy').format(reservation.createdAt)),

              const SizedBox(height: 32),

              // Contact information
              Text(
                'Contact Information',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),

              const SizedBox(height: 16),

              _buildDetailRow(context, 'Name', reservation.userName),
              const SizedBox(height: 16),
              _buildDetailRow(context, 'Phone', reservation.contactPhone),
              const SizedBox(height: 16),
              _buildDetailRow(context, 'Email', reservation.contactEmail),

              const SizedBox(height: 32),

              // Action buttons for upcoming reservations
              if (reservation.status == ReservationStatus.confirmed ||
                  reservation.status == ReservationStatus.pending) ...[
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          _showModifyReservationDialog(reservation);
                        },
                        child: const Text('Modify'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          _showCancelReservationDialog(reservation);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.error,
                          foregroundColor: theme.colorScheme.onError,
                        ),
                        child: const Text('Cancel Reservation'),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildDetailRow(BuildContext context, String label, String value) {
    final theme = Theme.of(context);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  void _showModifyReservationDialog(RestaurantReservation reservation) {
    // In a real app, this would navigate to a modification screen
    // For this implementation, we'll just show a dialog
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Modify Reservation'),
          content: const Text(
              'This feature is coming soon. You would be able to modify your reservation details here.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  void _showCancelReservationDialog(RestaurantReservation reservation) {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Cancel Reservation'),
          content: Text(
              'Are you sure you want to cancel your reservation at ${reservation.restaurantName} on ${DateFormat('EEE, MMM d').format(reservation.date)} at ${reservation.timeSlot.formatted}?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('No, Keep It'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _cancelReservation(reservation);
              },
              style: TextButton.styleFrom(
                foregroundColor: theme.colorScheme.error,
              ),
              child: const Text('Yes, Cancel'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _cancelReservation(RestaurantReservation reservation) async {
    try {
      _logger.info('ReservationManagementScreen',
          'Cancelling reservation: ${reservation.id}');
      await ref
          .read(restaurantReservationNotifierProvider.notifier)
          .cancelReservation(reservation.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Your reservation at ${reservation.restaurantName} has been cancelled.'),
            backgroundColor: Colors.green,
          ),
        );

        // Refresh the reservations lists to trigger a rebuild
        // We don't need to await these as they're just triggering a rebuild
        ref.invalidate(upcomingReservationsProvider);
        ref.invalidate(pastReservationsProvider);

        _logger.info('ReservationManagementScreen',
            'Reservation cancelled successfully: ${reservation.id}');
      }
    } catch (e, stackTrace) {
      _logger.error('ReservationManagementScreen',
          'Failed to cancel reservation: ${reservation.id}', e, stackTrace);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to cancel reservation: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
