import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/widgets/common/rating_display.dart';

/// Screen for displaying restaurant details
class RestaurantDetailsScreen extends ConsumerStatefulWidget {
  /// The restaurant to display
  final Restaurant restaurant;

  /// Creates a new restaurant details screen
  const RestaurantDetailsScreen({
    super.key,
    required this.restaurant,
  });

  @override
  ConsumerState<RestaurantDetailsScreen> createState() =>
      _RestaurantDetailsScreenState();
}

class _RestaurantDetailsScreenState
    extends ConsumerState<RestaurantDetailsScreen>
    with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  late TabController _tabController;
  DateTime _selectedDate = DateTime.now();
  TimeOfDay _selectedTime = TimeOfDay.now();
  int _partySize = 2;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _selectTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
    );

    if (picked != null && picked != _selectedTime) {
      setState(() {
        _selectedTime = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // App bar with image
          SliverAppBar(
            expandedHeight: 250,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              background: Hero(
                tag: 'restaurant_image_${widget.restaurant.id}',
                child: Image.network(
                  widget.restaurant.imageUrl,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: theme.colorScheme.surfaceContainerHighest,
                      child: Center(
                        child: Icon(
                          Icons.image_not_supported,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),

          // Content
          SliverToBoxAdapter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title, rating, and price
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title and rating
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.restaurant.name,
                                  style:
                                      theme.textTheme.headlineSmall?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    Icon(
                                      widget.restaurant.restaurantType.icon,
                                      size: 16,
                                      color: theme.colorScheme.primary,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      widget.restaurant.restaurantType
                                          .displayName,
                                      style:
                                          theme.textTheme.titleMedium?.copyWith(
                                        color:
                                            theme.colorScheme.onSurfaceVariant,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              RatingDisplay(
                                rating: widget.restaurant.rating,
                                size: 20,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '${widget.restaurant.reviewCount} reviews',
                                style: theme.textTheme.bodySmall,
                              ),
                            ],
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      // Location
                      Row(
                        children: [
                          Icon(
                            Icons.location_on,
                            size: 16,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              widget.restaurant.location,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      // Cuisine types
                      Row(
                        children: [
                          Icon(
                            Icons.restaurant_menu,
                            size: 16,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              widget.restaurant.formattedCuisineTypes,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Price
                      Row(
                        children: [
                          if (widget.restaurant.isOnSale &&
                              widget.restaurant.originalPrice != null) ...[
                            Text(
                              widget.restaurant.formattedOriginalPrice!,
                              style: theme.textTheme.titleMedium?.copyWith(
                                decoration: TextDecoration.lineThrough,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                            const SizedBox(width: 8),
                          ],
                          Text(
                            'Average ${widget.restaurant.formattedPrice} per person',
                            style: theme.textTheme.titleLarge?.copyWith(
                              color: theme.colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Tabs
                TabBar(
                  controller: _tabController,
                  tabs: const [
                    Tab(text: 'Info'),
                    Tab(text: 'Menu'),
                    Tab(text: 'Reviews'),
                  ],
                  labelColor: theme.colorScheme.primary,
                  unselectedLabelColor: theme.colorScheme.onSurface,
                  indicatorColor: theme.colorScheme.primary,
                ),

                // Tab content
                SizedBox(
                  height: 400, // Fixed height for tab content
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildInfoTab(),
                      _buildMenuTab(),
                      _buildReviewsTab(),
                    ],
                  ),
                ),

                // Reservation section
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Make a Reservation',
                        style: theme.textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            children: [
                              // Date
                              ListTile(
                                title: const Text('Date'),
                                subtitle: Text(
                                  '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                                  style: theme.textTheme.titleMedium,
                                ),
                                trailing: const Icon(Icons.calendar_today),
                                onTap: _selectDate,
                              ),
                              const Divider(),

                              // Time
                              ListTile(
                                title: const Text('Time'),
                                subtitle: Text(
                                  '${_selectedTime.hour}:${_selectedTime.minute.toString().padLeft(2, '0')}',
                                  style: theme.textTheme.titleMedium,
                                ),
                                trailing: const Icon(Icons.access_time),
                                onTap: _selectTime,
                              ),
                              const Divider(),

                              // Party size
                              ListTile(
                                title: const Text('Party Size'),
                                subtitle: Row(
                                  children: [
                                    IconButton(
                                      icon: const Icon(Icons.remove),
                                      onPressed: _partySize > 1
                                          ? () {
                                              setState(() {
                                                _partySize--;
                                              });
                                            }
                                          : null,
                                    ),
                                    Text(
                                      '$_partySize',
                                      style: theme.textTheme.titleMedium,
                                    ),
                                    IconButton(
                                      icon: const Icon(Icons.add),
                                      onPressed: _partySize < 20
                                          ? () {
                                              setState(() {
                                                _partySize++;
                                              });
                                            }
                                          : null,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Reserve button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: widget.restaurant.requiresReservation
                              ? () {
                                  // Show reservation confirmation dialog
                                  showDialog(
                                    context: context,
                                    builder: (context) => AlertDialog(
                                      title: const Text('Confirm Reservation'),
                                      content: Text(
                                        'Would you like to make a reservation at ${widget.restaurant.name} for $_partySize people on ${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year} at ${_selectedTime.hour}:${_selectedTime.minute.toString().padLeft(2, '0')}?',
                                      ),
                                      actions: [
                                        TextButton(
                                          onPressed: () =>
                                              Navigator.pop(context),
                                          child: const Text('Cancel'),
                                        ),
                                        ElevatedButton(
                                          onPressed: () {
                                            // Process reservation
                                            Navigator.pop(context);

                                            // Show success message
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(
                                              SnackBar(
                                                content: Text(
                                                  'Reservation confirmed at ${widget.restaurant.name}',
                                                ),
                                                backgroundColor:
                                                    theme.colorScheme.primary,
                                              ),
                                            );
                                          },
                                          child: const Text('Confirm'),
                                        ),
                                      ],
                                    ),
                                  );
                                }
                              : null,
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            backgroundColor: theme.colorScheme.primary,
                            foregroundColor: theme.colorScheme.onPrimary,
                          ),
                          child: Text(
                            widget.restaurant.requiresReservation
                                ? 'Reserve a Table'
                                : 'No Reservation Required',
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: theme.colorScheme.onPrimary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Description
          Text(
            'Description',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.restaurant.description,
            style: Theme.of(context).textTheme.bodyMedium,
          ),

          const SizedBox(height: 16),

          // Opening hours
          Text(
            'Opening Hours',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildOpeningHoursRow('Monday',
                      widget.restaurant.getOpeningHoursForDay('Monday')),
                  _buildOpeningHoursRow('Tuesday',
                      widget.restaurant.getOpeningHoursForDay('Tuesday')),
                  _buildOpeningHoursRow('Wednesday',
                      widget.restaurant.getOpeningHoursForDay('Wednesday')),
                  _buildOpeningHoursRow('Thursday',
                      widget.restaurant.getOpeningHoursForDay('Thursday')),
                  _buildOpeningHoursRow('Friday',
                      widget.restaurant.getOpeningHoursForDay('Friday')),
                  _buildOpeningHoursRow('Saturday',
                      widget.restaurant.getOpeningHoursForDay('Saturday')),
                  _buildOpeningHoursRow('Sunday',
                      widget.restaurant.getOpeningHoursForDay('Sunday')),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Features
          Text(
            'Features',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildFeatureChip(Icons.outdoor_grill, 'Outdoor Seating',
                  widget.restaurant.hasOutdoorSeating),
              _buildFeatureChip(
                  Icons.local_bar, 'Bar', widget.restaurant.hasBar),
              _buildFeatureChip(Icons.music_note, 'Live Music',
                  widget.restaurant.hasLiveMusic),
              _buildFeatureChip(
                  Icons.child_care, 'Kids Menu', widget.restaurant.hasKidsMenu),
              _buildFeatureChip(Icons.eco, 'Vegetarian Options',
                  widget.restaurant.hasVegetarianOptions),
              _buildFeatureChip(Icons.spa, 'Vegan Options',
                  widget.restaurant.hasVeganOptions),
              _buildFeatureChip(Icons.no_food, 'Gluten-Free Options',
                  widget.restaurant.hasGlutenFreeOptions),
              _buildFeatureChip(Icons.food_bank, 'Halal Options',
                  widget.restaurant.hasHalalOptions),
              _buildFeatureChip(Icons.synagogue, 'Kosher Options',
                  widget.restaurant.hasKosherOptions),
              _buildFeatureChip(Icons.checkroom, 'Dress Code',
                  widget.restaurant.hasDressCode),
              _buildFeatureChip(Icons.calendar_today, 'Requires Reservation',
                  widget.restaurant.requiresReservation),
              _buildFeatureChip(
                  Icons.local_parking, 'Parking', widget.restaurant.hasParking),
              _buildFeatureChip(Icons.directions_car, 'Valet Parking',
                  widget.restaurant.hasValetParking),
              _buildFeatureChip(Icons.accessible, 'Wheelchair Accessible',
                  widget.restaurant.isWheelchairAccessible),
              _buildFeatureChip(Icons.wifi, 'WiFi', widget.restaurant.hasWifi),
              _buildFeatureChip(Icons.credit_card, 'Accepts Credit Cards',
                  widget.restaurant.acceptsCreditCards),
              _buildFeatureChip(
                  Icons.visibility, 'View', widget.restaurant.hasView),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOpeningHoursRow(String day, String hours) {
    final isToday = day == _getDayOfWeek(DateTime.now().weekday);
    final isOpen = widget.restaurant.isOpenAt(DateTime.now()) && isToday;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            day,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: isToday ? FontWeight.bold : null,
                  color: isToday ? Theme.of(context).colorScheme.primary : null,
                ),
          ),
          Row(
            children: [
              Text(
                hours,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: isToday ? FontWeight.bold : null,
                      color: isToday
                          ? Theme.of(context).colorScheme.primary
                          : null,
                    ),
              ),
              if (isToday) ...[
                const SizedBox(width: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: isOpen ? Colors.green : Colors.red,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    isOpen ? 'Open' : 'Closed',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureChip(IconData icon, String label, bool isAvailable) {
    return Chip(
      avatar: Icon(
        icon,
        size: 16,
        color: isAvailable
            ? Theme.of(context).colorScheme.primary
            : Theme.of(context).colorScheme.onSurfaceVariant.withAlpha(128),
      ),
      label: Text(label),
      backgroundColor: isAvailable
          ? Theme.of(context).colorScheme.primaryContainer
          : Theme.of(context)
              .colorScheme
              .surfaceContainerHighest
              .withAlpha(128),
      labelStyle: TextStyle(
        color: isAvailable
            ? Theme.of(context).colorScheme.onPrimaryContainer
            : Theme.of(context).colorScheme.onSurfaceVariant.withAlpha(128),
        decoration: isAvailable ? null : TextDecoration.lineThrough,
      ),
    );
  }

  Widget _buildMenuTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: widget.restaurant.menuItems.length,
      itemBuilder: (context, index) {
        final menuItem = widget.restaurant.menuItems[index];

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Menu item image
                if (menuItem.imageUrl != null)
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      menuItem.imageUrl!,
                      width: 80,
                      height: 80,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 80,
                          height: 80,
                          color: Theme.of(context)
                              .colorScheme
                              .surfaceContainerHighest,
                          child: Center(
                            child: Icon(
                              Icons.image_not_supported,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                            ),
                          ),
                        );
                      },
                    ),
                  ),

                SizedBox(width: menuItem.imageUrl != null ? 16 : 0),

                // Menu item details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Name and price
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              menuItem.name,
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ),
                          Text(
                            menuItem.formattedPrice,
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  color: Theme.of(context).colorScheme.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),

                      // Description
                      Text(
                        menuItem.description,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 8),

                      // Dietary information
                      Wrap(
                        spacing: 8,
                        children: [
                          if (menuItem.isVegetarian)
                            _buildDietaryTag(
                                Icons.eco, 'Vegetarian', Colors.green),
                          if (menuItem.isVegan)
                            _buildDietaryTag(
                                Icons.spa, 'Vegan', Colors.green.shade700),
                          if (menuItem.isGlutenFree)
                            _buildDietaryTag(
                                Icons.no_food, 'Gluten-Free', Colors.amber),
                          if (menuItem.containsNuts)
                            _buildDietaryTag(
                                Icons.warning, 'Contains Nuts', Colors.orange),
                          if (menuItem.isSpicy)
                            _buildDietaryTag(
                                Icons.whatshot, 'Spicy', Colors.red),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDietaryTag(IconData icon, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withAlpha(26),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withAlpha(128)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: color,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewsTab() {
    // In a real app, this would fetch reviews from an API
    return Center(
      child: Text(
        'Reviews coming soon!',
        style: Theme.of(context).textTheme.titleMedium,
      ),
    );
  }

  /// Get the day of the week as a string
  String _getDayOfWeek(int weekday) {
    switch (weekday) {
      case DateTime.monday:
        return 'Monday';
      case DateTime.tuesday:
        return 'Tuesday';
      case DateTime.wednesday:
        return 'Wednesday';
      case DateTime.thursday:
        return 'Thursday';
      case DateTime.friday:
        return 'Friday';
      case DateTime.saturday:
        return 'Saturday';
      case DateTime.sunday:
        return 'Sunday';
      default:
        return '';
    }
  }
}
