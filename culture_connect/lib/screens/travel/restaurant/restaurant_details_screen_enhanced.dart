// Dart imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/travel/restaurant_reservation_provider.dart';
import 'package:culture_connect/screens/travel/restaurant/restaurant_reservation_screen.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/widgets/common/image_gallery.dart';
import 'package:culture_connect/widgets/common/rating_display.dart';
import 'package:culture_connect/widgets/travel/restaurant/menu_item_card.dart';
import 'package:culture_connect/widgets/travel/restaurant/restaurant_amenity_chip.dart';

/// Enhanced screen for displaying restaurant details
class RestaurantDetailsScreenEnhanced extends ConsumerStatefulWidget {
  /// The restaurant to display
  final Restaurant restaurant;

  /// Creates a new restaurant details screen
  const RestaurantDetailsScreenEnhanced({
    super.key,
    required this.restaurant,
  });

  @override
  ConsumerState<RestaurantDetailsScreenEnhanced> createState() =>
      _RestaurantDetailsScreenEnhancedState();
}

class _RestaurantDetailsScreenEnhancedState
    extends ConsumerState<RestaurantDetailsScreenEnhanced>
    with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  late TabController _tabController;
  bool _isAppBarExpanded = false;
  final LoggingService _logger = LoggingService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    _scrollController.addListener(() {
      if (_scrollController.hasClients) {
        final expanded = _scrollController.offset < 200;
        if (_isAppBarExpanded != expanded) {
          setState(() {
            _isAppBarExpanded = expanded;
          });
        }
      }
    });

    // Initialize the restaurant reservation service with mock data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        _logger.info('RestaurantDetailsScreen',
            'Initializing mock data for restaurant: ${widget.restaurant.id}');
        ref.read(restaurantReservationServiceProvider).initializeMockData();
      } catch (e, stackTrace) {
        _logger.error(
            'RestaurantDetailsScreen',
            'Failed to initialize mock data for restaurant: ${widget.restaurant.id}',
            e,
            stackTrace);
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: NestedScrollView(
        controller: _scrollController,
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 250,
              pinned: true,
              stretch: true,
              flexibleSpace: FlexibleSpaceBar(
                title: AnimatedOpacity(
                  opacity: _isAppBarExpanded ? 0.0 : 1.0,
                  duration: const Duration(milliseconds: 200),
                  child: Text(
                    widget.restaurant.name,
                    style: TextStyle(
                      color: theme.colorScheme.onPrimary,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                background: Hero(
                  tag: 'restaurant_image_${widget.restaurant.id}',
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      Image.network(
                        widget.restaurant.imageUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          _logger.error(
                              'RestaurantDetailsScreen',
                              'Failed to load restaurant image: ${widget.restaurant.imageUrl}',
                              error,
                              stackTrace);
                          return Container(
                            color: theme.colorScheme.surfaceContainerHighest,
                            child: Center(
                              child: Icon(
                                Icons.image_not_supported,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          );
                        },
                      ),
                      // Gradient overlay for better text visibility
                      Positioned.fill(
                        child: DecoratedBox(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black.withAlpha(
                                    179), // 179 is equivalent to 0.7 opacity
                              ],
                              stops: const [0.7, 1.0],
                            ),
                          ),
                        ),
                      ),
                      // Restaurant info overlay
                      Positioned(
                        bottom: 16,
                        left: 16,
                        right: 16,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Only show in expanded state
                            if (_isAppBarExpanded) ...[
                              Text(
                                widget.restaurant.name,
                                style: theme.textTheme.headlineSmall?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  RatingDisplay(
                                    rating: widget.restaurant.rating,
                                    size: 16,
                                    filledColor: Colors.amber,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    '(${widget.restaurant.reviewCount})',
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      color: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  const Icon(
                                    Icons.location_on,
                                    size: 16,
                                    color: Colors.white,
                                  ),
                                  const SizedBox(width: 4),
                                  Expanded(
                                    child: Text(
                                      widget.restaurant.location,
                                      style:
                                          theme.textTheme.bodyMedium?.copyWith(
                                        color: Colors.white,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              bottom: TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(text: 'Info'),
                  Tab(text: 'Menu'),
                  Tab(text: 'Reviews'),
                ],
                labelColor: theme.colorScheme.primary,
                unselectedLabelColor: theme.colorScheme.onSurface,
                indicatorColor: theme.colorScheme.primary,
              ),
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildInfoTab(),
            _buildMenuTab(),
            _buildReviewsTab(),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildInfoTab() {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Description
          Text(
            'About',
            style: theme.textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            widget.restaurant.description,
            style: theme.textTheme.bodyMedium,
          ),
          const SizedBox(height: 24),

          // Cuisine types
          Text(
            'Cuisine',
            style: theme.textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: widget.restaurant.cuisineTypes.map((cuisine) {
              return Chip(
                label: Text(cuisine.displayName),
                backgroundColor: theme.colorScheme.surfaceContainerHighest,
              );
            }).toList(),
          ),
          const SizedBox(height: 24),

          // Opening hours
          Text(
            'Opening Hours',
            style: theme.textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildOpeningHoursRow('Monday',
                      widget.restaurant.openingHours['Monday'] ?? 'Closed'),
                  _buildOpeningHoursRow('Tuesday',
                      widget.restaurant.openingHours['Tuesday'] ?? 'Closed'),
                  _buildOpeningHoursRow('Wednesday',
                      widget.restaurant.openingHours['Wednesday'] ?? 'Closed'),
                  _buildOpeningHoursRow('Thursday',
                      widget.restaurant.openingHours['Thursday'] ?? 'Closed'),
                  _buildOpeningHoursRow('Friday',
                      widget.restaurant.openingHours['Friday'] ?? 'Closed'),
                  _buildOpeningHoursRow('Saturday',
                      widget.restaurant.openingHours['Saturday'] ?? 'Closed'),
                  _buildOpeningHoursRow('Sunday',
                      widget.restaurant.openingHours['Sunday'] ?? 'Closed'),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Amenities
          Text(
            'Amenities',
            style: theme.textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 16,
            runSpacing: 16,
            children: [
              if (widget.restaurant.hasOutdoorSeating)
                const RestaurantAmenityChip(
                    icon: Icons.deck, label: 'Outdoor Seating'),
              if (widget.restaurant.hasBar)
                const RestaurantAmenityChip(
                    icon: Icons.local_bar, label: 'Bar'),
              if (widget.restaurant.hasLiveMusic)
                const RestaurantAmenityChip(
                    icon: Icons.music_note, label: 'Live Music'),
              if (widget.restaurant.hasKidsMenu)
                const RestaurantAmenityChip(
                    icon: Icons.child_care, label: 'Kids Menu'),
              if (widget.restaurant.hasVegetarianOptions)
                const RestaurantAmenityChip(
                    icon: Icons.eco, label: 'Vegetarian'),
              if (widget.restaurant.hasVeganOptions)
                const RestaurantAmenityChip(icon: Icons.spa, label: 'Vegan'),
              if (widget.restaurant.hasGlutenFreeOptions)
                const RestaurantAmenityChip(
                    icon: Icons.no_food, label: 'Gluten-Free'),
              if (widget.restaurant.hasWifi)
                const RestaurantAmenityChip(icon: Icons.wifi, label: 'WiFi'),
              if (widget.restaurant.hasParking)
                const RestaurantAmenityChip(
                    icon: Icons.local_parking, label: 'Parking'),
              if (widget.restaurant.isWheelchairAccessible)
                const RestaurantAmenityChip(
                    icon: Icons.accessible, label: 'Accessible'),
            ],
          ),
          const SizedBox(height: 24),

          // Photo gallery
          Text(
            'Photos',
            style: theme.textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          ImageGallery(images: widget.restaurant.additionalImages),
          const SizedBox(height: 100), // Space for bottom bar
        ],
      ),
    );
  }

  Widget _buildMenuTab() {
    final theme = Theme.of(context);

    // Group menu items by category
    final menuByCategory = <String, List<MenuItem>>{};
    for (final item in widget.restaurant.menuItems) {
      if (!menuByCategory.containsKey(item.category)) {
        menuByCategory[item.category] = [];
      }
      menuByCategory[item.category]!.add(item);
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for (final category in menuByCategory.keys) ...[
            Text(
              category,
              style: theme.textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            ...menuByCategory[category]!
                .map((item) => MenuItemCard(menuItem: item)),
            const SizedBox(height: 24),
          ],
          const SizedBox(height: 100), // Space for bottom bar
        ],
      ),
    );
  }

  Widget _buildReviewsTab() {
    return const Center(
      child: Text('Reviews coming soon'),
    );
  }

  Widget _buildOpeningHoursRow(String day, String hours) {
    final isToday = day == _getDayOfWeek(DateTime.now().weekday);
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            day,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: isToday ? FontWeight.bold : null,
              color: isToday ? theme.colorScheme.primary : null,
            ),
          ),
          Text(
            hours,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: isToday ? FontWeight.bold : null,
              color: isToday ? theme.colorScheme.primary : null,
            ),
          ),
        ],
      ),
    );
  }

  String _getDayOfWeek(int weekday) {
    switch (weekday) {
      case 1:
        return 'Monday';
      case 2:
        return 'Tuesday';
      case 3:
        return 'Wednesday';
      case 4:
        return 'Thursday';
      case 5:
        return 'Friday';
      case 6:
        return 'Saturday';
      case 7:
        return 'Sunday';
      default:
        return '';
    }
  }

  Widget _buildBottomBar() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color:
                Colors.black.withAlpha(26), // 26 is equivalent to 0.1 opacity
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: widget.restaurant.requiresReservation
                ? () => _navigateToReservationScreen()
                : null,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: theme.colorScheme.onPrimary,
            ),
            child: Text(
              widget.restaurant.requiresReservation
                  ? 'Make a Reservation'
                  : 'No Reservation Required',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToReservationScreen() {
    try {
      _logger.info('RestaurantDetailsScreen',
          'Navigating to reservation screen for restaurant: ${widget.restaurant.id}');
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => RestaurantReservationScreen(
            restaurant: widget.restaurant,
          ),
        ),
      );
    } catch (e, stackTrace) {
      _logger.error(
          'RestaurantDetailsScreen',
          'Failed to navigate to reservation screen for restaurant: ${widget.restaurant.id}',
          e,
          stackTrace);

      // Show error message to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to open reservation screen: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
