import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/trust_score_model.dart';
import 'package:culture_connect/services/trust_score_service.dart';
import 'package:culture_connect/widgets/trust/trust_score_widget.dart';
import 'package:culture_connect/widgets/verification/verification_badge.dart';
import 'package:culture_connect/services/verification_service.dart';

/// A screen for displaying trust scores
class TrustScoreScreen extends ConsumerWidget {
  /// The user ID to display the trust score for
  final String userId;

  /// The user's name
  final String userName;

  /// Creates a new trust score screen
  const TrustScoreScreen({
    super.key,
    required this.userId,
    required this.userName,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final trustScoreAsync = ref.watch(trustScoreProvider(userId));
    final verificationBadgesAsync = ref.watch(verificationBadgesProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('$userName\'s Trust Score'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Trust score overview
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Text(
                      'Trust Score',
                      style: theme.textTheme.titleLarge,
                    ),
                    const SizedBox(height: 24),
                    trustScoreAsync.when(
                      data: (trustScore) {
                        if (trustScore == null) {
                          return const Center(
                            child: Text('No trust score available'),
                          );
                        }

                        final trustLevel = trustScore.trustLevel;

                        return Column(
                          children: [
                            TrustScoreWidget(
                              userId: userId,
                              size: 100,
                              showLabel: true,
                              showScore: true,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _getTrustLevelDescription(trustLevel),
                              textAlign: TextAlign.center,
                              style: theme.textTheme.bodyMedium,
                            ),
                          ],
                        );
                      },
                      loading: () => const Center(
                        child: CircularProgressIndicator(),
                      ),
                      error: (error, stackTrace) => Center(
                        child: Text(
                          'Error loading trust score: $error',
                          style: TextStyle(color: theme.colorScheme.error),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Verification badges
            Text(
              'Verification Badges',
              style: theme.textTheme.titleLarge,
            ),
            const SizedBox(height: 16),

            verificationBadgesAsync.when(
              data: (badges) {
                if (badges.isEmpty) {
                  return Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey.withAlpha(26),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Text(
                        'No verification badges available',
                        textAlign: TextAlign.center,
                      ),
                    ),
                  );
                }

                return VerificationBadgesList(
                  badges: badges,
                  showDescription: true,
                  showOnlyValid: true,
                  emptyState: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey.withAlpha(26),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Text(
                        'No valid verification badges available',
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                );
              },
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stackTrace) => Center(
                child: Text(
                  'Error loading verification badges: $error',
                  style: TextStyle(color: theme.colorScheme.error),
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Trust factors
            TrustFactorsWidget(
              userId: userId,
            ),

            const SizedBox(height: 24),

            // How trust scores work
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'How Trust Scores Work',
                      style: theme.textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                        'Trust scores are calculated based on various factors including:'),
                    const SizedBox(height: 8),
                    _buildInfoItem(
                      context,
                      icon: Icons.badge,
                      title: 'Identity Verification',
                      description: 'Verified identity through government ID',
                    ),
                    _buildInfoItem(
                      context,
                      icon: Icons.security,
                      title: 'Background Checks',
                      description: 'Comprehensive background verification',
                    ),
                    _buildInfoItem(
                      context,
                      icon: Icons.star,
                      title: 'Positive Reviews',
                      description: 'Ratings and reviews from other users',
                    ),
                    _buildInfoItem(
                      context,
                      icon: Icons.check_circle,
                      title: 'Completed Experiences',
                      description: 'Successfully completed experiences',
                    ),
                    _buildInfoItem(
                      context,
                      icon: Icons.chat,
                      title: 'Response Rate',
                      description: 'How quickly and consistently they respond',
                    ),
                    _buildInfoItem(
                      context,
                      icon: Icons.access_time,
                      title: 'Account Age',
                      description: 'How long they\'ve been a member',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds an info item with an icon, title, and description
  Widget _buildInfoItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
  }) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 20,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleSmall,
                ),
                Text(
                  description,
                  style: theme.textTheme.bodySmall,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Returns a description for the given trust level
  String _getTrustLevelDescription(TrustLevel level) {
    switch (level) {
      case TrustLevel.exceptional:
        return 'This user has an exceptional trust score. They have completed extensive verification and have an outstanding reputation in the community.';
      case TrustLevel.excellent:
        return 'This user has an excellent trust score. They have completed multiple verifications and have a great reputation.';
      case TrustLevel.great:
        return 'This user has a great trust score. They have completed key verifications and have a good reputation.';
      case TrustLevel.good:
        return 'This user has a good trust score. They have completed some verifications and have a positive reputation.';
      case TrustLevel.average:
        return 'This user has an average trust score. They have completed basic verifications.';
      case TrustLevel.fair:
        return 'This user has a fair trust score. They have limited verifications or history on the platform.';
      case TrustLevel.poor:
        return 'This user has a poor trust score. They have few or no verifications and limited history.';
      case TrustLevel.untrusted:
        return 'This user has not completed any verifications and has no established history on the platform.';
    }
  }
}
