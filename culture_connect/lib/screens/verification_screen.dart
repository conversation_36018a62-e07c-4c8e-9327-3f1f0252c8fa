import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/providers/auth_provider.dart';

import 'package:culture_connect/widgets/auth_gradient_background.dart';
import 'package:culture_connect/widgets/curved_content_container.dart';
import 'package:culture_connect/widgets/custom_button.dart';

class VerificationScreen extends ConsumerStatefulWidget {
  const VerificationScreen({super.key});

  @override
  ConsumerState<VerificationScreen> createState() => _VerificationScreenState();
}

class _VerificationScreenState extends ConsumerState<VerificationScreen> {
  String? _errorMessage;
  String? _successMessage;
  bool _isVerified = false;
  bool _isLoading = false;
  bool _canResend = true;
  int _resendCooldown = 0;
  Timer? _timer;
  Timer? _resendTimer;
  final int _checkInterval = 2; // Check every 2 seconds for faster response
  final int _resendCooldownDuration = 10; // 10 seconds cooldown for resend

  @override
  void initState() {
    super.initState();
    // Start periodic verification check
    _startVerificationCheck();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _resendTimer?.cancel();
    super.dispose();
  }

  void _startVerificationCheck() {
    // Add initial delay to ensure Firebase Auth state is fully initialized
    Future.delayed(const Duration(milliseconds: 500), () {
      if (!mounted) return;

      // Check immediately on start with multiple approaches
      _checkEmailVerification();

      // Force token refresh on initial check to ensure latest claims
      _forceTokenRefreshAndCheck();

      // Then set up periodic checks
      _timer = Timer.periodic(Duration(seconds: _checkInterval), (timer) {
        if (!mounted) {
          timer.cancel();
          return;
        }
        _checkEmailVerification();
      });
    });
  }

  Future<void> _forceTokenRefreshAndCheck() async {
    try {
      User? user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      // Force token refresh to get latest claims
      await user.getIdToken(true);
      await _checkEmailVerification();
    } catch (e) {
      debugPrint('Token refresh failed: $e');
    }
  }

  Future<void> _checkEmailVerification() async {
    try {
      // Enhanced authentication state debugging using Riverpod
      debugPrint('🔍 Starting email verification check...');

      // Get current Firebase user directly from currentUserProvider
      final userAsync = ref.read(currentUserProvider);
      User? user;

      userAsync.when(
        data: (firebaseUser) => user = firebaseUser,
        loading: () => user = null,
        error: (_, __) => user = null,
      );

      if (user == null) {
        debugPrint(
            '❌ No user in currentUserProvider - checking Firebase directly...');

        // Fallback: Check Firebase Auth directly
        user = FirebaseAuth.instance.currentUser;

        if (user == null) {
          debugPrint('❌ No user in Firebase Auth either - session lost');

          // Additional debugging for Firebase state
          try {
            final app = Firebase.app();
            debugPrint('✅ Firebase app is initialized: ${app.name}');
          } catch (e) {
            debugPrint('❌ Firebase app not initialized: $e');
          }

          if (mounted) {
            setState(() {
              _errorMessage =
                  'Authentication session expired. Please sign in again.';
            });
          }
          return;
        } else {
          debugPrint('✅ User found in Firebase Auth: ${user!.email}');
        }
      } else {
        debugPrint(
            '✅ User authenticated via currentUserProvider: ${user!.email} (verified: ${user!.emailVerified})');
      }

      // Use token-based verification as primary method to avoid PigeonUserInfo issues
      bool isVerified = await _checkVerificationViaToken(user!);

      if (isVerified && !_isVerified) {
        _handleVerificationSuccess();
        return;
      }

      // Fallback: Try standard reload only if token method fails
      try {
        await user!.reload();
        user = FirebaseAuth.instance.currentUser;

        if (user!.emailVerified && !_isVerified) {
          debugPrint('✅ Email verified via standard reload method');
          _handleVerificationSuccess();
        }
      } catch (reloadError) {
        debugPrint(
            '⚠️ Standard reload failed, using token-based verification only: $reloadError');
        // Token-based verification already attempted above, so we're done
      }
    } catch (e) {
      debugPrint('❌ Error in main verification check: $e');
      debugPrint('Error type: ${e.runtimeType}');
    }
  }

  // Primary verification method using ID token to avoid PigeonUserInfo issues
  Future<bool> _checkVerificationViaToken(User user) async {
    try {
      debugPrint('🔍 Checking verification via ID token for: ${user.email}');

      // Get ID token with force refresh to ensure latest claims
      final idTokenResult = await user.getIdTokenResult(true);
      final emailVerified = idTokenResult.claims?['email_verified'] ?? false;

      debugPrint('📋 Token claims - email_verified: $emailVerified');
      debugPrint('📋 User.emailVerified property: ${user.emailVerified}');

      if (emailVerified == true) {
        debugPrint('✅ Email verified via token-based check');
        return true;
      } else {
        debugPrint('❌ Email not yet verified via token-based check');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Token-based verification failed: $e');
      debugPrint('Error type: ${e.runtimeType}');
      return false;
    }
  }

  // Handle successful verification
  void _handleVerificationSuccess() {
    debugPrint('✅ Email verified successfully!');

    // Update UI to show verification success
    setState(() {
      _isVerified = true;
      _successMessage =
          'Email verified successfully! Redirecting to celebration...';
      _errorMessage = null;
    });

    // Cancel the periodic check timer
    _timer?.cancel();

    // Navigate to success celebration screen immediately
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/verification-success');
      }
    });
  }

  Future<void> _manuallyCheckVerification() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      debugPrint('Manual verification check initiated by user');

      // Get current user
      User? user = FirebaseAuth.instance.currentUser;

      if (user == null) {
        setState(() {
          _errorMessage = 'No user is currently signed in';
          _isLoading = false;
        });
        return;
      }

      // Use the same robust token-based verification method
      bool verificationSuccess = await _checkVerificationViaToken(user);

      // Fallback: Try standard reload if token method fails
      if (!verificationSuccess) {
        try {
          debugPrint('Trying standard verification check as fallback...');
          await user.reload();
          user = FirebaseAuth.instance.currentUser;

          if (user!.emailVerified) {
            verificationSuccess = true;
            debugPrint('Standard verification check successful');
          }
        } catch (standardError) {
          debugPrint('Standard verification check failed: $standardError');
        }
      }

      // Handle verification result
      if (verificationSuccess) {
        _handleVerificationSuccess();
      } else {
        setState(() {
          _errorMessage =
              'Email not verified yet. Please check your inbox and click the verification link.';
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error in manual verification check: $e');
      debugPrint('Error type: ${e.runtimeType}');

      setState(() {
        _errorMessage = 'Error checking verification status: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _resendVerificationEmail() async {
    if (!_canResend) {
      setState(() {
        _errorMessage =
            'Please wait $_resendCooldown seconds before requesting another email';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = null;
    });

    try {
      // Use Riverpod auth provider to resend verification email
      await ref.read(authStateProvider.notifier).resendVerificationEmail();

      // Get user email for success message
      final userAsync = ref.read(currentUserProvider);
      String userEmail = 'your email';
      userAsync.when(
        data: (user) => userEmail = user?.email ?? 'your email',
        loading: () => userEmail = 'your email',
        error: (_, __) => userEmail = 'your email',
      );

      setState(() {
        _successMessage =
            'A new verification email has been sent to $userEmail';
        _canResend = false;
        _resendCooldown = _resendCooldownDuration;
      });

      // Start cooldown timer
      _startResendCooldown();
    } catch (e) {
      setState(() {
        _errorMessage = 'Error sending verification email: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _startResendCooldown() {
    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _resendCooldown--;
      });

      if (_resendCooldown <= 0) {
        setState(() {
          _canResend = true;
        });
        timer.cancel();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Get user email from currentUserProvider
    final userAsync = ref.watch(currentUserProvider);
    final userEmail = userAsync.when(
      data: (user) => user?.email ?? '',
      loading: () => '',
      error: (_, __) => '',
    );

    return Scaffold(
      body: AuthGradientBackground(
        child: Column(
          children: [
            // Top section with title and mascot - reduced flex to give more space to bottom
            Expanded(
              flex: 1,
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Back button (optional)
                      Row(
                        children: [
                          IconButton(
                            onPressed: () => Navigator.pop(context),
                            icon: const Icon(
                              Icons.arrow_back_ios,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          const Spacer(),
                        ],
                      ),

                      const SizedBox(height: 20),

                      // Email verification icon with animation
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(26),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          _isVerified
                              ? Icons.verified
                              : Icons.mark_email_unread_outlined,
                          size: 60,
                          color: Colors.white,
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Title
                      Text(
                        _isVerified ? 'Email Verified!' : 'Check Your Email',
                        style: const TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          letterSpacing: -0.5,
                        ),
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 8),

                      // Subtitle
                      Text(
                        _isVerified
                            ? 'Redirecting you to login...'
                            : 'We sent a verification link to your email',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white.withAlpha(179), // 0.7 opacity
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Bottom curved content section - increased flex for more space
            Expanded(
              flex: 4,
              child: SimpleCurvedContainer(
                curveHeight: 30.0,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Column(
                    children: [
                      const SizedBox(height: 16), // Reduced from 20

                      // User email display
                      if (userEmail.isNotEmpty) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor.withAlpha(13),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            userEmail,
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: AppTheme.primaryColor,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16), // Reduced from 20
                      ],

                      // Modern description with better formatting
                      Text(
                        _isVerified
                            ? 'Your email has been verified successfully! You will be redirected to the login screen shortly.'
                            : 'We\'ve sent a verification email to your inbox. Please check your email and click the verification link to activate your account.',
                        style: const TextStyle(
                          fontSize: 16,
                          color: AppTheme.textSecondaryColor,
                          height: 1.6,
                          fontWeight: FontWeight.w400,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 32), // Reduced from 40

                      // Modern progress indicator for verification check
                      if (_isVerified)
                        Container(
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: AppTheme.successColor.withAlpha(13),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: const Column(
                            children: [
                              CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(
                                    AppTheme.successColor),
                                strokeWidth: 3,
                              ),
                              SizedBox(height: 16),
                              Text(
                                'Preparing celebration...',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: AppTheme.successColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),

                      // Modern success message
                      if (_successMessage != null && !_isVerified)
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(20),
                          margin: const EdgeInsets.only(bottom: 24),
                          decoration: BoxDecoration(
                            color: AppTheme.successColor.withAlpha(13),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: AppTheme.successColor.withAlpha(51),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: AppTheme.successColor.withAlpha(26),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.check,
                                  color: AppTheme.successColor,
                                  size: 16,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Text(
                                  _successMessage!,
                                  style: const TextStyle(
                                    color: AppTheme.successColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    height: 1.4,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                      // Modern error message
                      if (_errorMessage != null)
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(20),
                          margin: const EdgeInsets.only(bottom: 24),
                          decoration: BoxDecoration(
                            color: AppTheme.errorColor.withAlpha(13),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: AppTheme.errorColor.withAlpha(51),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: AppTheme.errorColor.withAlpha(26),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.warning_amber_rounded,
                                  color: AppTheme.errorColor,
                                  size: 16,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Text(
                                  _errorMessage!,
                                  style: const TextStyle(
                                    color: AppTheme.errorColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    height: 1.4,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                      // Modern action buttons (only show if not verified)
                      if (!_isVerified) ...[
                        const SizedBox(height: 24),

                        // Check verification status button
                        CustomButton(
                          text: 'Check Verification Status',
                          onPressed: _manuallyCheckVerification,
                          isLoading: _isLoading,
                          type: ButtonType.primary,
                        ),
                        const SizedBox(height: 16),

                        // Resend verification email button with countdown
                        CustomButton(
                          text: _canResend
                              ? 'Resend Verification Email'
                              : 'Resend in $_resendCooldown seconds',
                          onPressed:
                              _canResend ? _resendVerificationEmail : null,
                          isLoading: _isLoading,
                          type: ButtonType.outlined,
                        ),
                        const SizedBox(
                            height: 16), // Reduced from 40 to prevent overflow
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
