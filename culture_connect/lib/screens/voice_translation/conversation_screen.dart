import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/translation/conversation_model.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/providers/voice_translation_enhanced_provider.dart';
import 'package:culture_connect/providers/voice_translation_provider.dart';
import 'package:culture_connect/services/voice_translation/conversation_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/voice_translation/voice_recording_button.dart';
import 'package:culture_connect/widgets/conversation/conversation_turn_widget.dart';
import 'package:culture_connect/widgets/conversation/continuous_listening_indicator.dart';
import 'package:culture_connect/widgets/conversation/conversation_export_dialog.dart';

/// A screen for conversation mode
class ConversationScreen extends ConsumerStatefulWidget {
  /// The conversation ID (null for new conversation)
  final String? conversationId;

  /// Creates a new conversation screen
  const ConversationScreen({
    super.key,
    this.conversationId,
  });

  @override
  ConsumerState<ConversationScreen> createState() => _ConversationScreenState();
}

class _ConversationScreenState extends ConsumerState<ConversationScreen> {
  late ScrollController _scrollController;
  ConversationRole _currentRole = ConversationRole.user;
  bool _isContinuousListeningActive = false;

  // Subscription to conversation events
  StreamSubscription<String>? _conversationEventsSubscription;

  // Subscription to continuous listening status
  StreamSubscription<bool>? _continuousListeningSubscription;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();

    // Initialize conversation
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeConversation();
      _subscribeToEvents();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _conversationEventsSubscription?.cancel();
    _continuousListeningSubscription?.cancel();

    // Stop continuous listening if active
    if (_isContinuousListeningActive) {
      ref.read(conversationServiceProvider).stopContinuousListening();
    }

    super.dispose();
  }

  /// Subscribe to conversation events
  void _subscribeToEvents() {
    // Subscribe to conversation events
    _conversationEventsSubscription = ref
        .read(conversationServiceProvider)
        .conversationEventsStream
        .listen((event) {
      // Show a snackbar with the event
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(event),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    });

    // Subscribe to continuous listening status
    _continuousListeningSubscription = ref
        .read(conversationServiceProvider)
        .continuousListeningStream
        .listen((isActive) {
      setState(() {
        _isContinuousListeningActive = isActive;
      });
    });
  }

  Future<void> _initializeConversation() async {
    if (widget.conversationId != null) {
      // Load existing conversation
      ref
          .read(conversationServiceProvider)
          .setCurrentConversation(widget.conversationId);
    } else {
      // Start a new conversation
      final userLanguage = ref.read(sourceLanguageProvider);
      final otherLanguage = ref.read(targetLanguageProvider);

      // Start a new conversation with the conversation service directly
      await ref.read(conversationServiceProvider).startConversation(
            userLanguageCode: userLanguage.code,
            otherLanguageCode: otherLanguage.code,
            autoDetectLanguage: false,
            speakerIdentification: false,
            listeningMode: ConversationListeningMode.manual,
            settings: const ConversationSettings(),
          );
    }
  }

  /// Show the export dialog
  Future<void> _showExportDialog(BuildContext context) async {
    final conversation = ref.read(currentConversationProvider).value;
    if (conversation == null) return;

    if (!conversation.canExport) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content:
              Text('No conversation to export. Start a conversation first.'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    await showDialog(
      context: context,
      builder: (context) => ConversationExportDialog(
        conversation: conversation,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final conversationAsync = ref.watch(currentConversationProvider);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Conversation',
        showBackButton: true,
        actions: [
          // Export button
          IconButton(
            icon: const Icon(Icons.ios_share),
            onPressed: () => _showExportDialog(context),
            tooltip: 'Export',
          ),

          // Settings button
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showConversationSettings(context),
            tooltip: 'Settings',
          ),
        ],
      ),
      body: conversationAsync.when(
        data: (conversation) {
          if (conversation == null) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return Column(
            children: [
              // Language bar
              _buildLanguageBar(conversation),

              // Conversation content
              Expanded(
                child: _buildConversationContent(conversation),
              ),

              // Bottom controls
              _buildBottomControls(conversation),
            ],
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }

  Widget _buildLanguageBar(ConversationModel conversation) {
    final userLanguage = supportedLanguages.firstWhere(
      (lang) => lang.code == conversation.userLanguageCode,
      orElse: () => supportedLanguages.first,
    );

    final otherLanguage = supportedLanguages.firstWhere(
      (lang) => lang.code == conversation.otherLanguageCode,
      orElse: () => supportedLanguages.first,
    );

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // User language
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: _currentRole == ConversationRole.user
                    ? AppTheme.primaryColor.withAlpha(25)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _currentRole == ConversationRole.user
                      ? AppTheme.primaryColor
                      : Colors.transparent,
                  width: 1,
                ),
              ),
              child: InkWell(
                onTap: () =>
                    setState(() => _currentRole = ConversationRole.user),
                borderRadius: BorderRadius.circular(8),
                child: Column(
                  children: [
                    Text(
                      userLanguage.flag,
                      style: TextStyle(
                        fontSize: 24,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'You (${userLanguage.name})',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: _currentRole == ConversationRole.user
                            ? AppTheme.primaryColor
                            : AppTheme.textSecondaryColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Swap button
          IconButton(
            icon: Icon(
              Icons.swap_horiz,
              color: AppTheme.primaryColor,
              size: 24,
            ),
            onPressed: () {
              setState(() {
                _currentRole = _currentRole == ConversationRole.user
                    ? ConversationRole.other
                    : ConversationRole.user;
              });
            },
            tooltip: 'Switch speaker',
          ),

          // Other language
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: _currentRole == ConversationRole.other
                    ? AppTheme.primaryColor.withAlpha(25)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _currentRole == ConversationRole.other
                      ? AppTheme.primaryColor
                      : Colors.transparent,
                  width: 1,
                ),
              ),
              child: InkWell(
                onTap: () =>
                    setState(() => _currentRole = ConversationRole.other),
                borderRadius: BorderRadius.circular(8),
                child: Column(
                  children: [
                    Text(
                      otherLanguage.flag,
                      style: TextStyle(
                        fontSize: 24,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'Other (${otherLanguage.name})',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: _currentRole == ConversationRole.other
                            ? AppTheme.primaryColor
                            : AppTheme.textSecondaryColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConversationContent(ConversationModel conversation) {
    if (conversation.turns.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.forum,
              size: 64,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16),
            Text(
              'Start a conversation',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            SizedBox(height: 8),
            Text(
              conversation.listeningMode == ConversationListeningMode.continuous
                  ? 'Continuous listening mode is active'
                  : 'Tap the microphone button to start speaking',
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            if (conversation.listeningMode ==
                ConversationListeningMode.continuous) ...[
              SizedBox(height: 24),
              ContinuousListeningIndicator(
                isActive: _isContinuousListeningActive,
                currentRole: conversation.lastActiveRole,
                size: 32,
              ),
            ],
          ],
        ),
      );
    }

    return Stack(
      children: [
        // Conversation turns
        ListView.builder(
          controller: _scrollController,
          padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
          itemCount: conversation.turns.length,
          itemBuilder: (context, index) {
            final turn = conversation.turns[index];
            return ConversationTurnWidget(
              turn: turn,
              showTranslation: true,
              showCulturalContext: conversation.settings.showCulturalContext,
              showSlangIdiom: conversation.settings.showSlangIdiom,
              showPronunciation: conversation.settings.showPronunciation,
              onPlayPressed: () {
                ref.read(currentVoiceTranslationProvider.notifier).state =
                    turn.translation;
                ref
                    .read(voiceTranslationNotifierProvider.notifier)
                    .playTranslatedAudio();
              },
              onPausePressed: () {
                // For now, we'll just stop the playback since pausePlayback is not implemented
                ref.read(voiceTranslationNotifierProvider.notifier).stopAudio();
              },
              onStopPressed: () {
                ref.read(voiceTranslationNotifierProvider.notifier).stopAudio();
              },
            );
          },
        ),

        // Continuous listening indicator (if active)
        if (conversation.listeningMode ==
                ConversationListeningMode.continuous &&
            _isContinuousListeningActive)
          Positioned(
            top: 16,
            right: 16,
            child: ContinuousListeningIndicator(
              isActive: _isContinuousListeningActive,
              currentRole: conversation.lastActiveRole,
              size: 24,
              showLabel: false,
            ),
          ),
      ],
    );
  }

  // This method is no longer needed as we're using the ConversationTurnWidget

  Widget _buildBottomControls(ConversationModel conversation) {
    final isRecording = ref.watch(isRecordingProvider);

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Continuous listening controls (if enabled)
          if (conversation.listeningMode ==
              ConversationListeningMode.continuous) ...[
            Row(
              children: [
                // Continuous listening indicator
                ContinuousListeningIndicator(
                  isActive: _isContinuousListeningActive,
                  currentRole: conversation.lastActiveRole,
                  size: 24,
                ),

                const Spacer(),

                // Toggle button
                ElevatedButton.icon(
                  onPressed: () => _toggleContinuousListening(conversation),
                  icon: Icon(
                    _isContinuousListeningActive
                        ? Icons.pause
                        : Icons.play_arrow,
                    size: 20,
                  ),
                  label: Text(
                    _isContinuousListeningActive ? 'Pause' : 'Start',
                    style: TextStyle(
                      fontSize: 14,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isContinuousListeningActive
                        ? Colors.orange
                        : AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Divider(height: 1),
            const SizedBox(height: 16),
          ],

          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Recording button (only shown in manual mode)
              if (conversation.listeningMode ==
                  ConversationListeningMode.manual)
                VoiceRecordingButton(
                  size: 64,
                  color: _currentRole == ConversationRole.user
                      ? AppTheme.primaryColor
                      : Colors.orange,
                  // We'll update the role when recording starts in the onTap handler
                ),

              if (conversation.listeningMode ==
                  ConversationListeningMode.manual)
                const SizedBox(width: 16),

              // Recording status
              if (isRecording)
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Recording...',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _currentRole == ConversationRole.user
                            ? 'Speaking as You'
                            : 'Speaking as Other',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// Toggle continuous listening mode
  Future<void> _toggleContinuousListening(
      ConversationModel conversation) async {
    if (_isContinuousListeningActive) {
      // Stop continuous listening
      await ref.read(conversationServiceProvider).stopContinuousListening();
    } else {
      // Start continuous listening
      await ref
          .read(conversationServiceProvider)
          .startContinuousListening(conversation.id);
    }
  }

  // Helper method to format time (used by ConversationTurnWidget)

  Future<void> _showConversationSettings(BuildContext context) async {
    final conversation = ref.read(currentConversationProvider).value;
    if (conversation == null) return;

    bool autoDetectLanguage = conversation.autoDetectLanguage;
    bool speakerIdentification = conversation.speakerIdentification;
    String title = conversation.title;

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Conversation Settings'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title
              TextFormField(
                initialValue: title,
                decoration: const InputDecoration(
                  labelText: 'Conversation Title',
                  hintText: 'Enter a title for this conversation',
                ),
                onChanged: (value) {
                  title = value;
                },
              ),

              const SizedBox(height: 16),

              // Auto-detect language
              SwitchListTile(
                title: const Text('Auto-detect Language'),
                subtitle:
                    const Text('Automatically detect the spoken language'),
                value: autoDetectLanguage,
                onChanged: (value) {
                  autoDetectLanguage = value;
                },
              ),

              // Speaker identification
              SwitchListTile(
                title: const Text('Speaker Identification'),
                subtitle: const Text('Automatically identify who is speaking'),
                value: speakerIdentification,
                onChanged: (value) {
                  speakerIdentification = value;
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // Get the navigator before the async gap
              final navigator = Navigator.of(context);

              // Update conversation settings
              ref
                  .read(conversationNotifierProvider.notifier)
                  .updateConversationSettings(
                    conversationId: conversation.id,
                    title: title,
                    autoDetectLanguage: autoDetectLanguage,
                    speakerIdentification: speakerIdentification,
                  )
                  .then((_) {
                // Pop after the settings are updated
                navigator.pop();
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }
}
