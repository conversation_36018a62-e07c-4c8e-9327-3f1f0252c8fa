import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/translation/custom_vocabulary_model.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/providers/voice_translation_enhanced_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';

/// A screen for managing custom vocabulary
class CustomVocabularyScreen extends ConsumerStatefulWidget {
  /// Creates a new custom vocabulary screen
  const CustomVocabularyScreen({super.key});

  @override
  ConsumerState<CustomVocabularyScreen> createState() =>
      _CustomVocabularyScreenState();
}

class _CustomVocabularyScreenState extends ConsumerState<CustomVocabularyScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final vocabularyTermsAsync = ref.watch(customVocabularyTermsProvider);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Custom Vocabulary',
        showBackButton: true,
      ),
      body: Column(
        children: [
          // Tab bar
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'All'),
              Tab(text: 'By Category'),
              Tab(text: 'Favorites'),
            ],
            labelColor: AppTheme.primaryColor,
            unselectedLabelColor: AppTheme.textSecondaryColor,
            indicatorColor: AppTheme.primaryColor,
          ),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // All terms
                vocabularyTermsAsync.when(
                  data: (terms) => _buildVocabularyTermsList(terms),
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stackTrace) => Center(
                    child: Text('Error: $error'),
                  ),
                ),

                // By category
                _buildCategoriesView(),

                // Favorites
                _buildFavoritesView(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddTermDialog(context),
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildVocabularyTermsList(List<CustomVocabularyModel> terms) {
    if (terms.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.menu_book,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No custom vocabulary terms',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Add custom terms to improve translation quality',
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _showAddTermDialog(context),
              icon: const Icon(Icons.add),
              label: const Text('Add Term'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
            ),
          ],
        ),
      );
    }

    // Sort terms by usage count (most used first)
    final sortedTerms = List<CustomVocabularyModel>.from(terms)
      ..sort((a, b) => b.usageCount.compareTo(a.usageCount));

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: sortedTerms.length,
      itemBuilder: (context, index) {
        final term = sortedTerms[index];
        return _buildVocabularyTermItem(term);
      },
    );
  }

  Widget _buildCategoriesView() {
    return Consumer(
      builder: (context, ref, child) {
        final terms = ref.watch(customVocabularyTermsProvider).value ?? [];

        if (terms.isEmpty) {
          return Center(
            child: Text(
              'No custom vocabulary terms',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          );
        }

        // Group terms by category
        final categorizedTerms =
            <VocabularyCategory, List<CustomVocabularyModel>>{};

        for (final term in terms) {
          if (!categorizedTerms.containsKey(term.category)) {
            categorizedTerms[term.category] = [];
          }

          categorizedTerms[term.category]!.add(term);
        }

        // Sort categories by number of terms (most terms first)
        final sortedCategories = categorizedTerms.keys.toList()
          ..sort((a, b) => categorizedTerms[b]!
              .length
              .compareTo(categorizedTerms[a]!.length));

        return ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: sortedCategories.length,
          itemBuilder: (context, index) {
            final category = sortedCategories[index];
            final categoryTerms = categorizedTerms[category]!;

            return _buildCategorySection(category, categoryTerms);
          },
        );
      },
    );
  }

  Widget _buildFavoritesView() {
    return Consumer(
      builder: (context, ref, child) {
        final terms = ref.watch(favoriteCustomVocabularyTermsProvider);

        if (terms.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.favorite_border,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'No favorite terms',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Mark terms as favorites to see them here',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: terms.length,
          itemBuilder: (context, index) {
            final term = terms[index];
            return _buildVocabularyTermItem(term);
          },
        );
      },
    );
  }

  Widget _buildCategorySection(
      VocabularyCategory category, List<CustomVocabularyModel> terms) {
    // Get the first term to get the category name
    final categoryName = terms.first.getCategoryName();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Category header
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Icon(
                _getCategoryIcon(category),
                size: 20,
                color: AppTheme.primaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                categoryName,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withAlpha(26),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${terms.length}',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Category terms
        ...terms.map((term) => _buildVocabularyTermItem(term)),

        // Divider
        const Divider(height: 32),
      ],
    );
  }

  Widget _buildVocabularyTermItem(CustomVocabularyModel term) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Term header
            Row(
              children: [
                // Category and language
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withAlpha(26),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getCategoryIcon(term.category),
                        size: 16,
                        color: AppTheme.primaryColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        term.getCategoryName(),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 8),

                // Language
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        term.originalLanguageCode.toLanguageFlag(),
                        style: TextStyle(
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        term.originalLanguageCode.toLanguageName(),
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),

                const Spacer(),

                // Usage count
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.amber.withAlpha(26),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.history,
                        size: 16,
                        color: Colors.amber[700],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Used ${term.usageCount} times',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.amber[700],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Original term
            Text(
              term.originalTerm,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            const SizedBox(height: 4),

            // Description
            if (term.description != null) ...[
              Text(
                term.description!,
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              const SizedBox(height: 12),
            ],

            // Translations
            if (term.translations.isNotEmpty) ...[
              Text(
                'Translations:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: term.translations.entries.map((entry) {
                  return Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.grey[300]!,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          entry.key.toLanguageFlag(),
                          style: TextStyle(
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          entry.value,
                          style: TextStyle(
                            fontSize: 12,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),
            ],

            // Actions
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // Edit button
                IconButton(
                  icon: Icon(
                    Icons.edit,
                    size: 20,
                    color: AppTheme.primaryColor,
                  ),
                  onPressed: () => _showEditTermDialog(context, term),
                  tooltip: 'Edit',
                ),

                // Favorite button
                IconButton(
                  icon: Icon(
                    term.isFavorite ? Icons.favorite : Icons.favorite_border,
                    size: 20,
                    color: term.isFavorite ? Colors.red : Colors.grey,
                  ),
                  onPressed: () => _toggleFavorite(term.id),
                  tooltip: term.isFavorite
                      ? 'Remove from favorites'
                      : 'Add to favorites',
                ),

                // Delete button
                IconButton(
                  icon: Icon(
                    Icons.delete,
                    size: 20,
                    color: Colors.red,
                  ),
                  onPressed: () => _confirmDeleteTerm(term),
                  tooltip: 'Delete',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  IconData _getCategoryIcon(VocabularyCategory category) {
    switch (category) {
      case VocabularyCategory.general:
        return Icons.language;
      case VocabularyCategory.medical:
        return Icons.medical_services;
      case VocabularyCategory.technical:
        return Icons.build;
      case VocabularyCategory.business:
        return Icons.business;
      case VocabularyCategory.legal:
        return Icons.gavel;
      case VocabularyCategory.academic:
        return Icons.school;
      case VocabularyCategory.cultural:
        return Icons.public;
      case VocabularyCategory.travel:
        return Icons.flight;
      case VocabularyCategory.food:
        return Icons.restaurant;
      case VocabularyCategory.custom:
        return Icons.category;
    }
  }

  Future<void> _showAddTermDialog(BuildContext context) async {
    final formKey = GlobalKey<FormState>();
    String originalTerm = '';
    String originalLanguageCode = supportedLanguages.first.code;
    final translations = <String, String>{};
    VocabularyCategory category = VocabularyCategory.general;
    String? customCategory;
    String? description;

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Custom Term'),
        content: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Original term
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'Original Term',
                    hintText: 'Enter the term in the original language',
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter the original term';
                    }
                    return null;
                  },
                  onSaved: (value) {
                    originalTerm = value!;
                  },
                ),

                SizedBox(height: 16),

                // Original language
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Original Language',
                  ),
                  value: originalLanguageCode,
                  items: supportedLanguages.map((language) {
                    return DropdownMenuItem<String>(
                      value: language.code,
                      child: Row(
                        children: [
                          Text(language.flag),
                          SizedBox(width: 8),
                          Text(language.name),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    originalLanguageCode = value!;
                  },
                ),

                SizedBox(height: 16),

                // Category
                DropdownButtonFormField<VocabularyCategory>(
                  decoration: const InputDecoration(
                    labelText: 'Category',
                  ),
                  value: category,
                  items: VocabularyCategory.values.map((cat) {
                    return DropdownMenuItem<VocabularyCategory>(
                      value: cat,
                      child: Row(
                        children: [
                          Icon(_getCategoryIcon(cat), size: 16),
                          SizedBox(width: 8),
                          Text(cat.toString().split('.').last),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    category = value!;
                  },
                ),

                SizedBox(height: 16),

                // Custom category (if category is custom)
                if (category == VocabularyCategory.custom)
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Custom Category Name',
                      hintText: 'Enter a name for your custom category',
                    ),
                    validator: (value) {
                      if (category == VocabularyCategory.custom &&
                          (value == null || value.isEmpty)) {
                        return 'Please enter a custom category name';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      customCategory = value;
                    },
                  ),

                SizedBox(height: 16),

                // Description
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'Description (Optional)',
                    hintText: 'Enter a description or context for this term',
                  ),
                  maxLines: 2,
                  onSaved: (value) {
                    description = value;
                  },
                ),

                SizedBox(height: 16),

                // Translations
                const Text(
                  'Translations:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),

                SizedBox(height: 8),

                // Add translation button
                ElevatedButton.icon(
                  onPressed: () {
                    // Show dialog to add translation
                  },
                  icon: const Icon(Icons.add),
                  label: const Text('Add Translation'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (formKey.currentState!.validate()) {
                formKey.currentState!.save();

                // Add the term
                ref
                    .read(customVocabularyNotifierProvider.notifier)
                    .addVocabularyTerm(
                      originalTerm: originalTerm,
                      originalLanguageCode: originalLanguageCode,
                      translations: translations,
                      category: category,
                      customCategory: customCategory,
                      description: description,
                    );

                Navigator.pop(context);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  Future<void> _showEditTermDialog(
      BuildContext context, CustomVocabularyModel term) async {
    // Similar to _showAddTermDialog but pre-filled with term data
  }

  Future<void> _toggleFavorite(String termId) async {
    try {
      await ref
          .read(customVocabularyNotifierProvider.notifier)
          .toggleFavorite(termId);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error toggling favorite: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _confirmDeleteTerm(CustomVocabularyModel term) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Term'),
        content: Text(
          'Are you sure you want to delete "${term.originalTerm}"? '
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref
            .read(customVocabularyNotifierProvider.notifier)
            .deleteVocabularyTerm(term.id);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Term deleted successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error deleting term: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
