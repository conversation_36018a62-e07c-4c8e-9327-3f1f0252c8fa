/// A screen for setting dialect and accent preferences for voice translation
library dialect_accent_preferences;

// Package imports
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports - Models
import 'package:culture_connect/models/translation/accent_model.dart';
import 'package:culture_connect/models/translation/dialect_model.dart';
import 'package:culture_connect/models/translation/language_model.dart';

// Project imports - Services
import 'package:culture_connect/services/voice_translation/dialect_accent_detection_service.dart';

// Project imports - Theme
import 'package:culture_connect/theme/app_theme.dart';

// Project imports - Widgets
import 'package:culture_connect/widgets/custom_app_bar.dart';

/// A screen for setting dialect and accent preferences for voice translation
class DialectAccentPreferencesScreen extends ConsumerStatefulWidget {
  /// Creates a new dialect accent preferences screen
  const DialectAccentPreferencesScreen({super.key});

  @override
  ConsumerState<DialectAccentPreferencesScreen> createState() =>
      _DialectAccentPreferencesScreenState();
}

/// State for the dialect accent preferences screen
class _DialectAccentPreferencesScreenState
    extends ConsumerState<DialectAccentPreferencesScreen> {
  /// Whether to use dialect detection
  bool _useDialectDetection = true;

  /// Whether to use accent detection
  bool _useAccentDetection = true;

  /// The minimum confidence threshold for dialect detection (0.0 to 1.0)
  double _dialectConfidenceThreshold = 0.6;

  /// The minimum confidence threshold for accent detection (0.0 to 1.0)
  double _accentConfidenceThreshold = 0.5;

  /// Map of language codes to preferred dialect codes
  final Map<String, String> _preferredDialects = {};

  /// Map of dialect codes to preferred accent codes
  final Map<String, String> _preferredAccents = {};

  @override
  void initState() {
    super.initState();

    // Load the current settings
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadSettings();
    });
  }

  /// Load the current dialect and accent settings from the service
  void _loadSettings() {
    final service = ref.read(dialectAccentDetectionServiceProvider);

    if (mounted) {
      setState(() {
        _useDialectDetection = service.useDialectDetection;
        _useAccentDetection = service.useAccentDetection;

        // Load preferred dialects
        for (final language in supportedLanguages) {
          final preferredDialect = service.getPreferredDialect(language.code);
          if (preferredDialect != null) {
            _preferredDialects[language.code] = preferredDialect;

            // Load preferred accents for this dialect
            final preferredAccent =
                service.getPreferredAccent(preferredDialect);
            if (preferredAccent != null) {
              _preferredAccents[preferredDialect] = preferredAccent;
            }
          }
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Dialect & Accent Preferences',
        showBackButton: true,
      ),
      body: ListView(
        padding: EdgeInsets.all(16),
        children: [
          // Detection settings
          _buildSectionHeader('Detection Settings'),

          SwitchListTile(
            title: const Text('Dialect Detection'),
            subtitle:
                const Text('Automatically detect dialects in text and speech'),
            value: _useDialectDetection,
            onChanged: (value) {
              setState(() {
                _useDialectDetection = value;
              });
              ref
                  .read(dialectAccentDetectionServiceProvider)
                  .setUseDialectDetection(value);
            },
            activeColor: AppTheme.primaryColor,
          ),

          SwitchListTile(
            title: const Text('Accent Detection'),
            subtitle: const Text('Automatically detect accents in speech'),
            value: _useAccentDetection,
            onChanged: (value) {
              setState(() {
                _useAccentDetection = value;
              });
              ref
                  .read(dialectAccentDetectionServiceProvider)
                  .setUseAccentDetection(value);
            },
            activeColor: AppTheme.primaryColor,
          ),

          // Confidence thresholds
          _buildSectionHeader('Confidence Thresholds'),

          _buildSliderSetting(
            'Dialect Confidence',
            'Minimum confidence required for dialect detection',
            _dialectConfidenceThreshold,
            (value) {
              setState(() {
                _dialectConfidenceThreshold = value;
              });
              ref
                  .read(dialectAccentDetectionServiceProvider)
                  .setDialectConfidenceThreshold(value);
            },
          ),

          _buildSliderSetting(
            'Accent Confidence',
            'Minimum confidence required for accent detection',
            _accentConfidenceThreshold,
            (value) {
              setState(() {
                _accentConfidenceThreshold = value;
              });
              ref
                  .read(dialectAccentDetectionServiceProvider)
                  .setAccentConfidenceThreshold(value);
            },
          ),

          // Preferred dialects
          _buildSectionHeader('Preferred Dialects'),

          ...supportedLanguages.map((language) {
            return _buildLanguageDialectSetting(language);
          }),

          const SizedBox(height: 16),

          // Clear preferences button
          ElevatedButton.icon(
            onPressed: _clearPreferences,
            icon: const Icon(Icons.clear_all),
            label: const Text('Clear All Preferences'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// Build a section header
  ///
  /// [title] The title of the section
  /// Returns a widget displaying the section header
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }

  /// Build a slider setting
  ///
  /// [title] The title of the slider
  /// [subtitle] The description of the slider
  /// [value] The current value of the slider
  /// [onChanged] Callback when the slider value changes
  /// Returns a widget displaying a slider with title and value
  Widget _buildSliderSetting(
    String title,
    String subtitle,
    double value,
    ValueChanged<double> onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              Text(
                subtitle,
                style: const TextStyle(
                  fontSize: 12,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
        ),
        Row(
          children: [
            Expanded(
              child: Slider(
                value: value,
                min: 0.0,
                max: 1.0,
                divisions: 10,
                label: value.toStringAsFixed(1),
                onChanged: onChanged,
                activeColor: AppTheme.primaryColor,
              ),
            ),
            SizedBox(
              width: 50,
              child: Text(
                value.toStringAsFixed(1),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build a language dialect setting
  ///
  /// [language] The language model to build the setting for
  /// Returns a widget displaying dialect and accent preferences for a language
  Widget _buildLanguageDialectSetting(LanguageModel language) {
    // Get the preferred dialect for this language
    final preferredDialectCode = _preferredDialects[language.code];

    // Find the dialect model
    DialectModel? preferredDialect;
    if (preferredDialectCode != null) {
      for (final dialect in language.dialects) {
        if (dialect.code == preferredDialectCode) {
          preferredDialect = dialect;
          break;
        }
      }
    }

    return Card(
      margin: EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Language header
            Row(
              children: [
                Text(
                  language.flag,
                  style: const TextStyle(
                    fontSize: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  language.name,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Dialect dropdown
            DropdownButtonFormField<String?>(
              decoration: const InputDecoration(
                labelText: 'Preferred Dialect',
                border: OutlineInputBorder(),
              ),
              value: preferredDialectCode,
              items: [
                const DropdownMenuItem<String?>(
                  value: null,
                  child: Text('Auto-detect (Default)'),
                ),
                ...language.dialects.map((dialect) {
                  return DropdownMenuItem<String?>(
                    value: dialect.code,
                    child: Text(dialect.name),
                  );
                }),
              ],
              onChanged: (value) {
                setState(() {
                  if (value == null) {
                    _preferredDialects.remove(language.code);
                  } else {
                    _preferredDialects[language.code] = value;
                  }
                });

                // Update the preferred dialect
                if (value == null) {
                  // Remove the preferred dialect
                  ref
                      .read(dialectAccentDetectionServiceProvider)
                      .setPreferredDialect(language.code, '');
                } else {
                  // Set the preferred dialect
                  ref
                      .read(dialectAccentDetectionServiceProvider)
                      .setPreferredDialect(language.code, value);
                }
              },
            ),

            // Accent dropdown (if a dialect is selected)
            if (preferredDialect != null &&
                preferredDialect.accentVariants.isNotEmpty) ...[
              const SizedBox(height: 12),
              DropdownButtonFormField<String?>(
                decoration: const InputDecoration(
                  labelText: 'Preferred Accent',
                  border: OutlineInputBorder(),
                ),
                value: _preferredAccents[preferredDialect.code],
                items: [
                  const DropdownMenuItem<String?>(
                    value: null,
                    child: Text('Auto-detect (Default)'),
                  ),
                  ...preferredDialect.accentVariants.map((accentCode) {
                    return DropdownMenuItem<String?>(
                      value: accentCode,
                      child: Text(_getAccentName(accentCode)),
                    );
                  }),
                ],
                onChanged: (value) {
                  setState(() {
                    if (value == null) {
                      _preferredAccents.remove(preferredDialect!.code);
                    } else {
                      _preferredAccents[preferredDialect!.code] = value;
                    }
                  });

                  // Update the preferred accent
                  if (value == null) {
                    // Remove the preferred accent
                    ref
                        .read(dialectAccentDetectionServiceProvider)
                        .setPreferredAccent(preferredDialect!.code, '');
                  } else {
                    // Set the preferred accent
                    ref
                        .read(dialectAccentDetectionServiceProvider)
                        .setPreferredAccent(preferredDialect!.code, value);
                  }
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Get a human-readable name for an accent code
  ///
  /// [accentCode] The accent code to convert to a human-readable name
  /// Returns a formatted accent name (e.g., 'en-us-southern' -> 'Southern')
  String _getAccentName(String accentCode) {
    // Parse the accent code (e.g., 'en-us-southern' -> 'Southern')
    final parts = accentCode.split('-');
    if (parts.length < 3) {
      return accentCode;
    }

    final accentName = parts.sublist(2).join('-');

    // Capitalize the accent name
    return accentName.split('-').map((part) {
      return part.isNotEmpty ? part[0].toUpperCase() + part.substring(1) : '';
    }).join(' ');
  }

  /// Clear all dialect and accent preferences
  /// Shows a confirmation dialog before clearing
  void _clearPreferences() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Preferences'),
        content: const Text(
            'Are you sure you want to clear all dialect and accent preferences?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Clear the preferences
              setState(() {
                _preferredDialects.clear();
                _preferredAccents.clear();
              });

              // Clear the detection caches
              ref.read(dialectAccentDetectionServiceProvider).clearCaches();

              // Close the dialog
              Navigator.of(context).pop();

              // Show a snackbar
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('All preferences cleared'),
                    duration: Duration(seconds: 2),
                  ),
                );
              }
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }
}
