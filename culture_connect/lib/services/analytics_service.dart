import 'dart:async';

// Package imports
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Project imports
import 'package:culture_connect/services/error_handling_service.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Provider for the AnalyticsService
final analyticsServiceProvider = Provider<AnalyticsService>((ref) {
  final loggingService = ref.watch(loggingServiceProvider);
  final errorHandlingService = ref.watch(errorHandlingServiceProvider);
  return AnalyticsService(loggingService, errorHandlingService);
});

/// Analytics event categories
enum AnalyticsCategory {
  navigation,
  userAction,
  engagement,
  error,
  performance,
  ar,
  booking,
  payment,
  safety,
  messaging,
  search,
  content,
  social,
  onboarding,
  authentication,
}

/// A comprehensive analytics service for the application
class AnalyticsService {
  final LoggingService _loggingService;
  final ErrorHandlingService _errorHandlingService;

  /// Firebase Analytics instance
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;

  /// Whether analytics is enabled
  bool _analyticsEnabled = true;

  /// User properties
  final Map<String, String> _userProperties = {};

  /// Session start time
  DateTime? _sessionStartTime;

  /// Screen viewing times
  final Map<String, DateTime> _screenViewTimes = {};

  /// Constructor
  AnalyticsService(this._loggingService, this._errorHandlingService);

  /// Initialize the analytics service
  Future<void> initialize() async {
    try {
      // Check if analytics is enabled in preferences
      final prefs = await SharedPreferences.getInstance();
      _analyticsEnabled = prefs.getBool('analytics_enabled') ?? true;

      // Set analytics collection enabled
      await _analytics.setAnalyticsCollectionEnabled(_analyticsEnabled);

      // Start session
      _startSession();

      _loggingService.info('AnalyticsService', 'Analytics service initialized');
    } catch (e, stackTrace) {
      await _errorHandlingService.handleError(
        error: e,
        context: 'AnalyticsService.initialize',
        stackTrace: stackTrace,
        type: ErrorType.unknown,
        severity: ErrorSeverity.medium,
      );
    }
  }

  /// Enable or disable analytics
  Future<void> setAnalyticsEnabled(bool enabled) async {
    try {
      _analyticsEnabled = enabled;

      // Save preference
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('analytics_enabled', enabled);

      // Update Firebase Analytics
      await _analytics.setAnalyticsCollectionEnabled(enabled);

      _loggingService.info(
          'AnalyticsService', 'Analytics ${enabled ? 'enabled' : 'disabled'}');
    } catch (e, stackTrace) {
      await _errorHandlingService.handleError(
        error: e,
        context: 'AnalyticsService.setAnalyticsEnabled',
        stackTrace: stackTrace,
        type: ErrorType.unknown,
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Start a new session
  void _startSession() {
    _sessionStartTime = DateTime.now();
    logEvent(
      name: 'app_session_begin',
      category: AnalyticsCategory.engagement,
      parameters: {
        'timestamp': _sessionStartTime!.toIso8601String(),
      },
    );
  }

  /// End the current session
  Future<void> endSession() async {
    if (_sessionStartTime != null) {
      final sessionDuration =
          DateTime.now().difference(_sessionStartTime!).inSeconds;
      await logEvent(
        name: 'app_session_end',
        category: AnalyticsCategory.engagement,
        parameters: {
          'session_duration_seconds': sessionDuration,
        },
      );
      _sessionStartTime = null;
    }
  }

  /// Set a user property
  Future<void> setUserProperty(
      {required String name, required String? value}) async {
    if (!_analyticsEnabled) return;

    try {
      await _analytics.setUserProperty(name: name, value: value);

      if (value != null) {
        _userProperties[name] = value;
      } else {
        _userProperties.remove(name);
      }

      _loggingService.debug(
          'AnalyticsService', 'Set user property: $name = $value');
    } catch (e, stackTrace) {
      await _errorHandlingService.handleError(
        error: e,
        context: 'AnalyticsService.setUserProperty',
        stackTrace: stackTrace,
        type: ErrorType.unknown,
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Set the user ID
  Future<void> setUserId(String? userId) async {
    if (!_analyticsEnabled) return;

    try {
      await _analytics.setUserId(id: userId);

      // Also set in Crashlytics
      if (userId != null) {
        await FirebaseCrashlytics.instance.setUserIdentifier(userId);
      }

      _loggingService.debug('AnalyticsService', 'Set user ID: $userId');
    } catch (e, stackTrace) {
      await _errorHandlingService.handleError(
        error: e,
        context: 'AnalyticsService.setUserId',
        stackTrace: stackTrace,
        type: ErrorType.unknown,
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Log a screen view
  Future<void> logScreenView({
    required String screenName,
    String? screenClass,
  }) async {
    if (!_analyticsEnabled) return;

    try {
      // Log previous screen duration if exists
      await _logScreenExit(screenName);

      // Log screen view using the new method
      await _analytics.logScreenView(
        screenName: screenName,
        screenClass: screenClass ?? 'Unknown',
      );

      // Store screen view time
      _screenViewTimes[screenName] = DateTime.now();

      await logEvent(
        name: 'screen_view',
        category: AnalyticsCategory.navigation,
        parameters: {
          'screen_name': screenName,
          'screen_class': screenClass ?? '',
        },
      );

      _loggingService.debug('AnalyticsService', 'Screen view: $screenName');
    } catch (e, stackTrace) {
      await _errorHandlingService.handleError(
        error: e,
        context: 'AnalyticsService.logScreenView',
        stackTrace: stackTrace,
        type: ErrorType.unknown,
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Log screen exit and duration
  Future<void> _logScreenExit(String newScreenName) async {
    // Find the current screen (the one being exited)
    final currentScreenEntry = _screenViewTimes.entries
        .where((entry) => entry.key != newScreenName)
        .toList()
        .lastOrNull;

    if (currentScreenEntry != null) {
      final screenName = currentScreenEntry.key;
      final viewTime = currentScreenEntry.value;
      final duration = DateTime.now().difference(viewTime).inSeconds;

      await logEvent(
        name: 'screen_exit',
        category: AnalyticsCategory.navigation,
        parameters: {
          'screen_name': screenName,
          'duration_seconds': duration,
          'next_screen': newScreenName,
        },
      );

      // Remove the screen from tracking
      _screenViewTimes.remove(screenName);
    }
  }

  /// Log a custom event
  Future<void> logEvent({
    required String name,
    required AnalyticsCategory category,
    Map<String, dynamic>? parameters,
  }) async {
    if (!_analyticsEnabled) return;

    try {
      // Add category to parameters
      final eventParams = {
        'category': category.name,
        ...?parameters,
      };

      // Convert all values to strings, numbers, or booleans as required by Firebase
      final convertedParams = _convertParameters(eventParams);

      await _analytics.logEvent(
        name: name,
        parameters: convertedParams,
      );

      _loggingService.debug(
        'AnalyticsService',
        'Event: $name',
        {'category': category.name, 'parameters': parameters},
      );
    } catch (e, stackTrace) {
      await _errorHandlingService.handleError(
        error: e,
        context: 'AnalyticsService.logEvent',
        stackTrace: stackTrace,
        type: ErrorType.unknown,
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Convert parameters to types supported by Firebase Analytics
  Map<String, dynamic> _convertParameters(Map<String, dynamic> parameters) {
    final result = <String, dynamic>{};

    for (final entry in parameters.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value == null) {
        // Skip null values
        continue;
      } else if (value is String || value is num || value is bool) {
        // These types are directly supported
        result[key] = value;
      } else if (value is DateTime) {
        // Convert DateTime to ISO string
        result[key] = value.toIso8601String();
      } else if (value is Iterable) {
        // Convert iterables to comma-separated strings
        result[key] = value.join(',');
      } else if (value is Map) {
        // Flatten map to key_subkey format
        for (final subEntry in value.entries) {
          final subKey = '${key}_${subEntry.key}';
          final subValue = subEntry.value;

          if (subValue is String || subValue is num || subValue is bool) {
            result[subKey] = subValue;
          } else if (subValue is DateTime) {
            result[subKey] = subValue.toIso8601String();
          } else {
            result[subKey] = subValue.toString();
          }
        }
      } else {
        // Convert other types to string
        result[key] = value.toString();
      }
    }

    return result;
  }

  /// Log an error event
  Future<void> logError({
    required String errorMessage,
    required ErrorType errorType,
    String? errorDetails,
    bool fatal = false,
  }) async {
    if (!_analyticsEnabled) return;

    try {
      await logEvent(
        name: 'app_error',
        category: AnalyticsCategory.error,
        parameters: {
          'error_message': errorMessage,
          'error_type': errorType.name,
          'error_details': errorDetails ?? '',
          'fatal': fatal,
        },
      );
    } catch (e, stackTrace) {
      await _errorHandlingService.handleError(
        error: e,
        context: 'AnalyticsService.logError',
        stackTrace: stackTrace,
        type: ErrorType.unknown,
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Log a performance event
  Future<void> logPerformance({
    required String name,
    required int durationMillis,
    Map<String, dynamic>? parameters,
  }) async {
    if (!_analyticsEnabled) return;

    try {
      await logEvent(
        name: 'performance',
        category: AnalyticsCategory.performance,
        parameters: {
          'name': name,
          'duration_ms': durationMillis,
          ...?parameters,
        },
      );
    } catch (e, stackTrace) {
      await _errorHandlingService.handleError(
        error: e,
        context: 'AnalyticsService.logPerformance',
        stackTrace: stackTrace,
        type: ErrorType.unknown,
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Log a search event
  Future<void> logSearch({
    required String searchTerm,
    String? searchCategory,
    int? resultCount,
  }) async {
    if (!_analyticsEnabled) return;

    try {
      await _analytics.logSearch(
        searchTerm: searchTerm,
        origin: searchCategory,
        numberOfNights: resultCount,
      );

      _loggingService.debug(
        'AnalyticsService',
        'Search: $searchTerm',
        {'category': searchCategory, 'resultCount': resultCount},
      );
    } catch (e, stackTrace) {
      await _errorHandlingService.handleError(
        error: e,
        context: 'AnalyticsService.logSearch',
        stackTrace: stackTrace,
        type: ErrorType.unknown,
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Log a login event
  Future<void> logLogin({required String method}) async {
    if (!_analyticsEnabled) return;

    try {
      await _analytics.logLogin(loginMethod: method);

      _loggingService.debug('AnalyticsService', 'Login: $method');
    } catch (e, stackTrace) {
      await _errorHandlingService.handleError(
        error: e,
        context: 'AnalyticsService.logLogin',
        stackTrace: stackTrace,
        type: ErrorType.unknown,
        severity: ErrorSeverity.low,
      );
    }
  }

  /// Log a sign up event
  Future<void> logSignUp({required String method}) async {
    if (!_analyticsEnabled) return;

    try {
      await _analytics.logSignUp(signUpMethod: method);

      _loggingService.debug('AnalyticsService', 'Sign up: $method');
    } catch (e, stackTrace) {
      await _errorHandlingService.handleError(
        error: e,
        context: 'AnalyticsService.logSignUp',
        stackTrace: stackTrace,
        type: ErrorType.unknown,
        severity: ErrorSeverity.low,
      );
    }
  }
}
