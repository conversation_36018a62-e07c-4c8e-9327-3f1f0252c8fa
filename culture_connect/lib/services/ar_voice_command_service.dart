import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:speech_to_text/speech_recognition_result.dart';
import 'package:flutter/services.dart';

/// A service that provides voice command functionality for the AR experience.
class ARVoiceCommandService {
  // Speech to text instance
  final stt.SpeechToText _speech = stt.SpeechToText();

  // Voice command state
  bool _isInitialized = false;
  bool _isListening = false;
  String _lastRecognizedWords = '';
  double _confidence = 0.0;

  // Command handlers
  final Map<String, Function> _commandHandlers = {};

  // Command history
  final List<String> _commandHistory = [];

  // Listeners
  final List<Function(String)> _recognitionListeners = [];
  final List<Function(bool)> _listeningStateListeners = [];
  final List<Function(String)> _commandExecutedListeners = [];

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isListening => _isListening;
  String get lastRecognizedWords => _lastRecognizedWords;
  double get confidence => _confidence;
  List<String> get commandHistory => List.unmodifiable(_commandHistory);

  // Available commands
  static const List<String> availableCommands = [
    'zoom in',
    'zoom out',
    'rotate left',
    'rotate right',
    'show info',
    'hide info',
    'take photo',
    'record video',
    'stop recording',
    'navigate to',
    'stop navigation',
    'show map',
    'hide map',
    'show settings',
    'go back',
    'help',
  ];

  // Singleton instance
  static final ARVoiceCommandService _instance =
      ARVoiceCommandService._internal();

  // Factory constructor
  factory ARVoiceCommandService() => _instance;

  // Internal constructor
  ARVoiceCommandService._internal();

  /// Initialize the voice command service.
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      _isInitialized = await _speech.initialize(
        onStatus: _onSpeechStatus,
        onError: _onSpeechError,
      );

      return _isInitialized;
    } catch (e) {
      debugPrint('Error initializing speech recognition: $e');
      return false;
    }
  }

  /// Register a command handler.
  void registerCommand(String command, Function handler) {
    _commandHandlers[command.toLowerCase()] = handler;
  }

  /// Register multiple command handlers.
  void registerCommands(Map<String, Function> handlers) {
    handlers.forEach((command, handler) {
      registerCommand(command, handler);
    });
  }

  /// Unregister a command handler.
  void unregisterCommand(String command) {
    _commandHandlers.remove(command.toLowerCase());
  }

  /// Start listening for voice commands.
  Future<bool> startListening() async {
    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) return false;
    }

    if (_isListening) return true;

    try {
      _isListening = await _speech.listen(
        onResult: _onSpeechResult,
        listenFor: const Duration(seconds: 30),
        pauseFor: const Duration(seconds: 5),
        partialResults: true,
        localeId: 'en_US',
        cancelOnError: true,
        listenMode: stt.ListenMode.confirmation,
      );

      _notifyListeningStateChanged(_isListening);

      // Provide haptic feedback when listening starts
      HapticFeedback.mediumImpact();

      return _isListening;
    } catch (e) {
      debugPrint('Error starting speech recognition: $e');
      return false;
    }
  }

  /// Stop listening for voice commands.
  Future<bool> stopListening() async {
    if (!_isListening) return true;

    try {
      _speech.stop();
      _isListening = false;
      _notifyListeningStateChanged(_isListening);

      // Provide haptic feedback when listening stops
      HapticFeedback.lightImpact();

      return true;
    } catch (e) {
      debugPrint('Error stopping speech recognition: $e');
      return false;
    }
  }

  /// Cancel listening for voice commands.
  Future<bool> cancelListening() async {
    if (!_isListening) return true;

    try {
      _speech.cancel();
      _isListening = false;
      _notifyListeningStateChanged(_isListening);
      return true;
    } catch (e) {
      debugPrint('Error canceling speech recognition: $e');
      return false;
    }
  }

  /// Handle speech recognition results.
  void _onSpeechResult(SpeechRecognitionResult result) {
    _lastRecognizedWords = result.recognizedWords;
    _confidence = result.confidence;

    _notifyRecognitionResult(_lastRecognizedWords);

    if (result.finalResult) {
      _processCommand(_lastRecognizedWords);
    }
  }

  /// Handle speech recognition status changes.
  void _onSpeechStatus(String status) {
    debugPrint('Speech recognition status: $status');

    if (status == 'done' || status == 'notListening') {
      _isListening = false;
      _notifyListeningStateChanged(_isListening);
    }
  }

  /// Handle speech recognition errors.
  void _onSpeechError(dynamic error) {
    debugPrint('Speech recognition error: $error');
    _isListening = false;
    _notifyListeningStateChanged(_isListening);
  }

  /// Process a recognized command.
  void _processCommand(String text) {
    final lowerText = text.toLowerCase();

    // Check for exact command matches
    for (final command in _commandHandlers.keys) {
      if (lowerText == command || lowerText.contains(command)) {
        _executeCommand(command);
        return;
      }
    }

    // Check for partial command matches
    for (final command in _commandHandlers.keys) {
      final commandWords = command.split(' ');
      bool allWordsMatch = true;

      for (final word in commandWords) {
        if (!lowerText.contains(word)) {
          allWordsMatch = false;
          break;
        }
      }

      if (allWordsMatch) {
        _executeCommand(command);
        return;
      }
    }

    // Check for commands with parameters
    if (lowerText.contains('navigate to')) {
      final target = lowerText.replaceAll('navigate to', '').trim();
      if (target.isNotEmpty) {
        _executeCommand('navigate to', [target]);
        return;
      }
    }

    debugPrint('No command handler found for: $lowerText');
  }

  /// Execute a command.
  void _executeCommand(String command, [List<dynamic> args = const []]) {
    final handler = _commandHandlers[command];

    if (handler != null) {
      try {
        if (args.isEmpty) {
          handler();
        } else {
          Function.apply(handler, args);
        }

        _commandHistory.add(command);
        _notifyCommandExecuted(command);

        // Provide haptic feedback when a command is executed
        HapticFeedback.heavyImpact();
      } catch (e) {
        debugPrint('Error executing command $command: $e');
      }
    }
  }

  /// Add a recognition listener.
  void addRecognitionListener(Function(String) listener) {
    _recognitionListeners.add(listener);
  }

  /// Remove a recognition listener.
  void removeRecognitionListener(Function(String) listener) {
    _recognitionListeners.remove(listener);
  }

  /// Add a listening state listener.
  void addListeningStateListener(Function(bool) listener) {
    _listeningStateListeners.add(listener);
  }

  /// Remove a listening state listener.
  void removeListeningStateListener(Function(bool) listener) {
    _listeningStateListeners.remove(listener);
  }

  /// Add a command executed listener.
  void addCommandExecutedListener(Function(String) listener) {
    _commandExecutedListeners.add(listener);
  }

  /// Remove a command executed listener.
  void removeCommandExecutedListener(Function(String) listener) {
    _commandExecutedListeners.remove(listener);
  }

  /// Notify all recognition listeners.
  void _notifyRecognitionResult(String text) {
    for (final listener in _recognitionListeners) {
      listener(text);
    }
  }

  /// Notify all listening state listeners.
  void _notifyListeningStateChanged(bool isListening) {
    for (final listener in _listeningStateListeners) {
      listener(isListening);
    }
  }

  /// Notify all command executed listeners.
  void _notifyCommandExecuted(String command) {
    for (final listener in _commandExecutedListeners) {
      listener(command);
    }
  }

  /// Dispose the voice command service.
  void dispose() {
    cancelListening();
    _recognitionListeners.clear();
    _listeningStateListeners.clear();
    _commandExecutedListeners.clear();
  }
}

/// A provider for the AR voice command service.
final arVoiceCommandServiceProvider = Provider<ARVoiceCommandService>((ref) {
  return ARVoiceCommandService();
});
