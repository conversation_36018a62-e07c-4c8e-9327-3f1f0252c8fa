// Dart imports

// Flutter imports
import 'package:flutter/foundation.dart';

// Package imports
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:local_auth/local_auth.dart';

// Project imports
import 'package:culture_connect/models/user_model.dart';

enum AuthStatus {
  initial,
  authenticated,
  unauthenticated,
  verificationPending,
}

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  final LocalAuthentication _localAuth = LocalAuthentication();

  // Stream to listen to authentication state changes
  Stream<AuthStatus> get authStateChanges {
    return _auth.authStateChanges().map((user) {
      if (user == null) {
        return AuthStatus.unauthenticated;
      } else if (!user.emailVerified) {
        return AuthStatus.verificationPending;
      } else {
        return AuthStatus.authenticated;
      }
    }).handleError((error) {
      debugPrint('Auth state change error: $error');
      // Return unauthenticated as fallback
      return AuthStatus.unauthenticated;
    });
  }

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Get current authentication status synchronously
  AuthStatus get currentAuthStatus {
    final user = _auth.currentUser;
    if (user == null) {
      return AuthStatus.unauthenticated;
    } else if (!user.emailVerified) {
      return AuthStatus.verificationPending;
    } else {
      return AuthStatus.authenticated;
    }
  }

  // Get current user as UserModel
  Future<UserModel?> get currentUserModel async {
    final user = _auth.currentUser;
    if (user == null) return null;

    try {
      final doc = await _firestore.collection('users').doc(user.uid).get();
      if (doc.exists) {
        return UserModel.fromJson({
          'id': user.uid,
          'email': user.email,
          'emailVerified': user.emailVerified,
          ...doc.data()!,
        });
      }
    } catch (e) {
      debugPrint('Error getting user model: $e');
    }
    return null;
  }

  // Register with email and password
  Future<UserCredential> registerWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String phoneNumber,
    required String dateOfBirth,
  }) async {
    try {
      // Create user with email and password
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Store additional user data in Firestore
      if (userCredential.user != null) {
        await _storeUserData(
          userCredential.user!.uid,
          firstName: firstName,
          lastName: lastName,
          email: email,
          phoneNumber: phoneNumber,
          dateOfBirth: dateOfBirth,
          userType: 'tourist',
        );

        // Send email verification
        await userCredential.user!.sendEmailVerification();
      }

      return userCredential;
    } on FirebaseAuthException catch (e) {
      debugPrint('Firebase Auth Exception: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('Unexpected error during registration: $e');
      rethrow;
    }
  }

  // Login with email and password
  Future<UserCredential> loginWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Update last login timestamp
      if (userCredential.user != null) {
        await _firestore
            .collection('users')
            .doc(userCredential.user!.uid)
            .update({
          'lastLogin': DateTime.now().toIso8601String(),
        });
      }

      return userCredential;
    } on FirebaseAuthException catch (e) {
      debugPrint('Firebase Auth Exception: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('Unexpected error during login: $e');
      rethrow;
    }
  }

  // Sign in with Google
  Future<UserCredential?> signInWithGoogle() async {
    try {
      // Create a GoogleAuthProvider
      final googleProvider = GoogleAuthProvider();

      // Add scopes if needed
      googleProvider.addScope('email');
      googleProvider.addScope('profile');

      // Sign in with Google using Firebase Auth directly
      final userCredential = await _auth.signInWithPopup(googleProvider);

      // Check if this is a new user
      if (userCredential.additionalUserInfo?.isNewUser ?? false) {
        // Store basic user data in Firestore
        final displayName = userCredential.user?.displayName ?? '';
        final nameParts = displayName.split(' ');
        final firstName = nameParts.isNotEmpty ? nameParts.first : '';
        final lastName = nameParts.length > 1 ? nameParts.last : '';

        await _storeUserData(
          userCredential.user!.uid,
          firstName: firstName,
          lastName: lastName,
          email: userCredential.user?.email ?? '',
          phoneNumber: userCredential.user?.phoneNumber ?? '',
          userType: 'tourist',
        );
      } else {
        // Update last login timestamp
        await _firestore
            .collection('users')
            .doc(userCredential.user!.uid)
            .update({
          'lastLogin': DateTime.now().toIso8601String(),
        });
      }

      return userCredential;
    } on FirebaseAuthException catch (e) {
      debugPrint('Firebase Auth Exception: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('Unexpected error during Google sign in: $e');
      rethrow;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      debugPrint('Error signing out: $e');
      rethrow;
    }
  }

  // Send password reset email
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      debugPrint('Firebase Auth Exception: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('Unexpected error sending password reset: $e');
      rethrow;
    }
  }

  // Verify email
  Future<bool> verifyEmail() async {
    try {
      User? user = _auth.currentUser;
      if (user != null) {
        await user.reload();
        user = _auth.currentUser;
        return user?.emailVerified ?? false;
      }
      return false;
    } catch (e) {
      debugPrint('Error verifying email: $e');
      return false;
    }
  }

  // Resend verification email
  Future<void> resendVerificationEmail() async {
    try {
      User? user = _auth.currentUser;
      if (user != null) {
        await user.sendEmailVerification();
      }
    } catch (e) {
      debugPrint('Error resending verification email: $e');
      rethrow;
    }
  }

  // Check if biometric authentication is available
  Future<bool> isBiometricAvailable() async {
    try {
      return await _localAuth.canCheckBiometrics;
    } catch (e) {
      debugPrint('Error checking biometric availability: $e');
      return false;
    }
  }

  // Authenticate with biometrics
  Future<bool> authenticateWithBiometrics() async {
    try {
      return await _localAuth.authenticate(
        localizedReason: 'Authenticate to access your account',
        options: const AuthenticationOptions(
          stickyAuth: true,
          biometricOnly: true,
        ),
      );
    } catch (e) {
      debugPrint('Error authenticating with biometrics: $e');
      return false;
    }
  }

  // Store user data in Firestore
  Future<void> _storeUserData(
    String userId, {
    required String firstName,
    required String lastName,
    required String email,
    required String phoneNumber,
    String? dateOfBirth,
    required String userType,
  }) async {
    try {
      final userData = {
        'firstName': firstName,
        'lastName': lastName,
        'email': email,
        'phoneNumber': phoneNumber,
        'dateOfBirth': dateOfBirth,
        'userType': userType,
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
        'lastLogin': DateTime.now().toIso8601String(),
        'isVerified': false,
        'verificationLevel': 1,
        'status': 'active',
      };

      await _firestore.collection('users').doc(userId).set(userData);
    } catch (e) {
      debugPrint('Error storing user data: $e');
      rethrow;
    }
  }
}
