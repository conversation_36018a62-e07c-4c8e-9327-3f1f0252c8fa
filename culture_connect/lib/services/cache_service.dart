import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Provider for the cache service
final cacheServiceProvider = Provider<CacheService>((ref) {
  return CacheService();
});

/// Service for caching data locally
class CacheService {
  // Singleton instance
  static final CacheService _instance = CacheService._internal();
  factory CacheService() => _instance;
  CacheService._internal();

  // Cache keys
  static const String _bookingsKey = 'cached_bookings';
  static const String _experiencesKey = 'cached_experiences';
  static const String _reviewsKey = 'cached_reviews';
  static const String _lastUpdatedPrefix = 'last_updated_';

  // Cache expiration time (24 hours)
  static const Duration _cacheExpiration = Duration(hours: 24);

  /// Save data to cache
  Future<bool> saveData(String key, dynamic data) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Convert data to JSON string
      final jsonString = jsonEncode(data);

      // Save data
      await prefs.setString(key, jsonString);

      // Save last updated timestamp
      await prefs.setInt(
          '$_lastUpdatedPrefix$key', DateTime.now().millisecondsSinceEpoch);

      return true;
    } catch (e) {
      debugPrint('Error saving data to cache: $e');
      return false;
    }
  }

  /// Get data from cache
  Future<dynamic> getData(String key, {bool checkExpiration = true}) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check if data exists
      if (!prefs.containsKey(key)) {
        return null;
      }

      // Check if cache is expired
      if (checkExpiration) {
        final lastUpdated = prefs.getInt('$_lastUpdatedPrefix$key');
        if (lastUpdated != null) {
          final lastUpdatedTime =
              DateTime.fromMillisecondsSinceEpoch(lastUpdated);
          final now = DateTime.now();

          if (now.difference(lastUpdatedTime) > _cacheExpiration) {
            // Cache is expired, clear it
            await prefs.remove(key);
            await prefs.remove('$_lastUpdatedPrefix$key');
            return null;
          }
        }
      }

      // Get data
      final jsonString = prefs.getString(key);
      if (jsonString == null) {
        return null;
      }

      // Parse JSON
      return jsonDecode(jsonString);
    } catch (e) {
      debugPrint('Error getting data from cache: $e');
      return null;
    }
  }

  /// Clear cache for a specific key
  Future<bool> clearCache(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Remove data and timestamp
      await prefs.remove(key);
      await prefs.remove('$_lastUpdatedPrefix$key');

      return true;
    } catch (e) {
      debugPrint('Error clearing cache: $e');
      return false;
    }
  }

  /// Clear all cache
  Future<bool> clearAllCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Clear all cache keys
      await prefs.remove(_bookingsKey);
      await prefs.remove(_experiencesKey);
      await prefs.remove(_reviewsKey);

      // Clear all timestamps
      await prefs.remove('$_lastUpdatedPrefix$_bookingsKey');
      await prefs.remove('$_lastUpdatedPrefix$_experiencesKey');
      await prefs.remove('$_lastUpdatedPrefix$_reviewsKey');

      return true;
    } catch (e) {
      debugPrint('Error clearing all cache: $e');
      return false;
    }
  }

  /// Check if cache is available and not expired
  Future<bool> isCacheAvailable(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check if data exists
      if (!prefs.containsKey(key)) {
        return false;
      }

      // Check if cache is expired
      final lastUpdated = prefs.getInt('$_lastUpdatedPrefix$key');
      if (lastUpdated == null) {
        return false;
      }

      final lastUpdatedTime = DateTime.fromMillisecondsSinceEpoch(lastUpdated);
      final now = DateTime.now();

      return now.difference(lastUpdatedTime) <= _cacheExpiration;
    } catch (e) {
      debugPrint('Error checking cache availability: $e');
      return false;
    }
  }

  /// Get bookings cache key
  String get bookingsKey => _bookingsKey;

  /// Get experiences cache key
  String get experiencesKey => _experiencesKey;

  /// Get reviews cache key
  String get reviewsKey => _reviewsKey;
}
