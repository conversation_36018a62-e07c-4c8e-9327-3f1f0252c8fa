import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:culture_connect/models/common/country.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Service for managing country data
class CountryService {
  static final CountryService _instance = CountryService._internal();
  factory CountryService() => _instance;
  CountryService._internal();

  final LoggingService _loggingService = LoggingService();
  List<Country>? _countries;
  bool _isInitialized = false;

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadCountries();
      _isInitialized = true;
    } catch (e, stackTrace) {
      _loggingService.error(
        'CountryService',
        'Error initializing country service',
        e,
        stackTrace,
      );
      // Use fallback data if loading fails
      _countries = Countries.all;
      _isInitialized = true;
    }
  }

  /// Load countries from assets or generate mock data
  Future<void> _loadCountries() async {
    try {
      // Try to load from assets first
      final String data =
          await rootBundle.loadString('assets/data/countries.json');
      final List<dynamic> jsonList = json.decode(data);
      _countries = jsonList.map((json) => Country.fromJson(json)).toList();
    } catch (e) {
      // If assets don't exist, use mock data
      _countries = _generateMockCountries();
    }
  }

  /// Generate mock country data
  List<Country> _generateMockCountries() {
    return [
      const Country(
        code: 'US',
        name: 'United States',
        dialCode: '1',
        flag: '🇺🇸',
        currencyCode: 'USD',
        currencySymbol: '\$',
        continent: 'North America',
        capital: 'Washington, D.C.',
        languages: ['en'],
      ),
      const Country(
        code: 'GB',
        name: 'United Kingdom',
        dialCode: '44',
        flag: '🇬🇧',
        currencyCode: 'GBP',
        currencySymbol: '£',
        continent: 'Europe',
        capital: 'London',
        languages: ['en'],
      ),
      const Country(
        code: 'CA',
        name: 'Canada',
        dialCode: '1',
        flag: '🇨🇦',
        currencyCode: 'CAD',
        currencySymbol: 'C\$',
        continent: 'North America',
        capital: 'Ottawa',
        languages: ['en', 'fr'],
      ),
      const Country(
        code: 'AU',
        name: 'Australia',
        dialCode: '61',
        flag: '🇦🇺',
        currencyCode: 'AUD',
        currencySymbol: 'A\$',
        continent: 'Oceania',
        capital: 'Canberra',
        languages: ['en'],
      ),
      const Country(
        code: 'DE',
        name: 'Germany',
        dialCode: '49',
        flag: '🇩🇪',
        currencyCode: 'EUR',
        currencySymbol: '€',
        continent: 'Europe',
        capital: 'Berlin',
        languages: ['de'],
      ),
      const Country(
        code: 'FR',
        name: 'France',
        dialCode: '33',
        flag: '🇫🇷',
        currencyCode: 'EUR',
        currencySymbol: '€',
        continent: 'Europe',
        capital: 'Paris',
        languages: ['fr'],
      ),
      const Country(
        code: 'ES',
        name: 'Spain',
        dialCode: '34',
        flag: '🇪🇸',
        currencyCode: 'EUR',
        currencySymbol: '€',
        continent: 'Europe',
        capital: 'Madrid',
        languages: ['es'],
      ),
      const Country(
        code: 'IT',
        name: 'Italy',
        dialCode: '39',
        flag: '🇮🇹',
        currencyCode: 'EUR',
        currencySymbol: '€',
        continent: 'Europe',
        capital: 'Rome',
        languages: ['it'],
      ),
      const Country(
        code: 'JP',
        name: 'Japan',
        dialCode: '81',
        flag: '🇯🇵',
        currencyCode: 'JPY',
        currencySymbol: '¥',
        continent: 'Asia',
        capital: 'Tokyo',
        languages: ['ja'],
        requiresVisa: true,
      ),
      const Country(
        code: 'CN',
        name: 'China',
        dialCode: '86',
        flag: '🇨🇳',
        currencyCode: 'CNY',
        currencySymbol: '¥',
        continent: 'Asia',
        capital: 'Beijing',
        languages: ['zh'],
        requiresVisa: true,
      ),
      const Country(
        code: 'IN',
        name: 'India',
        dialCode: '91',
        flag: '🇮🇳',
        currencyCode: 'INR',
        currencySymbol: '₹',
        continent: 'Asia',
        capital: 'New Delhi',
        languages: ['hi', 'en'],
        requiresVisa: true,
      ),
      const Country(
        code: 'BR',
        name: 'Brazil',
        dialCode: '55',
        flag: '🇧🇷',
        currencyCode: 'BRL',
        currencySymbol: 'R\$',
        continent: 'South America',
        capital: 'Brasília',
        languages: ['pt'],
      ),
      const Country(
        code: 'MX',
        name: 'Mexico',
        dialCode: '52',
        flag: '🇲🇽',
        currencyCode: 'MXN',
        currencySymbol: '\$',
        continent: 'North America',
        capital: 'Mexico City',
        languages: ['es'],
      ),
      const Country(
        code: 'RU',
        name: 'Russia',
        dialCode: '7',
        flag: '🇷🇺',
        currencyCode: 'RUB',
        currencySymbol: '₽',
        continent: 'Europe',
        capital: 'Moscow',
        languages: ['ru'],
        requiresVisa: true,
      ),
      const Country(
        code: 'ZA',
        name: 'South Africa',
        dialCode: '27',
        flag: '🇿🇦',
        currencyCode: 'ZAR',
        currencySymbol: 'R',
        continent: 'Africa',
        capital: 'Cape Town',
        languages: ['en', 'af'],
      ),
    ];
  }

  /// Get all countries
  Future<List<Country>> getCountries() async {
    await initialize();
    return _countries ?? [];
  }

  /// Get a country by its code
  Future<Country?> getCountryByCode(String code) async {
    await initialize();
    try {
      return _countries?.firstWhere(
        (country) => country.code.toLowerCase() == code.toLowerCase(),
      );
    } catch (e) {
      return null;
    }
  }

  /// Get a country by its name
  Future<Country?> getCountryByName(String name) async {
    await initialize();
    try {
      return _countries?.firstWhere(
        (country) => country.name.toLowerCase() == name.toLowerCase(),
      );
    } catch (e) {
      return null;
    }
  }

  /// Search countries by name
  Future<List<Country>> searchCountries(String query) async {
    await initialize();
    if (query.isEmpty) return _countries ?? [];

    final lowerQuery = query.toLowerCase();
    return _countries
            ?.where((country) =>
                country.name.toLowerCase().contains(lowerQuery) ||
                country.code.toLowerCase().contains(lowerQuery))
            .toList() ??
        [];
  }

  /// Get countries by continent
  Future<List<Country>> getCountriesByContinent(String continent) async {
    await initialize();
    return _countries
            ?.where((country) =>
                country.continent?.toLowerCase() == continent.toLowerCase())
            .toList() ??
        [];
  }

  /// Get countries that require visa
  Future<List<Country>> getCountriesRequiringVisa() async {
    await initialize();
    return _countries?.where((country) => country.requiresVisa).toList() ?? [];
  }

  /// Get countries by currency
  Future<List<Country>> getCountriesByCurrency(String currencyCode) async {
    await initialize();
    return _countries
            ?.where((country) =>
                country.currencyCode?.toLowerCase() ==
                currencyCode.toLowerCase())
            .toList() ??
        [];
  }

  /// Get countries by language
  Future<List<Country>> getCountriesByLanguage(String languageCode) async {
    await initialize();
    return _countries
            ?.where((country) => country.hasLanguage(languageCode))
            .toList() ??
        [];
  }

  /// Get popular countries (commonly used)
  Future<List<Country>> getPopularCountries() async {
    await initialize();
    final popularCodes = ['US', 'GB', 'CA', 'AU', 'DE', 'FR', 'JP', 'CN'];
    final popular = <Country>[];

    for (final code in popularCodes) {
      final country = await getCountryByCode(code);
      if (country != null) {
        popular.add(country);
      }
    }

    return popular;
  }
}
