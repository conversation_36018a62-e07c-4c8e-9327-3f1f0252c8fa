// Dart imports
import 'dart:async';
import 'dart:convert';

// Flutter imports
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Project imports
import 'package:culture_connect/models/flight/flight_booking_management.dart';
import 'package:culture_connect/models/travel/flight/booking_info.dart';
import 'package:culture_connect/models/travel/flight/flight_info.dart';
import 'package:culture_connect/models/travel/flight/passenger_info.dart';

import 'package:culture_connect/models/mascot/mascot_state.dart';
import 'package:culture_connect/services/achievement_service.dart';
import 'package:culture_connect/services/mascot_service.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/providers/achievement_provider.dart';

/// Provider for the flight management service
final flightManagementServiceProvider =
    Provider<FlightManagementService>((ref) {
  final prefs = ref.read(sharedPreferencesProvider);
  final achievementService = ref.read(achievementServiceProvider);
  final mascotService = ref.read(mascotServiceProvider);

  return FlightManagementService(
    prefs: prefs,
    achievementService: achievementService,
    mascotService: mascotService,
  );
});

/// Service for managing flight bookings and tracking
class FlightManagementService {
  final SharedPreferences _prefs;
  final AchievementService _achievementService;
  final MascotService _mascotService;

  static const String _bookingsKey = 'flight_bookings';
  static const String _modificationsKey = 'booking_modifications';
  static const String _boardingPassesKey = 'boarding_passes';
  static const String _flightStatusKey = 'flight_status_cache';

  /// Creates a new flight management service
  FlightManagementService({
    required SharedPreferences prefs,
    required AchievementService achievementService,
    required MascotService mascotService,
  })  : _prefs = prefs,
        _achievementService = achievementService,
        _mascotService = mascotService;

  /// Get all user flight bookings
  Future<List<BookingInfo>> getUserBookings() async {
    try {
      final bookingsJson = _prefs.getString(_bookingsKey);
      if (bookingsJson == null) return [];

      final bookingsList = jsonDecode(bookingsJson) as List;
      return bookingsList
          .map((json) => BookingInfo.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      debugPrint('Error loading user bookings: $e');
      return [];
    }
  }

  /// Get a specific booking by reference
  Future<BookingInfo?> getBooking(String bookingReference) async {
    try {
      final bookings = await getUserBookings();
      return bookings
          .where((booking) => booking.bookingReference == bookingReference)
          .firstOrNull;
    } catch (e) {
      debugPrint('Error getting booking: $e');
      return null;
    }
  }

  /// Save a booking
  Future<void> saveBooking(BookingInfo booking) async {
    try {
      final bookings = await getUserBookings();

      // Remove existing booking with same reference if it exists
      bookings
          .removeWhere((b) => b.bookingReference == booking.bookingReference);

      // Add the new/updated booking
      bookings.add(booking);

      final bookingsJson = jsonEncode(bookings.map((b) => b.toJson()).toList());
      await _prefs.setString(_bookingsKey, bookingsJson);

      // Schedule flight reminders
      await _scheduleFlightReminders(booking);
    } catch (e) {
      debugPrint('Error saving booking: $e');
    }
  }

  /// Request booking modification
  Future<BookingModification?> requestModification({
    required String bookingReference,
    required ModificationType type,
    required String description,
    String? previousValue,
    required String newValue,
    double? fee,
    String? currency,
  }) async {
    try {
      final modification = BookingModification.create(
        type: type,
        description: description,
        previousValue: previousValue,
        newValue: newValue,
        fee: fee,
        currency: currency,
      );

      // Save modification
      await _saveModification(bookingReference, modification);

      // Trigger haptic feedback
      await HapticFeedback.mediumImpact();

      // Update mascot based on modification type
      await _updateMascotForModification(type);

      // Check for achievements
      await _checkModificationAchievements(type);

      return modification;
    } catch (e) {
      debugPrint('Error requesting modification: $e');
      return null;
    }
  }

  /// Get modifications for a booking
  Future<List<BookingModification>> getBookingModifications(
      String bookingReference) async {
    try {
      final modificationsJson =
          _prefs.getString('${_modificationsKey}_$bookingReference');
      if (modificationsJson == null) return [];

      final modificationsList = jsonDecode(modificationsJson) as List;
      return modificationsList
          .map((json) =>
              BookingModification.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      debugPrint('Error loading modifications: $e');
      return [];
    }
  }

  /// Get real-time flight status
  Future<FlightInfo?> getFlightStatus(
      String flightNumber, DateTime date) async {
    try {
      // Check cache first
      final cacheKey = '${flightNumber}_${date.year}-${date.month}-${date.day}';
      final cachedStatus = _prefs.getString('${_flightStatusKey}_$cacheKey');

      if (cachedStatus != null) {
        final cachedData = jsonDecode(cachedStatus) as Map<String, dynamic>;
        final cacheTime = DateTime.parse(cachedData['timestamp'] as String);

        // Use cached data if less than 5 minutes old
        if (DateTime.now().difference(cacheTime).inMinutes < 5) {
          return FlightInfo.fromJson(
              cachedData['data'] as Map<String, dynamic>);
        }
      }

      // Simulate API call to Trawex or flight tracking service
      await Future.delayed(const Duration(milliseconds: 800));

      // Generate mock flight status (in real app, this would be API call)
      final flightInfo = _generateMockFlightStatus(flightNumber, date);

      // Cache the result
      final cacheData = {
        'timestamp': DateTime.now().toIso8601String(),
        'data': flightInfo.toJson(),
      };
      await _prefs.setString(
          '${_flightStatusKey}_$cacheKey', jsonEncode(cacheData));

      return flightInfo;
    } catch (e) {
      debugPrint('Error getting flight status: $e');
      return null;
    }
  }

  /// Generate boarding pass
  Future<BoardingPass?> generateBoardingPass({
    required String bookingReference,
    required PassengerInfo passenger,
    String? gate,
    String? terminal,
    String? boardingGroup,
  }) async {
    try {
      final booking = await getBooking(bookingReference);
      if (booking == null) return null;

      final boardingPass = BoardingPass.fromBookingInfo(
        bookingInfo: booking,
        passenger: passenger,
        gate: gate,
        terminal: terminal,
        boardingGroup: boardingGroup,
      );

      // Save boarding pass
      await _saveBoardingPass(boardingPass);

      // Trigger haptic feedback
      await HapticFeedback.mediumImpact();

      // Update mascot for boarding pass generation
      await _mascotService.updateState(
        MascotState(
          expression: MascotExpression.excited,
          context: MascotContext.idle,
          metadata: {
            'action': 'boarding_pass_generated',
            'flight': booking.flight.flightNumber
          },
        ),
      );

      // Check for achievements
      await _checkBoardingPassAchievements();

      return boardingPass;
    } catch (e) {
      debugPrint('Error generating boarding pass: $e');
      return null;
    }
  }

  /// Get boarding passes for a booking
  Future<List<BoardingPass>> getBoardingPasses(String bookingReference) async {
    try {
      final passesJson =
          _prefs.getString('${_boardingPassesKey}_$bookingReference');
      if (passesJson == null) return [];

      final passesList = jsonDecode(passesJson) as List;
      return passesList
          .map((json) => BoardingPass.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      debugPrint('Error loading boarding passes: $e');
      return [];
    }
  }

  /// Cancel booking
  Future<bool> cancelBooking(String bookingReference, String reason) async {
    try {
      final booking = await getBooking(bookingReference);
      if (booking == null) return false;

      // Create cancellation modification
      await requestModification(
        bookingReference: bookingReference,
        type: ModificationType.cancellation,
        description: 'Booking cancelled: $reason',
        newValue: 'Cancelled',
      );

      // Update mascot for cancellation
      await _mascotService.updateState(
        MascotState(
          expression: MascotExpression.sympathetic,
          context: MascotContext.idle,
          metadata: {'action': 'booking_cancelled', 'reason': reason},
        ),
      );

      return true;
    } catch (e) {
      debugPrint('Error cancelling booking: $e');
      return false;
    }
  }

  /// Schedule flight reminders
  Future<void> _scheduleFlightReminders(BookingInfo booking) async {
    try {
      final departureTime = booking.flight.departureDateTime;

      // 24-hour reminder
      final reminder24h = departureTime.subtract(const Duration(hours: 24));
      if (reminder24h.isAfter(DateTime.now())) {
        // TODO: Integrate with notification service for flight reminders
        debugPrint(
            'Would schedule 24h reminder for ${booking.bookingReference}');
      }

      // 2-hour reminder for check-in
      final reminder2h = departureTime.subtract(const Duration(hours: 2));
      if (reminder2h.isAfter(DateTime.now())) {
        // TODO: Integrate with notification service for check-in reminder
        debugPrint(
            'Would schedule 2h check-in reminder for ${booking.bookingReference}');
      }
    } catch (e) {
      debugPrint('Error scheduling flight reminders: $e');
    }
  }

  /// Save modification
  Future<void> _saveModification(
      String bookingReference, BookingModification modification) async {
    try {
      final modifications = await getBookingModifications(bookingReference);
      modifications.add(modification);

      final modificationsJson =
          jsonEncode(modifications.map((m) => m.toJson()).toList());
      await _prefs.setString(
          '${_modificationsKey}_$bookingReference', modificationsJson);
    } catch (e) {
      debugPrint('Error saving modification: $e');
    }
  }

  /// Save boarding pass
  Future<void> _saveBoardingPass(BoardingPass boardingPass) async {
    try {
      final passes = await getBoardingPasses(boardingPass.bookingReference);

      // Remove existing pass for same passenger if it exists
      passes.removeWhere((p) =>
          p.passenger.passportNumber == boardingPass.passenger.passportNumber);

      // Add the new pass
      passes.add(boardingPass);

      final passesJson = jsonEncode(passes.map((p) => p.toJson()).toList());
      await _prefs.setString(
          '${_boardingPassesKey}_${boardingPass.bookingReference}', passesJson);
    } catch (e) {
      debugPrint('Error saving boarding pass: $e');
    }
  }

  /// Update mascot for modification
  Future<void> _updateMascotForModification(ModificationType type) async {
    try {
      MascotExpression expression;
      switch (type) {
        case ModificationType.seatChange:
        case ModificationType.mealChange:
        case ModificationType.specialAssistance:
        case ModificationType.passengerInfo:
          expression = MascotExpression.helpful;
          break;
        case ModificationType.flightChange:
          expression = MascotExpression.excited;
          break;
        case ModificationType.cancellation:
          expression = MascotExpression.sympathetic;
          break;
      }

      await _mascotService.updateState(
        MascotState(
          expression: expression,
          context: MascotContext.idle,
          metadata: {
            'action': 'booking_modification',
            'type': type.displayName
          },
        ),
      );
    } catch (e) {
      debugPrint('Error updating mascot for modification: $e');
    }
  }

  /// Check modification achievements
  Future<void> _checkModificationAchievements(ModificationType type) async {
    try {
      // Track flight manager achievement
      await _achievementService.trackUserAction(
        UserAction.appFeatureUsed,
        metadata: {
          'feature': 'flight_management',
          'action': 'booking_modification',
          'type': type.displayName,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('Error checking modification achievements: $e');
    }
  }

  /// Check boarding pass achievements
  Future<void> _checkBoardingPassAchievements() async {
    try {
      await _achievementService.trackUserAction(
        UserAction.appFeatureUsed,
        metadata: {
          'feature': 'flight_management',
          'action': 'boarding_pass_generated',
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('Error checking boarding pass achievements: $e');
    }
  }

  /// Generate mock flight status (replace with real API call)
  FlightInfo _generateMockFlightStatus(String flightNumber, DateTime date) {
    final statuses = [
      FlightStatus.scheduled,
      FlightStatus.boarding,
      FlightStatus.delayed,
      FlightStatus.inAir
    ];
    final randomStatus = statuses[DateTime.now().millisecond % statuses.length];

    return FlightInfo(
      flightNumber: flightNumber,
      airlineCode: flightNumber.substring(0, 2),
      airlineName: 'Mock Airlines',
      departureAirportCode: 'LAG',
      departureAirportName: 'Lagos Airport',
      departureCity: 'Lagos',
      departureTerminal: 'Terminal 1',
      departureGate: randomStatus == FlightStatus.boarding ? 'A12' : null,
      scheduledDepartureTime: date,
      actualDepartureTime: randomStatus == FlightStatus.delayed
          ? date.add(const Duration(minutes: 30))
          : null,
      arrivalAirportCode: 'LHR',
      arrivalAirportName: 'Heathrow Airport',
      arrivalCity: 'London',
      arrivalTerminal: 'Terminal 2',
      arrivalGate: null,
      scheduledArrival: date.add(const Duration(hours: 6)),
      actualArrival: null,
      status: randomStatus,
      delayMinutes: randomStatus == FlightStatus.delayed ? 30 : null,
    );
  }
}
