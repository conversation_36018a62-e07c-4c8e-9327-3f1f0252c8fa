import 'dart:async';
import 'dart:convert';

// Flutter imports
import 'package:flutter/foundation.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:uuid/uuid.dart';

// Project imports
import 'package:culture_connect/models/booking_model.dart';
import 'package:culture_connect/models/instant_booking_model.dart';
import 'package:culture_connect/models/payment/payment_method_model.dart';
import 'package:culture_connect/models/payment/transaction_model.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/services/booking_service.dart'
    as booking_service;
import 'package:culture_connect/services/enhanced_payment_service.dart';
import 'package:culture_connect/providers/enhanced_payment_provider.dart';
import 'package:culture_connect/services/travel_availability_service.dart';

/// Service for handling instant bookings
class InstantBookingService {
  final Box<String> _instantBookingsBox;
  final EnhancedPaymentService _paymentService;
  final booking_service.BookingService _bookingService;
  final TravelAvailabilityService _availabilityService;
  final Uuid _uuid = const Uuid();

  // Stream controllers
  final _instantBookingsController =
      StreamController<List<InstantBookingModel>>.broadcast();
  final _instantBookingStatusController =
      StreamController<InstantBookingModel>.broadcast();

  /// Creates a new instant booking service
  InstantBookingService({
    required EnhancedPaymentService paymentService,
    required booking_service.BookingService bookingService,
    required TravelAvailabilityService availabilityService,
  })  : _instantBookingsBox = Hive.box<String>('instant_bookings'),
        _paymentService = paymentService,
        _bookingService = bookingService,
        _availabilityService = availabilityService {
    _loadInitialData();
  }

  /// Loads initial data
  Future<void> _loadInitialData() async {
    // Notify listeners of initial data
    _notifyInstantBookingsListeners();
  }

  /// Notifies instant bookings listeners
  void _notifyInstantBookingsListeners() {
    _instantBookingsController.add(getInstantBookings());
  }

  /// Notifies instant booking status listeners
  void _notifyInstantBookingStatusListeners(InstantBookingModel booking) {
    _instantBookingStatusController.add(booking);
  }

  /// Gets all instant bookings
  List<InstantBookingModel> getInstantBookings() {
    final bookings = <InstantBookingModel>[];

    for (final key in _instantBookingsBox.keys) {
      final json = _instantBookingsBox.get(key);
      if (json == null) continue;

      try {
        final data = jsonDecode(json) as Map<String, dynamic>;
        bookings.add(InstantBookingModel.fromJson(data));
      } catch (e) {
        debugPrint('Error parsing instant booking: $e');
      }
    }

    // Sort by timestamp (newest first)
    bookings.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return bookings;
  }

  /// Gets an instant booking by ID
  InstantBookingModel? getInstantBooking(String id) {
    final json = _instantBookingsBox.get(id);
    if (json == null) return null;

    try {
      final data = jsonDecode(json) as Map<String, dynamic>;
      return InstantBookingModel.fromJson(data);
    } catch (e) {
      debugPrint('Error parsing instant booking: $e');
      return null;
    }
  }

  /// Creates a new instant booking for an experience
  Future<InstantBookingModel> createExperienceInstantBooking({
    required String experienceId,
    required String experienceName,
    required String experienceImageUrl,
    required DateTime serviceDate,
    required int participantCount,
    required double totalAmount,
    required String currency,
    required PaymentMethodModel paymentMethod,
    Map<String, dynamic> additionalDetails = const {},
  }) async {
    // Check availability
    final availability = await _availabilityService.checkAvailability(
      'experience',
      experienceId,
    );

    if (!availability.isAvailable) {
      throw Exception('Experience is not available for the selected date');
    }

    // Generate a booking ID
    final bookingId = _uuid.v4();

    // Create a pending instant booking
    final pendingBooking = InstantBookingModel(
      id: bookingId,
      type: InstantBookingType.experience,
      status: InstantBookingStatus.processing,
      serviceId: experienceId,
      serviceName: experienceName,
      serviceImageUrl: experienceImageUrl,
      bookingDate: DateTime.now(),
      serviceDate: serviceDate,
      participantCount: participantCount,
      totalAmount: totalAmount,
      currency: currency,
      paymentMethod: paymentMethod,
      timestamp: DateTime.now(),
      additionalDetails: additionalDetails,
    );

    // Save the pending booking
    await _saveInstantBooking(pendingBooking);

    // Notify listeners
    _notifyInstantBookingsListeners();
    _notifyInstantBookingStatusListeners(pendingBooking);

    try {
      // Simulate a successful payment transaction
      await Future.delayed(const Duration(milliseconds: 800));

      // Create a mock transaction
      final transaction = TransactionModel(
        id: _uuid.v4(),
        amount: totalAmount,
        currency: currency,
        status: TransactionStatus.completed,
        description: 'Instant Booking: $experienceName',
        paymentMethod: paymentMethod,
        timestamp: DateTime.now(),
      );

      // Create a booking
      final booking = BookingModel(
        id: bookingId,
        guideId: 'guide_id',
        customer: BookingCustomer(
          id: 'user_id',
          name: additionalDetails['userName'] as String? ?? 'User',
          email:
              additionalDetails['userEmail'] as String? ?? '<EMAIL>',
        ),
        experience: BookingExperience(
          id: experienceId,
          title: experienceName,
          description: 'Experience booking',
          category: 'Experience',
          price: totalAmount / participantCount,
          currency: currency,
          durationMinutes: 120,
        ),
        bookingDate: DateTime.now(),
        experienceDate: serviceDate,
        startTime:
            '${serviceDate.hour}:${serviceDate.minute.toString().padLeft(2, '0')}',
        participantCount: participantCount,
        status: BookingStatus.approved,
        payment: PaymentInfo(
          id: transaction.id,
          status: PaymentStatus.paid,
          amount: totalAmount,
          currency: currency,
          transactionId: transaction.id,
          paymentMethod: 'Credit Card',
          paymentDate: DateTime.now(),
        ),
        isReviewed: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Update the instant booking with success status
      final successBooking = pendingBooking.copyWith(
        status: InstantBookingStatus.success,
        transaction: transaction,
        booking: booking,
      );

      // Save the success booking
      await _saveInstantBooking(successBooking);

      // Notify listeners
      _notifyInstantBookingsListeners();
      _notifyInstantBookingStatusListeners(successBooking);

      return successBooking;
    } catch (e) {
      // Handle any errors
      final failedBooking = pendingBooking.copyWith(
        status: InstantBookingStatus.failed,
        errorMessage: 'Error processing booking: $e',
      );

      // Save the failed booking
      await _saveInstantBooking(failedBooking);

      // Notify listeners
      _notifyInstantBookingsListeners();
      _notifyInstantBookingStatusListeners(failedBooking);

      rethrow;
    }
  }

  /// Creates a new instant booking for a travel service
  Future<InstantBookingModel> createTravelServiceInstantBooking({
    required TravelService travelService,
    required DateTime serviceDate,
    required int participantCount,
    required double totalAmount,
    required String currency,
    required PaymentMethodModel paymentMethod,
    Map<String, dynamic> additionalDetails = const {},
  }) async {
    // Determine the booking type
    final bookingType = _getTravelServiceBookingType(travelService);

    // Check availability
    final serviceType = _getTravelServiceType(travelService);
    final availability = await _availabilityService.checkAvailability(
      serviceType,
      travelService.id,
    );

    if (!availability.isAvailable) {
      throw Exception(
          '${travelService.name} is not available for the selected date');
    }

    // Generate a booking ID
    final bookingId = _uuid.v4();

    // Create a pending instant booking
    final pendingBooking = InstantBookingModel(
      id: bookingId,
      type: bookingType,
      status: InstantBookingStatus.processing,
      serviceId: travelService.id,
      serviceName: travelService.name,
      serviceImageUrl: travelService.imageUrl,
      bookingDate: DateTime.now(),
      serviceDate: serviceDate,
      participantCount: participantCount,
      totalAmount: totalAmount,
      currency: currency,
      paymentMethod: paymentMethod,
      timestamp: DateTime.now(),
      additionalDetails: additionalDetails,
    );

    // Save the pending booking
    await _saveInstantBooking(pendingBooking);

    // Notify listeners
    _notifyInstantBookingsListeners();
    _notifyInstantBookingStatusListeners(pendingBooking);

    try {
      // Simulate a successful payment transaction
      await Future.delayed(const Duration(milliseconds: 800));

      // Create a mock transaction
      final transaction = TransactionModel(
        id: _uuid.v4(),
        amount: totalAmount,
        currency: currency,
        status: TransactionStatus.completed,
        description: 'Instant Booking: ${travelService.name}',
        paymentMethod: paymentMethod,
        timestamp: DateTime.now(),
      );

      // Create a booking
      final booking = BookingModel(
        id: bookingId,
        guideId: 'guide_id',
        customer: BookingCustomer(
          id: 'user_id',
          name: additionalDetails['userName'] as String? ?? 'User',
          email:
              additionalDetails['userEmail'] as String? ?? '<EMAIL>',
        ),
        experience: BookingExperience(
          id: travelService.id,
          title: travelService.name,
          description: 'Travel service booking',
          category: serviceType,
          price: totalAmount / participantCount,
          currency: currency,
          durationMinutes: 120,
        ),
        bookingDate: DateTime.now(),
        experienceDate: serviceDate,
        startTime:
            '${serviceDate.hour}:${serviceDate.minute.toString().padLeft(2, '0')}',
        participantCount: participantCount,
        status: BookingStatus.approved,
        payment: PaymentInfo(
          id: transaction.id,
          status: PaymentStatus.paid,
          amount: totalAmount,
          currency: currency,
          transactionId: transaction.id,
          paymentMethod: 'Credit Card',
          paymentDate: DateTime.now(),
        ),
        isReviewed: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Update the instant booking with success status
      final successBooking = pendingBooking.copyWith(
        status: InstantBookingStatus.success,
        transaction: transaction,
        booking: booking,
      );

      // Save the success booking
      await _saveInstantBooking(successBooking);

      // Notify listeners
      _notifyInstantBookingsListeners();
      _notifyInstantBookingStatusListeners(successBooking);

      return successBooking;
    } catch (e) {
      // Handle any errors
      final failedBooking = pendingBooking.copyWith(
        status: InstantBookingStatus.failed,
        errorMessage: 'Error processing booking: $e',
      );

      // Save the failed booking
      await _saveInstantBooking(failedBooking);

      // Notify listeners
      _notifyInstantBookingsListeners();
      _notifyInstantBookingStatusListeners(failedBooking);

      rethrow;
    }
  }

  /// Cancels an instant booking
  Future<InstantBookingModel> cancelInstantBooking(String bookingId) async {
    // Get the booking
    final booking = getInstantBooking(bookingId);
    if (booking == null) {
      throw Exception('Booking not found');
    }

    // Check if the booking can be cancelled
    if (booking.status != InstantBookingStatus.success) {
      throw Exception('Only successful bookings can be cancelled');
    }

    // Update the booking status
    final cancelledBooking = booking.copyWith(
      status: InstantBookingStatus.cancelled,
    );

    // Save the cancelled booking
    await _saveInstantBooking(cancelledBooking);

    // Notify listeners
    _notifyInstantBookingsListeners();
    _notifyInstantBookingStatusListeners(cancelledBooking);

    return cancelledBooking;
  }

  /// Saves an instant booking
  Future<void> _saveInstantBooking(InstantBookingModel booking) async {
    await _instantBookingsBox.put(
      booking.id,
      jsonEncode(booking.toJson()),
    );
  }

  /// Gets the travel service booking type
  InstantBookingType _getTravelServiceBookingType(TravelService travelService) {
    if (travelService is Hotel) {
      return InstantBookingType.hotel;
    } else if (travelService is Flight) {
      return InstantBookingType.flight;
    } else if (travelService is CarRental) {
      return InstantBookingType.carRental;
    } else if (travelService is Restaurant) {
      return InstantBookingType.restaurant;
    } else if (travelService is PrivateSecurity) {
      return InstantBookingType.privateSecurity;
    } else if (travelService is Cruise) {
      return InstantBookingType.cruise;
    } else {
      throw Exception('Unsupported travel service type');
    }
  }

  /// Gets the travel service type string
  String _getTravelServiceType(TravelService travelService) {
    if (travelService is Hotel) {
      return 'hotel';
    } else if (travelService is Flight) {
      return 'flight';
    } else if (travelService is CarRental) {
      return 'car';
    } else if (travelService is Restaurant) {
      return 'restaurant';
    } else if (travelService is PrivateSecurity) {
      return 'security';
    } else if (travelService is Cruise) {
      return 'cruise';
    } else {
      throw Exception('Unsupported travel service type');
    }
  }

  /// Stream of instant bookings
  Stream<List<InstantBookingModel>> get instantBookingsStream =>
      _instantBookingsController.stream;

  /// Stream of instant booking status updates
  Stream<InstantBookingModel> get instantBookingStatusStream =>
      _instantBookingStatusController.stream;

  /// Disposes of resources
  void dispose() {
    _instantBookingsController.close();
    _instantBookingStatusController.close();
  }
}

/// Provider for the instant booking service
final instantBookingServiceProvider = Provider<InstantBookingService>((ref) {
  final paymentService = ref.watch(enhancedPaymentServiceProvider);
  final bookingService = ref.watch(Provider<booking_service.BookingService>(
      (ref) => booking_service.BookingService()));
  final availabilityService = ref.watch(travelAvailabilityServiceProvider);

  final service = InstantBookingService(
    paymentService: paymentService,
    bookingService: bookingService,
    availabilityService: availabilityService,
  );

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
