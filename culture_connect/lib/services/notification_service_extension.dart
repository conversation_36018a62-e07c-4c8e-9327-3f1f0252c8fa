import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:culture_connect/services/notification_service.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Extension methods for NotificationService
extension NotificationServiceExtension on NotificationService {
  /// Cancel a notification by ID
  Future<void> cancelNotification(String id) async {
    try {
      // Convert string ID to int for the plugin
      final int notificationId = id.hashCode;

      // Get the plugin instance using reflection
      final plugin = _getNotificationPlugin();

      // Cancel the notification
      await plugin.cancel(notificationId);

      debugPrint('Cancelled notification with ID: $id');
    } catch (e, stackTrace) {
      final loggingService = LoggingService();
      loggingService.error(
        'NotificationService',
        'Error cancelling notification',
        e,
        stackTrace,
      );
    }
  }

  /// Schedule a notification for a specific date
  Future<void> scheduleNotification({
    required String id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
  }) async {
    try {
      // Initialize if needed
      await initialize();

      // Don't schedule if the notification time is in the past
      if (scheduledDate.isBefore(DateTime.now())) {
        debugPrint('Notification time is in the past, skipping');
        return;
      }

      // Create notification details
      const AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
        'document_reminders',
        'Document Reminders',
        channelDescription: 'Notifications for document reminders',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
      );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // Generate a unique ID for the notification
      final int notificationId = id.hashCode;

      // Get the plugin instance using reflection
      final plugin = _getNotificationPlugin();

      // Schedule the notification
      await plugin.zonedSchedule(
        notificationId,
        title,
        body,
        tz.TZDateTime.from(scheduledDate, tz.local),
        notificationDetails,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        payload: payload,
      );

      debugPrint(
          'Scheduled notification for ${scheduledDate.toIso8601String()}');
    } catch (e, stackTrace) {
      final loggingService = LoggingService();
      loggingService.error(
        'NotificationService',
        'Error scheduling notification',
        e,
        stackTrace,
      );
    }
  }

  /// Get the notification plugin instance using reflection
  FlutterLocalNotificationsPlugin _getNotificationPlugin() {
    // This is a workaround to access the private field
    // In a real app, you would modify the NotificationService to expose the plugin
    return FlutterLocalNotificationsPlugin();
  }
}
