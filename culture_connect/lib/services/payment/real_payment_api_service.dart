import 'dart:async';
import 'package:dio/dio.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/payment/payment_config_service.dart';
import 'package:culture_connect/services/payment/payment_auth_service.dart';
import 'package:culture_connect/utils/exceptions/payment_exceptions.dart';

/// Production-ready payment API service with complete backend integration
class RealPaymentApiService {
  static const int _timeoutSeconds = 30;
  static const int _maxRetries = 3;

  final Dio _dio;
  final LoggingService _loggingService;
  final PaymentConfigService _configService;
  final PaymentAuthService _authService;

  bool _isInitialized = false;

  RealPaymentApiService({
    required LoggingService loggingService,
    required PaymentConfigService configService,
    required PaymentAuthService authService,
  })  : _loggingService = loggingService,
        _configService = configService,
        _authService = authService,
        _dio = Dio();

  /// Initialize the payment API service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Configure Dio with dynamic base URL from config service
      _dio.options = BaseOptions(
        baseUrl: _configService.apiBaseUrl,
        connectTimeout: const Duration(seconds: _timeoutSeconds),
        receiveTimeout: const Duration(seconds: _timeoutSeconds),
        sendTimeout: const Duration(seconds: _timeoutSeconds),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'CultureConnect-Mobile/1.0',
        },
      );

      // Add authentication interceptor
      _dio.interceptors.add(InterceptorsWrapper(
        onRequest: (options, handler) async {
          // Add authentication token to requests
          final token = await _authService.getPaymentToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }

          // Add correlation ID for request tracking
          options.headers['X-Correlation-ID'] = _generateCorrelationId();

          // Add user context if available
          final userContext = _authService.userContext;
          if (userContext != null) {
            options.headers['X-User-ID'] = userContext.userId;
            options.headers['X-User-Country'] = userContext.countryCode;
          }

          handler.next(options);
        },
        onError: (error, handler) async {
          // Handle token expiration with automatic retry
          if (error.response?.statusCode == 401) {
            _loggingService.warning(
              'RealPaymentApiService',
              'Authentication token expired, attempting refresh',
              {'endpoint': error.requestOptions.path},
            );

            // Token refresh is handled by PaymentAuthService automatically
            // Just log the error and let retry mechanism handle it
          }
          handler.next(error);
        },
      ));

      // Add logging interceptor
      _dio.interceptors.add(LogInterceptor(
        requestBody: false, // Don't log sensitive payment data
        responseBody: false,
        logPrint: (object) => _loggingService.info(
          'RealPaymentApiService',
          'HTTP Request/Response',
          {'log': object.toString()},
        ),
      ));

      _isInitialized = true;

      _loggingService.info(
        'RealPaymentApiService',
        'Payment API service initialized',
        {
          'base_url': _configService.apiBaseUrl,
          'environment': _configService.currentEnvironment.name,
        },
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'RealPaymentApiService',
        'Failed to initialize payment API service',
        {'error': e.toString()},
        stackTrace,
      );
      rethrow;
    }
  }

  /// Initialize payment with backend
  /// Endpoint: POST /api/payments/initialize
  /// Authentication: Bearer JWT token
  /// Request format: PaymentInitRequest.toJson()
  /// Response format: PaymentInitResponse.fromJson()
  /// Error handling: 400 (validation), 401 (auth), 500 (server)
  Future<PaymentInitResponse> initializePayment(
    PaymentInitRequest request,
  ) async {
    if (!_isInitialized) {
      throw const PaymentException(
        'Payment API service not initialized',
        code: 'API_NOT_INITIALIZED',
      );
    }

    try {
      final response = await _retryRequest(() async {
        return await _dio.post(
          '/api/payments/initialize',
          data: request.toJson(),
        );
      });

      if (response.statusCode == 200) {
        final paymentInit = PaymentInitResponse.fromJson(
          response.data as Map<String, dynamic>,
        );

        _loggingService.info(
          'RealPaymentApiService',
          'Payment initialized successfully',
          {
            'transaction_reference': paymentInit.transactionReference,
            'selected_provider': paymentInit.selectedProvider.name,
            'correlation_id': paymentInit.correlationId,
          },
        );

        return paymentInit;
      } else {
        throw PaymentApiException(
          'Payment initialization failed',
          statusCode: response.statusCode,
          correlationId: response.requestOptions.headers['X-Correlation-ID'],
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e, 'initializePayment');
    } catch (e) {
      throw PaymentApiException(
        'Unexpected error during payment initialization: $e',
        correlationId: _generateCorrelationId(),
      );
    }
  }

  /// Verify payment status with backend
  /// Endpoint: POST /api/payments/verify
  /// Authentication: Bearer JWT token
  /// Request format: PaymentVerificationRequest.toJson()
  /// Response format: PaymentVerificationResponse.fromJson()
  /// Polling interval: 5 seconds for pending payments
  Future<PaymentVerificationResponse> verifyPayment(
    PaymentVerificationRequest request,
  ) async {
    if (!_isInitialized) {
      throw const PaymentException(
        'Payment API service not initialized',
        code: 'API_NOT_INITIALIZED',
      );
    }

    try {
      final response = await _retryRequest(() async {
        return await _dio.post(
          '/api/payments/verify',
          data: request.toJson(),
        );
      });

      if (response.statusCode == 200) {
        final verification = PaymentVerificationResponse.fromJson(
          response.data as Map<String, dynamic>,
        );

        _loggingService.info(
          'RealPaymentApiService',
          'Payment verification completed',
          {
            'transaction_reference': request.transactionReference,
            'status': verification.status.name,
            'is_successful': verification.isSuccessful,
          },
        );

        return verification;
      } else {
        throw PaymentApiException(
          'Payment verification failed',
          statusCode: response.statusCode,
          correlationId: response.requestOptions.headers['X-Correlation-ID'],
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e, 'verifyPayment');
    } catch (e) {
      throw PaymentApiException(
        'Unexpected error during payment verification: $e',
        correlationId: _generateCorrelationId(),
      );
    }
  }

  /// Get payment status by transaction reference
  /// Endpoint: GET /api/payments/status/{reference}
  /// Authentication: Bearer JWT token
  /// Response format: PaymentVerificationResponse.fromJson()
  /// Used for real-time status monitoring
  Future<PaymentVerificationResponse> getPaymentStatus(
    String transactionReference,
  ) async {
    if (!_isInitialized) {
      throw const PaymentException(
        'Payment API service not initialized',
        code: 'API_NOT_INITIALIZED',
      );
    }

    try {
      final response = await _retryRequest(() async {
        return await _dio.get(
          '/api/payments/status/$transactionReference',
        );
      });

      if (response.statusCode == 200) {
        return PaymentVerificationResponse.fromJson(
          response.data as Map<String, dynamic>,
        );
      } else {
        throw PaymentApiException(
          'Failed to get payment status',
          statusCode: response.statusCode,
          correlationId: response.requestOptions.headers['X-Correlation-ID'],
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e, 'getPaymentStatus');
    } catch (e) {
      throw PaymentApiException(
        'Unexpected error getting payment status: $e',
        correlationId: _generateCorrelationId(),
      );
    }
  }

  /// Get provider configurations from backend
  /// Endpoint: GET /api/payments/provider-configs
  /// Authentication: Bearer JWT token
  /// Response: Map of provider configurations
  Future<Map<PaymentProvider, PaymentProviderConfiguration>>
      getProviderConfigurations() async {
    if (!_isInitialized) {
      throw const PaymentException(
        'Payment API service not initialized',
        code: 'API_NOT_INITIALIZED',
      );
    }

    try {
      final response = await _retryRequest(() async {
        return await _dio.get('/api/payments/provider-configs');
      });

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        final configs = <PaymentProvider, PaymentProviderConfiguration>{};

        for (final entry in data.entries) {
          final provider = PaymentProvider.values.firstWhere(
            (p) => p.name == entry.key,
          );
          configs[provider] = PaymentProviderConfiguration.fromJson(
            entry.value as Map<String, dynamic>,
          );
        }

        _loggingService.info(
          'RealPaymentApiService',
          'Provider configurations loaded',
          {'providers': configs.keys.map((p) => p.name).toList()},
        );

        return configs;
      } else {
        throw PaymentApiException(
          'Failed to get provider configurations',
          statusCode: response.statusCode,
          correlationId: response.requestOptions.headers['X-Correlation-ID'],
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e, 'getProviderConfigurations');
    } catch (e) {
      throw PaymentApiException(
        'Unexpected error getting provider configurations: $e',
        correlationId: _generateCorrelationId(),
      );
    }
  }

  /// Retry request with exponential backoff
  Future<Response> _retryRequest(Future<Response> Function() request) async {
    int attempts = 0;
    Duration delay = const Duration(milliseconds: 500);

    while (attempts < _maxRetries) {
      try {
        return await request();
      } on DioException catch (e) {
        attempts++;

        if (attempts >= _maxRetries || !_shouldRetry(e)) {
          rethrow;
        }

        _loggingService.warning(
          'RealPaymentApiService',
          'Request failed, retrying in ${delay.inMilliseconds}ms (attempt $attempts/$_maxRetries)',
          {'error': e.toString()},
        );

        await Future.delayed(delay);
        delay *= 2; // Exponential backoff
      }
    }

    throw PaymentApiException(
      'Max retry attempts exceeded',
      correlationId: _generateCorrelationId(),
    );
  }

  /// Check if error should trigger a retry
  bool _shouldRetry(DioException error) {
    if (error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.receiveTimeout ||
        error.type == DioExceptionType.sendTimeout) {
      return true;
    }

    if (error.response?.statusCode != null) {
      final statusCode = error.response!.statusCode!;
      return statusCode >= 500 ||
          statusCode == 429; // Server errors or rate limiting
    }

    return false;
  }

  /// Handle Dio exceptions and convert to PaymentApiException
  PaymentApiException _handleDioException(
      DioException error, String operation) {
    final correlationId =
        error.requestOptions.headers['X-Correlation-ID'] as String?;

    _loggingService.error(
      'RealPaymentApiService',
      'API error in $operation',
      {
        'error_type': error.type.name,
        'status_code': error.response?.statusCode,
        'correlation_id': correlationId,
        'message': error.message,
      },
      error.stackTrace,
    );

    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return PaymentApiException(
          'Connection timeout. Please check your internet connection.',
          statusCode: null,
          correlationId: correlationId,
          isNetworkError: true,
        );

      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message = error.response?.data?['message'] as String? ??
            'Request failed with status $statusCode';

        return PaymentApiException(
          message,
          statusCode: statusCode,
          correlationId: correlationId,
        );

      case DioExceptionType.cancel:
        return PaymentApiException(
          'Request was cancelled',
          correlationId: correlationId,
        );

      default:
        return PaymentApiException(
          'Network error: ${error.message}',
          correlationId: correlationId,
          isNetworkError: true,
        );
    }
  }

  /// Generate unique correlation ID for request tracking
  String _generateCorrelationId() {
    return 'cc_${DateTime.now().millisecondsSinceEpoch}_${_generateRandomString(8)}';
  }

  /// Generate random string for correlation ID
  String _generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    return List.generate(
      length,
      (index) =>
          chars[(DateTime.now().millisecondsSinceEpoch + index) % chars.length],
    ).join();
  }

  /// Dispose resources
  void dispose() {
    _dio.close();
    _isInitialized = false;
  }
}
