import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/utils/logger.dart';

/// Service for handling local storage operations
class LocalStorageService {
  // Singleton instance
  static final LocalStorageService _instance = LocalStorageService._internal();
  factory LocalStorageService() => _instance;
  LocalStorageService._internal();

  // Logger
  final _logger = Logger('LocalStorageService');

  // Shared preferences instance
  late SharedPreferences _prefs;

  // Keys
  static const String _keyPrefix = 'culture_connect_';
  static const String _keyUserData = '${_keyPrefix}user_data';
  static const String _keyAuthToken = '${_keyPrefix}auth_token';
  static const String _keyAppSettings = '${_keyPrefix}app_settings';
  static const String _keyCurrencyPreferences =
      '${_keyPrefix}currency_preferences';
  static const String _keyOfflineData = '${_keyPrefix}offline_data';

  /// Initialize the service
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      _logger.info('LocalStorageService initialized');
    } catch (e) {
      _logger.error('Error initializing LocalStorageService: $e');
      rethrow;
    }
  }

  /// Save a string value
  Future<bool> setString(String key, String value) async {
    try {
      return await _prefs.setString(key, value);
    } catch (e) {
      _logger.error('Error saving string value: $e');
      return false;
    }
  }

  /// Get a string value
  String? getString(String key) {
    try {
      return _prefs.getString(key);
    } catch (e) {
      _logger.error('Error getting string value: $e');
      return null;
    }
  }

  /// Save a boolean value
  Future<bool> setBool(String key, bool value) async {
    try {
      return await _prefs.setBool(key, value);
    } catch (e) {
      _logger.error('Error saving boolean value: $e');
      return false;
    }
  }

  /// Get a boolean value
  bool? getBool(String key) {
    try {
      return _prefs.getBool(key);
    } catch (e) {
      _logger.error('Error getting boolean value: $e');
      return null;
    }
  }

  /// Save an integer value
  Future<bool> setInt(String key, int value) async {
    try {
      return await _prefs.setInt(key, value);
    } catch (e) {
      _logger.error('Error saving integer value: $e');
      return false;
    }
  }

  /// Get an integer value
  int? getInt(String key) {
    try {
      return _prefs.getInt(key);
    } catch (e) {
      _logger.error('Error getting integer value: $e');
      return null;
    }
  }

  /// Save a double value
  Future<bool> setDouble(String key, double value) async {
    try {
      return await _prefs.setDouble(key, value);
    } catch (e) {
      _logger.error('Error saving double value: $e');
      return false;
    }
  }

  /// Get a double value
  double? getDouble(String key) {
    try {
      return _prefs.getDouble(key);
    } catch (e) {
      _logger.error('Error getting double value: $e');
      return null;
    }
  }

  /// Save a list of strings
  Future<bool> setStringList(String key, List<String> value) async {
    try {
      return await _prefs.setStringList(key, value);
    } catch (e) {
      _logger.error('Error saving string list: $e');
      return false;
    }
  }

  /// Get a list of strings
  List<String>? getStringList(String key) {
    try {
      return _prefs.getStringList(key);
    } catch (e) {
      _logger.error('Error getting string list: $e');
      return null;
    }
  }

  /// Save a JSON object
  Future<bool> setJson(String key, Map<String, dynamic> value) async {
    try {
      final jsonString = json.encode(value);
      return await _prefs.setString(key, jsonString);
    } catch (e) {
      _logger.error('Error saving JSON object: $e');
      return false;
    }
  }

  /// Get a JSON object
  Map<String, dynamic>? getJson(String key) {
    try {
      final jsonString = _prefs.getString(key);
      if (jsonString == null) return null;
      return json.decode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      _logger.error('Error getting JSON object: $e');
      return null;
    }
  }

  /// Remove a value
  Future<bool> remove(String key) async {
    try {
      return await _prefs.remove(key);
    } catch (e) {
      _logger.error('Error removing value: $e');
      return false;
    }
  }

  /// Clear all values
  Future<bool> clear() async {
    try {
      return await _prefs.clear();
    } catch (e) {
      _logger.error('Error clearing all values: $e');
      return false;
    }
  }

  /// Save user data
  Future<bool> saveUserData(Map<String, dynamic> userData) async {
    return await setJson(_keyUserData, userData);
  }

  /// Get user data
  Map<String, dynamic>? getUserData() {
    return getJson(_keyUserData);
  }

  /// Save auth token
  Future<bool> saveAuthToken(String token) async {
    return await setString(_keyAuthToken, token);
  }

  /// Get auth token
  String? getAuthToken() {
    return getString(_keyAuthToken);
  }

  /// Save app settings
  Future<bool> saveAppSettings(Map<String, dynamic> settings) async {
    return await setJson(_keyAppSettings, settings);
  }

  /// Get app settings
  Map<String, dynamic>? getAppSettings() {
    return getJson(_keyAppSettings);
  }

  /// Save currency preferences
  Future<bool> saveCurrencyPreferences(Map<String, dynamic> preferences) async {
    return await setJson(_keyCurrencyPreferences, preferences);
  }

  /// Get currency preferences
  Map<String, dynamic>? getCurrencyPreferences() {
    return getJson(_keyCurrencyPreferences);
  }

  /// Save offline data
  Future<bool> saveOfflineData(Map<String, dynamic> data) async {
    return await setJson(_keyOfflineData, data);
  }

  /// Get offline data
  Map<String, dynamic>? getOfflineData() {
    return getJson(_keyOfflineData);
  }
}
