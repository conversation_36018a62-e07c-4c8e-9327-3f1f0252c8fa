import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/travel/document/travel_documents.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Service for managing travel documents
class TravelDocumentService {
  // Singleton instance
  static final TravelDocumentService _instance =
      TravelDocumentService._internal();
  factory TravelDocumentService() => _instance;
  TravelDocumentService._internal();

  // Firebase instances
  final FirebaseFirestore? _firestore =
      kIsWeb ? null : FirebaseFirestore.instance;
  final FirebaseStorage? _storage = kIsWeb ? null : FirebaseStorage.instance;

  // Hive box for caching documents
  final Box<String>? _documentsBox = Hive.isBoxOpen('travel_documents')
      ? Hive.box<String>('travel_documents')
      : null;

  // Logging service
  final LoggingService _loggingService = LoggingService();

  // In-memory storage for documents (used when Firebase is not available)
  final List<TravelDocument> _documents = [];

  /// Initialize the service
  Future<void> initialize() async {
    if (_documents.isNotEmpty) return;

    try {
      // Try to get data from Hive cache
      if (_documentsBox != null) {
        final cachedDocuments = _documentsBox!.values.toList();
        if (cachedDocuments.isNotEmpty) {
          _documents.clear();
          for (final jsonStr in cachedDocuments) {
            final json = jsonDecode(jsonStr);
            if (json['type'] == 'passport') {
              _documents.add(Passport.fromJson(json));
            } else if (json['type'] == 'visa') {
              _documents.add(Visa.fromJson(json));
            }
          }
          return;
        }
      }

      // Try to get data from Firestore
      if (_firestore != null) {
        final snapshot = await _firestore!.collection('travel_documents').get();
        if (snapshot.docs.isNotEmpty) {
          _documents.clear();
          for (final doc in snapshot.docs) {
            final data = doc.data();
            if (data['type'] == 'passport') {
              _documents.add(Passport.fromJson({...data, 'id': doc.id}));
            } else if (data['type'] == 'visa') {
              _documents.add(Visa.fromJson({...data, 'id': doc.id}));
            }
          }

          // Cache the documents
          if (_documentsBox != null) {
            for (final document in _documents) {
              await _documentsBox!
                  .put(document.id, jsonEncode(document.toJson()));
            }
          }

          return;
        }
      }

      // If no data is available, generate mock data
      await _generateMockData();
    } catch (e, stackTrace) {
      _loggingService.error(
        'TravelDocumentService',
        'Error initializing travel document service',
        e,
        stackTrace,
      );

      // If there's an error, generate mock data
      await _generateMockData();
    }
  }

  /// Get all travel documents for a user
  Future<List<TravelDocument>> getDocuments(String userId) async {
    await initialize();
    return _documents.where((doc) => doc.userId == userId).toList();
  }

  /// Get a travel document by ID
  Future<TravelDocument?> getDocument(String id) async {
    await initialize();
    try {
      return _documents.firstWhere((doc) => doc.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get all passports for a user
  Future<List<Passport>> getPassports(String userId) async {
    await initialize();
    return _documents
        .where((doc) => doc.userId == userId && doc is Passport)
        .cast<Passport>()
        .toList();
  }

  /// Get all visas for a user
  Future<List<Visa>> getVisas(String userId) async {
    await initialize();
    return _documents
        .where((doc) => doc.userId == userId && doc is Visa)
        .cast<Visa>()
        .toList();
  }

  /// Add a new passport
  Future<Passport> addPassport(Passport passport) async {
    await initialize();

    final newPassport = passport.copyWith(
      id: const Uuid().v4(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    _documents.add(newPassport);

    // Save to Firestore
    if (_firestore != null) {
      try {
        await _firestore!
            .collection('travel_documents')
            .doc(newPassport.id)
            .set(newPassport.toJson());
      } catch (e, stackTrace) {
        _loggingService.error(
          'TravelDocumentService',
          'Error adding passport to Firestore',
          e,
          stackTrace,
        );
      }
    }

    // Cache the document
    if (_documentsBox != null) {
      try {
        await _documentsBox!
            .put(newPassport.id, jsonEncode(newPassport.toJson()));
      } catch (e, stackTrace) {
        _loggingService.error(
          'TravelDocumentService',
          'Error caching passport',
          e,
          stackTrace,
        );
      }
    }

    return newPassport;
  }

  /// Add a new visa
  Future<Visa> addVisa(Visa visa) async {
    await initialize();

    final newVisa = visa.copyWith(
      id: const Uuid().v4(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    _documents.add(newVisa);

    // Save to Firestore
    if (_firestore != null) {
      try {
        await _firestore!
            .collection('travel_documents')
            .doc(newVisa.id)
            .set(newVisa.toJson());
      } catch (e, stackTrace) {
        _loggingService.error(
          'TravelDocumentService',
          'Error adding visa to Firestore',
          e,
          stackTrace,
        );
      }
    }

    // Cache the document
    if (_documentsBox != null) {
      try {
        await _documentsBox!.put(newVisa.id, jsonEncode(newVisa.toJson()));
      } catch (e, stackTrace) {
        _loggingService.error(
          'TravelDocumentService',
          'Error caching visa',
          e,
          stackTrace,
        );
      }
    }

    return newVisa;
  }

  /// Update a passport
  Future<Passport> updatePassport(Passport passport) async {
    await initialize();

    final index = _documents.indexWhere((doc) => doc.id == passport.id);
    if (index == -1) {
      throw Exception('Passport not found');
    }

    final updatedPassport = passport.copyWith(
      updatedAt: DateTime.now(),
    );

    _documents[index] = updatedPassport;

    // Update in Firestore
    if (_firestore != null) {
      try {
        await _firestore!
            .collection('travel_documents')
            .doc(updatedPassport.id)
            .update(updatedPassport.toJson());
      } catch (e, stackTrace) {
        _loggingService.error(
          'TravelDocumentService',
          'Error updating passport in Firestore',
          e,
          stackTrace,
        );
      }
    }

    // Update cache
    if (_documentsBox != null) {
      try {
        await _documentsBox!
            .put(updatedPassport.id, jsonEncode(updatedPassport.toJson()));
      } catch (e, stackTrace) {
        _loggingService.error(
          'TravelDocumentService',
          'Error updating cached passport',
          e,
          stackTrace,
        );
      }
    }

    return updatedPassport;
  }

  /// Update a visa
  Future<Visa> updateVisa(Visa visa) async {
    await initialize();

    final index = _documents.indexWhere((doc) => doc.id == visa.id);
    if (index == -1) {
      throw Exception('Visa not found');
    }

    final updatedVisa = visa.copyWith(
      updatedAt: DateTime.now(),
    );

    _documents[index] = updatedVisa;

    // Update in Firestore
    if (_firestore != null) {
      try {
        await _firestore!
            .collection('travel_documents')
            .doc(updatedVisa.id)
            .update(updatedVisa.toJson());
      } catch (e, stackTrace) {
        _loggingService.error(
          'TravelDocumentService',
          'Error updating visa in Firestore',
          e,
          stackTrace,
        );
      }
    }

    // Update cache
    if (_documentsBox != null) {
      try {
        await _documentsBox!
            .put(updatedVisa.id, jsonEncode(updatedVisa.toJson()));
      } catch (e, stackTrace) {
        _loggingService.error(
          'TravelDocumentService',
          'Error updating cached visa',
          e,
          stackTrace,
        );
      }
    }

    return updatedVisa;
  }

  /// Delete a travel document
  Future<void> deleteDocument(String id) async {
    await initialize();

    final index = _documents.indexWhere((doc) => doc.id == id);
    if (index == -1) {
      throw Exception('Document not found');
    }

    _documents.removeAt(index);

    // Delete from Firestore
    if (_firestore != null) {
      try {
        await _firestore!.collection('travel_documents').doc(id).delete();
      } catch (e, stackTrace) {
        _loggingService.error(
          'TravelDocumentService',
          'Error deleting document from Firestore',
          e,
          stackTrace,
        );
      }
    }

    // Delete from cache
    if (_documentsBox != null) {
      try {
        await _documentsBox!.delete(id);
      } catch (e, stackTrace) {
        _loggingService.error(
          'TravelDocumentService',
          'Error deleting cached document',
          e,
          stackTrace,
        );
      }
    }
  }

  /// Upload a document image
  Future<String> uploadDocumentImage(
      String userId, String documentId, Uint8List imageData) async {
    // Generate a unique filename
    final filename =
        '${userId}_${documentId}_${DateTime.now().millisecondsSinceEpoch}.jpg';

    // Upload to Firebase Storage
    if (_storage != null) {
      try {
        final ref = _storage!.ref().child('travel_documents/$userId/$filename');
        await ref.putData(imageData);
        return await ref.getDownloadURL();
      } catch (e, stackTrace) {
        _loggingService.error(
          'TravelDocumentService',
          'Error uploading document image to Firebase Storage',
          e,
          stackTrace,
        );
      }
    }

    // Return a mock URL if Firebase Storage is not available
    return 'https://example.com/travel_documents/$userId/$filename';
  }

  /// Generate mock data for testing
  Future<void> _generateMockData() async {
    _documents.clear();

    // Add mock passports
    _documents.add(
      Passport(
        id: '1',
        userId: 'user1',
        name: 'John Doe',
        documentNumber: '*********',
        issuedBy: 'United States',
        issuedDate: DateTime.now().subtract(const Duration(days: 365 * 2)),
        expiryDate: DateTime.now().add(const Duration(days: 365 * 8)),
        status: TravelDocumentStatus.valid,
        documentImageUrls: [
          'https://example.com/passport1.jpg',
          'https://example.com/passport1_back.jpg',
        ],
        createdAt: DateTime.now().subtract(const Duration(days: 365 * 2)),
        updatedAt: DateTime.now().subtract(const Duration(days: 30)),
        nationality: 'United States',
        countryCode: 'US',
        placeOfBirth: 'New York',
        dateOfBirth: DateTime(1985, 5, 15),
        gender: 'M',
      ),
    );

    // Add mock visas
    _documents.add(
      Visa(
        id: '2',
        userId: 'user1',
        name: 'Japan Tourist Visa',
        documentNumber: 'V9876543',
        issuedBy: 'Japanese Embassy',
        issuedDate: DateTime.now().subtract(const Duration(days: 30)),
        expiryDate: DateTime.now().add(const Duration(days: 90)),
        status: TravelDocumentStatus.valid,
        documentImageUrls: [
          'https://example.com/visa1.jpg',
        ],
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 30)),
        visaType: VisaType.tourist,
        entryType: VisaEntryType.single,
        countryOfIssue: 'United States',
        countryValidFor: 'Japan',
        maxStayDuration: 90,
      ),
    );
  }
}
