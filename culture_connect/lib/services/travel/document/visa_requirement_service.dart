import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hive/hive.dart';
import 'package:culture_connect/models/travel/document/visa_requirement.dart';
import 'package:culture_connect/services/connectivity_service.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Service for managing visa requirements
class VisaRequirementService {
  // Singleton instance
  static final VisaRequirementService _instance =
      VisaRequirementService._internal();
  factory VisaRequirementService() => _instance;
  VisaRequirementService._internal();

  // Services
  final ConnectivityService _connectivityService = ConnectivityService();
  final LoggingService _loggingService = LoggingService();

  // Firestore reference
  final FirebaseFirestore? _firestore = FirebaseFirestore.instance;

  // Hive box for caching visa requirements
  Box<String>? _requirementsBox;

  // In-memory storage for visa requirements
  final Map<String, VisaRequirement> _requirements = {};

  // Flag to track initialization
  bool _isInitialized = false;

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Open Hive box for caching
      _requirementsBox = await Hive.openBox<String>('visa_requirements');

      // Try to get data from Firestore
      if (_firestore != null) {
        final snapshot =
            await _firestore!.collection('visa_requirements').get();
        if (snapshot.docs.isNotEmpty) {
          _requirements.clear();
          for (final doc in snapshot.docs) {
            final requirement =
                VisaRequirement.fromJson({...doc.data(), 'id': doc.id});
            final key = '${requirement.countryFrom}_${requirement.countryTo}';
            _requirements[key] = requirement;

            // Cache the requirement
            if (_requirementsBox != null) {
              await _requirementsBox!
                  .put(key, jsonEncode(requirement.toJson()));
            }
          }

          _isInitialized = true;
          return;
        }
      }

      // If no data is available from Firestore, try to load from cache
      if (_requirementsBox != null && _requirementsBox!.isNotEmpty) {
        _requirements.clear();
        for (final key in _requirementsBox!.keys) {
          final json =
              jsonDecode(_requirementsBox!.get(key)!) as Map<String, dynamic>;
          final requirement = VisaRequirement.fromJson(json);
          _requirements[key] = requirement;
        }

        _isInitialized = true;
        return;
      }

      // If no data is available, generate mock data
      await _generateMockData();
      _isInitialized = true;
    } catch (e, stackTrace) {
      _loggingService.error(
        'VisaRequirementService',
        'Error initializing visa requirement service',
        e,
        stackTrace,
      );

      // If there's an error, generate mock data
      await _generateMockData();
      _isInitialized = true;
    }
  }

  /// Get visa requirements between two countries
  Future<VisaRequirement?> getVisaRequirement(
      String countryFrom, String countryTo) async {
    await initialize();

    final key = '${countryFrom}_$countryTo';

    // Check if we have the requirement in memory
    if (_requirements.containsKey(key)) {
      return _requirements[key];
    }

    // Check if we have the requirement in cache
    VisaRequirement? cachedData;
    if (_requirementsBox != null && _requirementsBox!.containsKey(key)) {
      try {
        final json =
            jsonDecode(_requirementsBox!.get(key)!) as Map<String, dynamic>;
        cachedData = VisaRequirement.fromJson(json);
      } catch (e) {
        // Ignore cache errors
      }
    }

    // If not in cache, try to fetch from API
    final isConnected = await _connectivityService.isConnected();
    if (isConnected) {
      try {
        final requirement = await _fetchVisaRequirement(countryFrom, countryTo);
        if (requirement != null) {
          _requirements[key] = requirement;

          // Cache the requirement
          if (_requirementsBox != null) {
            await _requirementsBox!.put(key, jsonEncode(requirement.toJson()));
          }

          // Save to Firestore
          if (_firestore != null) {
            await _firestore!
                .collection('visa_requirements')
                .doc(requirement.id)
                .set(requirement.toJson());
          }

          return requirement;
        }
      } catch (e, stackTrace) {
        _loggingService.error(
          'VisaRequirementService',
          'Error fetching visa requirement',
          e,
          stackTrace,
        );
      }
    }

    // If not found, return cached data or null
    return cachedData;
  }

  /// Get all visa requirements
  Future<List<VisaRequirement>> getAllVisaRequirements() async {
    await initialize();
    return _requirements.values.toList();
  }

  /// Get all visa requirements for a specific country of origin
  Future<List<VisaRequirement>> getVisaRequirementsForCountry(
      String countryFrom) async {
    await initialize();

    try {
      // Filter requirements by country of origin
      final countryRequirements = _requirements.values
          .where((requirement) => requirement.countryFrom == countryFrom)
          .toList();

      // If we have requirements in memory, return them
      if (countryRequirements.isNotEmpty) {
        return countryRequirements;
      }

      // If not in memory, try to fetch from Firestore
      if (_firestore != null) {
        try {
          final snapshot = await _firestore!
              .collection('visa_requirements')
              .where('countryFrom', isEqualTo: countryFrom)
              .get();

          if (snapshot.docs.isNotEmpty) {
            final requirements = <VisaRequirement>[];
            for (final doc in snapshot.docs) {
              final requirement =
                  VisaRequirement.fromJson({...doc.data(), 'id': doc.id});
              final key = '${requirement.countryFrom}_${requirement.countryTo}';
              _requirements[key] = requirement; // Cache in memory
              requirements.add(requirement);

              // Cache the requirement
              if (_requirementsBox != null) {
                await _requirementsBox!
                    .put(key, jsonEncode(requirement.toJson()));
              }
            }

            return requirements;
          }
        } catch (e, stackTrace) {
          _loggingService.error(
            'VisaRequirementService',
            'Error fetching visa requirements for country from Firestore',
            e,
            stackTrace,
          );
        }
      }

      // If not found in Firestore, return empty list
      return [];
    } catch (e, stackTrace) {
      _loggingService.error(
        'VisaRequirementService',
        'Error getting visa requirements for country',
        e,
        stackTrace,
      );
      return [];
    }
  }

  /// Check if a visa is required for a destination
  Future<bool> isVisaRequired(String countryFrom, String countryTo) async {
    final requirement = await getVisaRequirement(countryFrom, countryTo);
    if (requirement == null) {
      return true; // Assume visa is required if no information is available
    }

    return requirement.requirementType == VisaRequirementType.visaRequired ||
        requirement.requirementType == VisaRequirementType.eVisa ||
        requirement.requirementType == VisaRequirementType.visaOnArrival ||
        requirement.requirementType == VisaRequirementType.specialPermit;
  }

  /// Fetch visa requirement from API
  Future<VisaRequirement?> _fetchVisaRequirement(
      String countryFrom, String countryTo) async {
    // In a real app, this would make an API call to get visa requirements
    // For now, we'll just return a mock requirement
    await Future.delayed(
        const Duration(milliseconds: 500)); // Simulate network delay

    // Check if we have a mock requirement for this country pair
    for (final requirement in _requirements.values) {
      if (requirement.countryFrom == countryFrom &&
          requirement.countryTo == countryTo) {
        return requirement;
      }
    }

    // If not found, return null
    return null;
  }

  /// Generate mock data for testing
  Future<void> _generateMockData() async {
    _requirements.clear();

    final mockRequirements = [
      VisaRequirement(
        id: '1',
        countryFrom: 'US',
        countryTo: 'JP',
        requirementType: VisaRequirementType.visaRequired,
        description:
            'US citizens need a visa to visit Japan for tourism purposes.',
        maxStayDuration: 90,
        processingTime: 5,
        visaFee: 50.0,
        requiredDocuments: [
          'Valid passport',
          'Visa application form',
          'Passport-sized photo',
          'Flight itinerary',
          'Hotel reservation',
        ],
        applicationUrl:
            'https://www.us.emb-japan.go.jp/itpr_en/travel_and_visatml',
        lastUpdated: DateTime.now().subtract(const Duration(days: 30)),
      ),
      VisaRequirement(
        id: '2',
        countryFrom: 'US',
        countryTo: 'UK',
        requirementType: VisaRequirementType.noVisaRequired,
        description:
            'US citizens can visit the UK for tourism purposes without a visa for up to 6 months.',
        maxStayDuration: 180,
        requiredDocuments: [
          'Valid passport',
          'Return flight ticket',
          'Proof of accommodation',
          'Proof of sufficient funds',
        ],
        lastUpdated: DateTime.now().subtract(const Duration(days: 60)),
      ),
      VisaRequirement(
        id: '3',
        countryFrom: 'US',
        countryTo: 'IN',
        requirementType: VisaRequirementType.eVisa,
        description:
            'US citizens can apply for an e-Visa to visit India for tourism purposes.',
        maxStayDuration: 60,
        processingTime: 3,
        visaFee: 25.0,
        requiredDocuments: [
          'Valid passport',
          'Digital passport-sized photo',
          'Return flight ticket',
          'Hotel reservation',
        ],
        applicationUrl: 'https://indianvisaonline.gov.in/evisa/tvoatml',
        lastUpdated: DateTime.now().subtract(const Duration(days: 45)),
      ),
    ];

    for (final requirement in mockRequirements) {
      final key = '${requirement.countryFrom}_${requirement.countryTo}';
      _requirements[key] = requirement;

      // Cache the requirement
      if (_requirementsBox != null) {
        await _requirementsBox!.put(key, jsonEncode(requirement.toJson()));
      }
    }
  }

  /// Add a new visa requirement
  Future<VisaRequirement> addVisaRequirement(
      VisaRequirement requirement) async {
    await initialize();

    final newRequirement = requirement.copyWith(
      id: const Uuid().v4(),
      lastUpdated: DateTime.now(),
    );

    final key = '${newRequirement.countryFrom}_${newRequirement.countryTo}';
    _requirements[key] = newRequirement;

    // Save to Firestore
    if (_firestore != null) {
      try {
        await _firestore!
            .collection('visa_requirements')
            .doc(newRequirement.id)
            .set(newRequirement.toJson());
      } catch (e, stackTrace) {
        _loggingService.error(
          'VisaRequirementService',
          'Error adding visa requirement to Firestore',
          e,
          stackTrace,
        );
      }
    }

    // Cache the requirement
    if (_requirementsBox != null) {
      try {
        await _requirementsBox!.put(key, jsonEncode(newRequirement.toJson()));
      } catch (e, stackTrace) {
        _loggingService.error(
          'VisaRequirementService',
          'Error caching visa requirement',
          e,
          stackTrace,
        );
      }
    }

    return newRequirement;
  }

  /// Update a visa requirement
  Future<VisaRequirement> updateVisaRequirement(
      VisaRequirement requirement) async {
    await initialize();

    final updatedRequirement = requirement.copyWith(
      lastUpdated: DateTime.now(),
    );

    final key =
        '${updatedRequirement.countryFrom}_${updatedRequirement.countryTo}';
    _requirements[key] = updatedRequirement;

    // Save to Firestore
    if (_firestore != null) {
      try {
        await _firestore!
            .collection('visa_requirements')
            .doc(updatedRequirement.id)
            .update(updatedRequirement.toJson());
      } catch (e, stackTrace) {
        _loggingService.error(
          'VisaRequirementService',
          'Error updating visa requirement in Firestore',
          e,
          stackTrace,
        );
      }
    }

    // Cache the requirement
    if (_requirementsBox != null) {
      try {
        await _requirementsBox!
            .put(key, jsonEncode(updatedRequirement.toJson()));
      } catch (e, stackTrace) {
        _loggingService.error(
          'VisaRequirementService',
          'Error caching updated visa requirement',
          e,
          stackTrace,
        );
      }
    }

    return updatedRequirement;
  }

  /// Delete a visa requirement
  Future<void> deleteVisaRequirement(String id) async {
    await initialize();

    // Find the requirement by ID
    String? keyToDelete;
    for (final entry in _requirements.entries) {
      if (entry.value.id == id) {
        keyToDelete = entry.key;
        break;
      }
    }

    if (keyToDelete == null) {
      throw Exception('Visa requirement with ID $id not found');
    }

    // Remove from memory
    _requirements.remove(keyToDelete);

    // Remove from Firestore
    if (_firestore != null) {
      try {
        await _firestore!.collection('visa_requirements').doc(id).delete();
      } catch (e, stackTrace) {
        _loggingService.error(
          'VisaRequirementService',
          'Error deleting visa requirement from Firestore',
          e,
          stackTrace,
        );
      }
    }

    // Remove from cache
    if (_requirementsBox != null) {
      try {
        await _requirementsBox!.delete(keyToDelete);
      } catch (e, stackTrace) {
        _loggingService.error(
          'VisaRequirementService',
          'Error removing visa requirement from cache',
          e,
          stackTrace,
        );
      }
    }
  }
}
