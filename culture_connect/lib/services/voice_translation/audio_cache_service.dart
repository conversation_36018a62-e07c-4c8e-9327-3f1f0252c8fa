import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:culture_connect/services/logging_service.dart';

/// A service for caching audio files with LRU (Least Recently Used) strategy
class AudioCacheService {
  /// The maximum size of the cache in bytes (default: 100MB)
  final int _maxCacheSize;

  /// The current size of the cache in bytes
  int _currentCacheSize = 0;

  /// The cache directory
  late final Directory _cacheDirectory;

  /// Whether the service is initialized
  bool _isInitialized = false;

  /// The logging service
  final LoggingService _loggingService;

  /// The connectivity service
  final Connectivity _connectivity;

  /// Whether the service is currently online
  bool _isOnline = true;

  /// Subscription to connectivity changes
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  /// The cache entries, ordered by last access time (most recent first)
  final List<_CacheEntry> _cacheEntries = [];

  /// The error stream controller
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();

  /// The cache stats stream controller
  final StreamController<CacheStats> _statsController =
      StreamController<CacheStats>.broadcast();

  /// Creates a new audio cache service
  AudioCacheService(this._loggingService, this._connectivity,
      {int? maxCacheSize})
      : _maxCacheSize = maxCacheSize ?? 100 * 1024 * 1024 {
    // 100MB default
    _initConnectivity();
  }

  /// The error stream
  Stream<String> get errorStream => _errorController.stream;

  /// The cache stats stream
  Stream<CacheStats> get statsStream => _statsController.stream;

  /// Whether the service is currently online
  bool get isOnline => _isOnline;

  /// Initialize connectivity monitoring
  void _initConnectivity() {
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
    // Check initial connection state
    _checkConnectivity();
  }

  /// Check current connectivity
  Future<void> _checkConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
    } on Exception catch (e) {
      debugPrint('Error checking connectivity: $e');
      _loggingService.error(
          'AudioCacheService', 'Error checking connectivity: $e');
      _isOnline = false;
    }
  }

  /// Update connection status based on connectivity result
  void _updateConnectionStatus(ConnectivityResult result) {
    _isOnline = result != ConnectivityResult.none;
    _loggingService.info('AudioCacheService',
        'Connectivity changed: ${_isOnline ? 'Online' : 'Offline'}');
  }

  /// Initialize the audio cache service
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      // Get the cache directory
      final tempDir = await getTemporaryDirectory();
      _cacheDirectory = Directory('${tempDir.path}/audio_cache');

      // Create the cache directory if it doesn't exist
      if (!await _cacheDirectory.exists()) {
        await _cacheDirectory.create(recursive: true);
      }

      // Load existing cache entries
      await _loadExistingCache();

      _isInitialized = true;
      _loggingService.info(
          'AudioCacheService', 'Audio cache service initialized');

      // Publish initial stats
      _publishStats();

      return true;
    } catch (e) {
      final errorMsg = 'Error initializing audio cache service: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      _loggingService.error('AudioCacheService', errorMsg);
      return false;
    }
  }

  /// Load existing cache entries from the cache directory
  Future<void> _loadExistingCache() async {
    try {
      final files = await _cacheDirectory.list().toList();

      for (final entity in files) {
        if (entity is File) {
          final stat = await entity.stat();
          final size = stat.size;
          final lastAccessed = stat.accessed;

          _cacheEntries.add(_CacheEntry(
            key: entity.path.split('/').last,
            path: entity.path,
            size: size,
            lastAccessed: lastAccessed,
          ));

          _currentCacheSize += size;
        }
      }

      // Sort by last accessed time (most recent first)
      _cacheEntries.sort((a, b) => b.lastAccessed.compareTo(a.lastAccessed));

      _loggingService.info('AudioCacheService',
          'Loaded ${_cacheEntries.length} cache entries, total size: ${_currentCacheSize ~/ 1024}KB');
    } catch (e) {
      final errorMsg = 'Error loading existing cache: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      _loggingService.error('AudioCacheService', errorMsg);
    }
  }

  /// Get a file from the cache
  Future<File?> getFile(String key) async {
    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) return null;
    }

    try {
      // Check if the file exists in the cache
      final index = _cacheEntries.indexWhere((entry) => entry.key == key);

      if (index >= 0) {
        final entry = _cacheEntries[index];
        final file = File(entry.path);

        if (await file.exists()) {
          // Update last accessed time
          _cacheEntries[index] = entry.copyWith(lastAccessed: DateTime.now());

          // Move to the front of the list (most recently used)
          if (index > 0) {
            _cacheEntries.removeAt(index);
            _cacheEntries.insert(0, entry);
          }

          _loggingService.info('AudioCacheService', 'Cache hit: $key');
          return file;
        } else {
          // File doesn't exist, remove from cache
          _cacheEntries.removeAt(index);
          _currentCacheSize -= entry.size;
          _loggingService.info(
              'AudioCacheService', 'Cache entry exists but file missing: $key');
        }
      } else {
        _loggingService.info('AudioCacheService', 'Cache miss: $key');
      }

      return null;
    } catch (e) {
      final errorMsg = 'Error getting file from cache: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      _loggingService.error('AudioCacheService', errorMsg);
      return null;
    }
  }

  /// Put a file in the cache
  Future<File?> putFile(String key, List<int> data) async {
    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) return null;
    }

    try {
      // Check if the file already exists in the cache
      final existingIndex =
          _cacheEntries.indexWhere((entry) => entry.key == key);

      if (existingIndex >= 0) {
        // Remove the existing entry
        final existingEntry = _cacheEntries[existingIndex];
        _cacheEntries.removeAt(existingIndex);
        _currentCacheSize -= existingEntry.size;

        // Delete the existing file
        final existingFile = File(existingEntry.path);
        if (await existingFile.exists()) {
          await existingFile.delete();
        }
      }

      // Make room for the new file if needed
      final newSize = data.length;
      await _ensureSpaceAvailable(newSize);

      // Create the new file
      final path = '${_cacheDirectory.path}/$key';
      final file = File(path);
      await file.writeAsBytes(data);

      // Add to cache
      final entry = _CacheEntry(
        key: key,
        path: path,
        size: newSize,
        lastAccessed: DateTime.now(),
      );

      _cacheEntries.insert(0, entry); // Add to front (most recently used)
      _currentCacheSize += newSize;

      _loggingService.info('AudioCacheService',
          'Added to cache: $key, size: ${newSize ~/ 1024}KB');

      // Publish updated stats
      _publishStats();

      return file;
    } catch (e) {
      final errorMsg = 'Error putting file in cache: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      _loggingService.error('AudioCacheService', errorMsg);
      return null;
    }
  }

  /// Ensure there is enough space available in the cache
  Future<void> _ensureSpaceAvailable(int requiredSize) async {
    // If the required size is larger than the max cache size, we can't cache it
    if (requiredSize > _maxCacheSize) {
      throw Exception(
          'File size ($requiredSize bytes) exceeds maximum cache size ($_maxCacheSize bytes)');
    }

    // If there's already enough space, we're done
    if (_currentCacheSize + requiredSize <= _maxCacheSize) {
      return;
    }

    // Remove least recently used entries until we have enough space
    while (_cacheEntries.isNotEmpty &&
        _currentCacheSize + requiredSize > _maxCacheSize) {
      final entry = _cacheEntries.removeLast(); // Remove least recently used

      // Delete the file
      final file = File(entry.path);
      if (await file.exists()) {
        await file.delete();
      }

      _currentCacheSize -= entry.size;
      _loggingService.info('AudioCacheService',
          'Removed from cache: ${entry.key}, size: ${entry.size ~/ 1024}KB');
    }
  }

  /// Clear the cache
  Future<void> clearCache() async {
    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) return;
    }

    try {
      // Delete all files in the cache directory
      final files = await _cacheDirectory.list().toList();

      for (final entity in files) {
        if (entity is File) {
          await entity.delete();
        }
      }

      // Clear cache entries
      _cacheEntries.clear();
      _currentCacheSize = 0;

      _loggingService.info('AudioCacheService', 'Cache cleared');

      // Publish updated stats
      _publishStats();
    } catch (e) {
      final errorMsg = 'Error clearing cache: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      _loggingService.error('AudioCacheService', errorMsg);
    }
  }

  /// Get cache statistics
  CacheStats getCacheStats() {
    return CacheStats(
      entryCount: _cacheEntries.length,
      currentSize: _currentCacheSize,
      maxSize: _maxCacheSize,
      usagePercentage:
          _maxCacheSize > 0 ? (_currentCacheSize / _maxCacheSize) * 100 : 0,
    );
  }

  /// Publish cache statistics
  void _publishStats() {
    _statsController.add(getCacheStats());
  }

  /// Dispose resources
  Future<void> dispose() async {
    await _connectivitySubscription?.cancel();
    await _errorController.close();
    await _statsController.close();
    _loggingService.info('AudioCacheService', 'Audio cache service disposed');
  }
}

/// A cache entry
class _CacheEntry {
  /// The cache key
  final String key;

  /// The file path
  final String path;

  /// The file size in bytes
  final int size;

  /// The last accessed time
  final DateTime lastAccessed;

  /// Creates a new cache entry
  const _CacheEntry({
    required this.key,
    required this.path,
    required this.size,
    required this.lastAccessed,
  });

  /// Creates a copy with the given fields replaced
  _CacheEntry copyWith({
    String? key,
    String? path,
    int? size,
    DateTime? lastAccessed,
  }) {
    return _CacheEntry(
      key: key ?? this.key,
      path: path ?? this.path,
      size: size ?? this.size,
      lastAccessed: lastAccessed ?? this.lastAccessed,
    );
  }
}

/// Cache statistics
class CacheStats {
  /// The number of entries in the cache
  final int entryCount;

  /// The current size of the cache in bytes
  final int currentSize;

  /// The maximum size of the cache in bytes
  final int maxSize;

  /// The usage percentage (0-100)
  final double usagePercentage;

  /// Creates new cache statistics
  const CacheStats({
    required this.entryCount,
    required this.currentSize,
    required this.maxSize,
    required this.usagePercentage,
  });
}

/// Provider for the audio cache service
final audioCacheServiceProvider = Provider<AudioCacheService>((ref) {
  final loggingService = LoggingService();
  final connectivity = Connectivity();

  final service = AudioCacheService(loggingService, connectivity);

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
