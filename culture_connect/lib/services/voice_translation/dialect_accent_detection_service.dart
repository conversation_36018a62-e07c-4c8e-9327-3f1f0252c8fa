import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/models/translation/accent_model.dart';
import 'package:culture_connect/models/translation/dialect_model.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/services/voice_translation/language_detection_service.dart';

/// A service for detecting dialects and accents
class DialectAccentDetectionService {
  /// The shared preferences instance
  final SharedPreferences _prefs;

  /// The language detection service
  final LanguageDetectionService _languageDetectionService;

  /// The dialect detection events controller
  final StreamController<String> _detectionEventsController =
      StreamController<String>.broadcast();

  /// The dialect detection cache
  final Map<String, DialectDetectionResult> _dialectDetectionCache = {};

  /// The accent detection cache
  final Map<String, AccentDetectionResult> _accentDetectionCache = {};

  /// Whether to use dialect detection
  bool _useDialectDetection = true;

  /// Whether to use accent detection
  bool _useAccentDetection = true;

  /// The confidence threshold for dialect detection
  double _dialectConfidenceThreshold = 0.6;

  /// The confidence threshold for accent detection
  double _accentConfidenceThreshold = 0.5;

  /// The user's preferred dialects by language
  final Map<String, String> _preferredDialects = {};

  /// The user's preferred accents by dialect
  final Map<String, String> _preferredAccents = {};

  /// Creates a new dialect accent detection service
  DialectAccentDetectionService(this._prefs, this._languageDetectionService) {
    _loadPreferences();
  }

  /// Load preferences from shared preferences
  Future<void> _loadPreferences() async {
    try {
      _useDialectDetection = _prefs.getBool('use_dialect_detection') ?? true;
      _useAccentDetection = _prefs.getBool('use_accent_detection') ?? true;
      _dialectConfidenceThreshold =
          _prefs.getDouble('dialect_confidence_threshold') ?? 0.6;
      _accentConfidenceThreshold =
          _prefs.getDouble('accent_confidence_threshold') ?? 0.5;

      // Load preferred dialects
      final preferredDialectsJson = _prefs.getString('preferred_dialects');
      if (preferredDialectsJson != null) {
        final Map<String, dynamic> data = jsonDecode(preferredDialectsJson);
        data.forEach((key, value) {
          _preferredDialects[key] = value.toString();
        });
      }

      // Load preferred accents
      final preferredAccentsJson = _prefs.getString('preferred_accents');
      if (preferredAccentsJson != null) {
        final Map<String, dynamic> data = jsonDecode(preferredAccentsJson);
        data.forEach((key, value) {
          _preferredAccents[key] = value.toString();
        });
      }

      // Load detection caches
      final dialectCacheJson = _prefs.getString('dialect_detection_cache');
      if (dialectCacheJson != null) {
        final Map<String, dynamic> data = jsonDecode(dialectCacheJson);
        data.forEach((key, value) {
          _dialectDetectionCache[key] = DialectDetectionResult.fromJson(value);
        });
      }

      final accentCacheJson = _prefs.getString('accent_detection_cache');
      if (accentCacheJson != null) {
        final Map<String, dynamic> data = jsonDecode(accentCacheJson);
        data.forEach((key, value) {
          _accentDetectionCache[key] = AccentDetectionResult.fromJson(value);
        });
      }
    } catch (e) {
      debugPrint('Error loading dialect accent detection preferences: $e');
    }
  }

  /// Save preferences to shared preferences
  Future<void> _savePreferences() async {
    try {
      await _prefs.setBool('use_dialect_detection', _useDialectDetection);
      await _prefs.setBool('use_accent_detection', _useAccentDetection);
      await _prefs.setDouble(
          'dialect_confidence_threshold', _dialectConfidenceThreshold);
      await _prefs.setDouble(
          'accent_confidence_threshold', _accentConfidenceThreshold);

      // Save preferred dialects
      await _prefs.setString(
          'preferred_dialects', jsonEncode(_preferredDialects));

      // Save preferred accents
      await _prefs.setString(
          'preferred_accents', jsonEncode(_preferredAccents));

      // Save detection caches (limit to 100 entries each)
      final dialectCacheEntries = _dialectDetectionCache.entries.toList()
        ..sort((a, b) => b.value.confidence.compareTo(a.value.confidence));

      final limitedDialectCache = <String, DialectDetectionResult>{};
      for (var i = 0; i < min(100, dialectCacheEntries.length); i++) {
        limitedDialectCache[dialectCacheEntries[i].key] =
            dialectCacheEntries[i].value;
      }

      await _prefs.setString(
          'dialect_detection_cache',
          jsonEncode(limitedDialectCache
              .map((key, value) => MapEntry(key, value.toJson()))));

      final accentCacheEntries = _accentDetectionCache.entries.toList()
        ..sort((a, b) => b.value.confidence.compareTo(a.value.confidence));

      final limitedAccentCache = <String, AccentDetectionResult>{};
      for (var i = 0; i < min(100, accentCacheEntries.length); i++) {
        limitedAccentCache[accentCacheEntries[i].key] =
            accentCacheEntries[i].value;
      }

      await _prefs.setString(
          'accent_detection_cache',
          jsonEncode(limitedAccentCache
              .map((key, value) => MapEntry(key, value.toJson()))));
    } catch (e) {
      debugPrint('Error saving dialect accent detection preferences: $e');
    }
  }

  /// Set whether to use dialect detection
  Future<void> setUseDialectDetection(bool value) async {
    _useDialectDetection = value;
    await _savePreferences();
    _detectionEventsController
        .add('Dialect detection ${value ? 'enabled' : 'disabled'}');
  }

  /// Set whether to use accent detection
  Future<void> setUseAccentDetection(bool value) async {
    _useAccentDetection = value;
    await _savePreferences();
    _detectionEventsController
        .add('Accent detection ${value ? 'enabled' : 'disabled'}');
  }

  /// Set the confidence threshold for dialect detection
  Future<void> setDialectConfidenceThreshold(double value) async {
    _dialectConfidenceThreshold = value;
    await _savePreferences();
    _detectionEventsController
        .add('Dialect confidence threshold set to ${value.toStringAsFixed(2)}');
  }

  /// Set the confidence threshold for accent detection
  Future<void> setAccentConfidenceThreshold(double value) async {
    _accentConfidenceThreshold = value;
    await _savePreferences();
    _detectionEventsController
        .add('Accent confidence threshold set to ${value.toStringAsFixed(2)}');
  }

  /// Set the preferred dialect for a language
  Future<void> setPreferredDialect(
      String languageCode, String dialectCode) async {
    _preferredDialects[languageCode] = dialectCode;
    await _savePreferences();
    _detectionEventsController
        .add('Preferred dialect for $languageCode set to $dialectCode');
  }

  /// Set the preferred accent for a dialect
  Future<void> setPreferredAccent(String dialectCode, String accentCode) async {
    _preferredAccents[dialectCode] = accentCode;
    await _savePreferences();
    _detectionEventsController
        .add('Preferred accent for $dialectCode set to $accentCode');
  }

  /// Get the preferred dialect for a language
  String? getPreferredDialect(String languageCode) {
    return _preferredDialects[languageCode];
  }

  /// Get the preferred accent for a dialect
  String? getPreferredAccent(String dialectCode) {
    return _preferredAccents[dialectCode];
  }

  /// Get whether dialect detection is enabled
  bool get useDialectDetection => _useDialectDetection;

  /// Get whether accent detection is enabled
  bool get useAccentDetection => _useAccentDetection;

  /// Get the dialect detection events stream
  Stream<String> get detectionEventsStream => _detectionEventsController.stream;

  /// Detect the dialect of a text
  Future<DialectDetectionResult?> detectDialect(
      String text, String languageCode) async {
    try {
      if (!_useDialectDetection || text.isEmpty) {
        return null;
      }

      // Check if there's a preferred dialect for this language
      final preferredDialect = _preferredDialects[languageCode];
      if (preferredDialect != null) {
        // Find the dialect model
        final language = supportedLanguages.firstWhere(
          (lang) => lang.code == languageCode,
          orElse: () => throw Exception('Language not found: $languageCode'),
        );

        final dialect = language.dialects.firstWhere(
          (d) => d.code == preferredDialect,
          orElse: () => throw Exception('Dialect not found: $preferredDialect'),
        );

        // Return the preferred dialect with high confidence
        return DialectDetectionResult(
          dialect: dialect,
          confidence: 1.0,
          detectedTerms: [],
        );
      }

      // Create a cache key
      final cacheKey = '${languageCode}_${text.hashCode}';

      // Check the cache
      if (_dialectDetectionCache.containsKey(cacheKey)) {
        return _dialectDetectionCache[cacheKey];
      }

      // Find the language model
      final language = supportedLanguages.firstWhere(
        (lang) => lang.code == languageCode,
        orElse: () => throw Exception('Language not found: $languageCode'),
      );

      // If the language has no dialects, return null
      if (language.dialects.isEmpty) {
        return null;
      }

      // Normalize the text
      final normalizedText = text.toLowerCase();

      // Calculate scores for each dialect
      final scores = <DialectModel, double>{};
      final detectedTerms = <DialectModel, List<String>>{};

      for (final dialect in language.dialects) {
        double score = 0.0;
        final terms = <String>[];

        // Check for dialect-specific terms
        for (final term in dialect.dialectSpecificTerms) {
          if (normalizedText.contains(term)) {
            score += 0.1;
            terms.add(term);
          }
        }

        // If we found terms, add this dialect to the scores
        if (score > 0) {
          scores[dialect] = score;
          detectedTerms[dialect] = terms;
        }
      }

      // If no scores, try to use the default dialect for the language
      if (scores.isEmpty && language.defaultDialectCode != null) {
        final defaultDialect = language.dialects.firstWhere(
          (d) => d.code == language.defaultDialectCode,
          orElse: () => language.dialects.first,
        );

        final result = DialectDetectionResult(
          dialect: defaultDialect,
          confidence: 0.5, // Medium confidence since it's the default
          detectedTerms: [],
        );

        // Cache the result
        _dialectDetectionCache[cacheKey] = result;
        await _savePreferences();

        _detectionEventsController
            .add('Detected dialect: ${defaultDialect.name} (default)');

        return result;
      }

      // Get the dialect with the highest score
      if (scores.isNotEmpty) {
        final entries = scores.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));

        final topDialect = entries.first;

        // Check if the confidence is high enough
        if (topDialect.value >= _dialectConfidenceThreshold) {
          final result = DialectDetectionResult(
            dialect: topDialect.key,
            confidence: topDialect.value,
            detectedTerms: detectedTerms[topDialect.key] ?? [],
          );

          // Cache the result
          _dialectDetectionCache[cacheKey] = result;
          await _savePreferences();

          _detectionEventsController.add(
            'Detected dialect: ${topDialect.key.name} (confidence: ${topDialect.value.toStringAsFixed(2)})',
          );

          return result;
        }
      }

      // If no dialect was detected with high confidence, use the default
      if (language.defaultDialectCode != null) {
        final defaultDialect = language.dialects.firstWhere(
          (d) => d.code == language.defaultDialectCode,
          orElse: () => language.dialects.first,
        );

        final result = DialectDetectionResult(
          dialect: defaultDialect,
          confidence: 0.5, // Medium confidence since it's the default
          detectedTerms: [],
        );

        // Cache the result
        _dialectDetectionCache[cacheKey] = result;
        await _savePreferences();

        _detectionEventsController
            .add('Detected dialect: ${defaultDialect.name} (default)');

        return result;
      }

      return null;
    } catch (e) {
      debugPrint('Error detecting dialect: $e');
      return null;
    }
  }

  /// Detect the accent from audio
  Future<AccentDetectionResult?> detectAccentFromAudio(
      String audioPath, String dialectCode) async {
    try {
      if (!_useAccentDetection || audioPath.isEmpty) {
        return null;
      }

      // Check if there's a preferred accent for this dialect
      final preferredAccent = _preferredAccents[dialectCode];
      if (preferredAccent != null) {
        // Find the dialect model
        DialectModel? dialect;
        for (final language in supportedLanguages) {
          for (final d in language.dialects) {
            if (d.code == dialectCode) {
              dialect = d;
              break;
            }
          }
          if (dialect != null) break;
        }

        if (dialect == null) {
          throw Exception('Dialect not found: $dialectCode');
        }

        // Create a mock accent model (in a real app, this would be fetched from a database)
        final accent = AccentModel(
          code: preferredAccent,
          name: _getAccentName(preferredAccent),
          region: dialect.region,
          dialectCode: dialectCode,
          difficulty: AccentDifficulty.moderate,
          characteristics: [
            const AccentCharacteristic(
              type: AccentCharacteristicType.vowelSounds,
              description: 'Distinctive vowel sounds',
              examples: ['example1', 'example2'],
            ),
            const AccentCharacteristic(
              type: AccentCharacteristicType.intonation,
              description: 'Unique intonation patterns',
              examples: ['example1', 'example2'],
            ),
          ],
        );

        // Return the preferred accent with high confidence
        return AccentDetectionResult(
          accent: accent,
          confidence: 1.0,
          detectedCharacteristics: accent.characteristics,
        );
      }

      // Create a cache key
      final cacheKey = '${dialectCode}_${audioPath.hashCode}';

      // Check the cache
      if (_accentDetectionCache.containsKey(cacheKey)) {
        return _accentDetectionCache[cacheKey];
      }

      // In a real app, this would use audio analysis to detect the accent
      // For demo purposes, we'll return a mock result

      // Find the dialect
      DialectModel? dialect;
      for (final language in supportedLanguages) {
        for (final d in language.dialects) {
          if (d.code == dialectCode) {
            dialect = d;
            break;
          }
        }
        if (dialect != null) break;
      }

      if (dialect == null) {
        throw Exception('Dialect not found: $dialectCode');
      }

      // Get a random accent variant
      final accentVariants = dialect.accentVariants;
      if (accentVariants.isEmpty) {
        return null;
      }

      final accentCode =
          accentVariants[Random().nextInt(accentVariants.length)];

      // Create a mock accent model
      final accent = AccentModel(
        code: accentCode,
        name: _getAccentName(accentCode),
        region: 'Unknown Region',
        dialectCode: dialectCode,
        difficulty: AccentDifficulty
            .values[Random().nextInt(AccentDifficulty.values.length)],
        characteristics: [
          const AccentCharacteristic(
            type: AccentCharacteristicType.vowelSounds,
            description: 'Distinctive vowel sounds',
            examples: ['example1', 'example2'],
          ),
          const AccentCharacteristic(
            type: AccentCharacteristicType.intonation,
            description: 'Unique intonation patterns',
            examples: ['example1', 'example2'],
          ),
        ],
      );

      // Generate a random confidence score
      final confidence =
          0.6 + (Random().nextDouble() * 0.4); // Between 0.6 and 1.0

      // Create the result
      final result = AccentDetectionResult(
        accent: accent,
        confidence: confidence,
        detectedCharacteristics: accent.characteristics,
      );

      // Cache the result
      _accentDetectionCache[cacheKey] = result;
      await _savePreferences();

      _detectionEventsController.add(
        'Detected accent: ${accent.name} (confidence: ${confidence.toStringAsFixed(2)})',
      );

      return result;
    } catch (e) {
      debugPrint('Error detecting accent: $e');
      return null;
    }
  }

  /// Get a human-readable name for an accent code
  String _getAccentName(String accentCode) {
    // Parse the accent code (e.g., 'en-us-southern' -> 'Southern American')
    final parts = accentCode.split('-');
    if (parts.length < 3) {
      return accentCode;
    }

    final languageCode = parts[0];
    final dialectCode = parts[1];
    final accentName = parts.sublist(2).join('-');

    // Capitalize the accent name
    final capitalizedAccentName = accentName.split('-').map((part) {
      return part.isNotEmpty ? part[0].toUpperCase() + part.substring(1) : '';
    }).join(' ');

    // Get the dialect name
    String dialectName = '';
    for (final language in supportedLanguages) {
      if (language.code == languageCode) {
        for (final dialect in language.dialects) {
          if (dialect.code == '$languageCode-$dialectCode') {
            dialectName = dialect.name.split(' ').last;
            break;
          }
        }
        break;
      }
    }

    if (dialectName.isNotEmpty) {
      return '$capitalizedAccentName $dialectName';
    } else {
      return capitalizedAccentName;
    }
  }

  /// Clear the detection caches
  Future<void> clearCaches() async {
    _dialectDetectionCache.clear();
    _accentDetectionCache.clear();
    await _savePreferences();
    _detectionEventsController.add('Detection caches cleared');
  }

  /// Dispose the service
  Future<void> dispose() async {
    await _detectionEventsController.close();
  }
}

/// Provider for the dialect accent detection service
final dialectAccentDetectionServiceProvider =
    Provider<DialectAccentDetectionService>((ref) {
  final prefs = ref.read(sharedPreferencesProvider);
  final languageDetectionService = ref.read(languageDetectionServiceProvider);

  final service =
      DialectAccentDetectionService(prefs, languageDetectionService);

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
