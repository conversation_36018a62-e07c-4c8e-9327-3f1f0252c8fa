import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/models/translation/language_pack_model.dart';
import 'package:culture_connect/services/voice_translation/voice_translation_service.dart';

/// A service for managing language packs
class LanguagePackService {
  /// The API key for the translation service
  final String _apiKey;

  /// The HTTP client
  final http.Client _client;

  /// The shared preferences instance
  final SharedPreferences _prefs;

  /// The base URL for the language pack API
  final String _baseUrl = 'https://api.example.com/language-packs';

  /// The downloaded language packs
  List<LanguagePackModel> _languagePacks = [];

  /// The language packs stream controller
  final StreamController<List<LanguagePackModel>> _languagePacksController =
      StreamController<List<LanguagePackModel>>.broadcast();

  /// The download progress stream controllers
  final Map<String, StreamController<double>> _downloadProgressControllers = {};

  /// Creates a new language pack service
  LanguagePackService(
    this._apiKey,
    this._client,
    this._prefs,
  ) {
    _loadLanguagePacks();
  }

  /// Load language packs from shared preferences
  Future<void> _loadLanguagePacks() async {
    try {
      final packsJson = _prefs.getString('language_packs');
      if (packsJson != null) {
        final List<dynamic> decoded = jsonDecode(packsJson);
        _languagePacks =
            decoded.map((item) => LanguagePackModel.fromJson(item)).toList();

        // Notify listeners
        _languagePacksController.add(_languagePacks);
      }
    } catch (e) {
      debugPrint('Error loading language packs: $e');
    }
  }

  /// Save language packs to shared preferences
  Future<void> _saveLanguagePacks() async {
    try {
      final packsJson =
          jsonEncode(_languagePacks.map((item) => item.toJson()).toList());
      await _prefs.setString('language_packs', packsJson);
    } catch (e) {
      debugPrint('Error saving language packs: $e');
    }
  }

  /// Get available language packs from the server
  Future<List<LanguageModel>> getAvailableLanguagePacks() async {
    try {
      // In a real app, this would fetch from the server
      // For now, we'll return the supported languages
      return supportedLanguages;
    } catch (e) {
      debugPrint('Error getting available language packs: $e');
      return [];
    }
  }

  /// Download a language pack
  Future<LanguagePackModel?> downloadLanguagePack(String languageCode) async {
    try {
      // Check if already downloaded
      final existingPack = _languagePacks.firstWhere(
        (pack) => pack.languageCode == languageCode,
        orElse: () => LanguagePackModel(
          languageCode: languageCode,
          version: '1.0.0',
          sizeMB: 0.0,
          localPath: '',
          downloadedAt: DateTime.now(),
          lastUsedAt: DateTime.now(),
          status: LanguagePackStatus.notDownloaded,
        ),
      );

      if (existingPack.status == LanguagePackStatus.downloaded) {
        return existingPack;
      }

      // Create a progress controller for this download
      final progressController = StreamController<double>.broadcast();
      _downloadProgressControllers[languageCode] = progressController;

      // Update the language pack status to downloading
      final updatedPack = existingPack.copyWith(
        status: LanguagePackStatus.downloading,
        downloadProgress: 0.0,
      );

      // Update the language packs list
      final index = _languagePacks
          .indexWhere((pack) => pack.languageCode == languageCode);
      if (index >= 0) {
        _languagePacks[index] = updatedPack;
      } else {
        _languagePacks.add(updatedPack);
      }

      // Notify listeners
      _languagePacksController.add(_languagePacks);
      await _saveLanguagePacks();

      // Get the language model
      final language = supportedLanguages.firstWhere(
        (lang) => lang.code == languageCode,
        orElse: () => throw Exception('Language not found: $languageCode'),
      );

      // Simulate download with progress updates
      for (int i = 1; i <= 10; i++) {
        await Future.delayed(const Duration(milliseconds: 500));
        final progress = i / 10;
        progressController.add(progress);

        // Update the language pack progress
        final updatedPack2 = updatedPack.copyWith(
          downloadProgress: progress,
        );

        // Update the language packs list
        final index2 = _languagePacks
            .indexWhere((pack) => pack.languageCode == languageCode);
        if (index2 >= 0) {
          _languagePacks[index2] = updatedPack2;
          _languagePacksController.add(_languagePacks);
          await _saveLanguagePacks();
        }
      }

      // Create a directory for the language pack
      final appDir = await getApplicationDocumentsDirectory();
      final langDir = Directory('${appDir.path}/language_packs/$languageCode');
      if (!await langDir.exists()) {
        await langDir.create(recursive: true);
      }

      // Create a mock file to represent the language pack
      final mockFile = File('${langDir.path}/model.bin');
      await mockFile.writeAsString('Mock language pack data for $languageCode');

      // Update the language pack status to downloaded
      final completedPack = updatedPack.copyWith(
        status: LanguagePackStatus.downloaded,
        downloadProgress: 1.0,
        localPath: langDir.path,
        downloadedAt: DateTime.now(),
        lastUsedAt: DateTime.now(),
        sizeMB: language.downloadSizeMB,
      );

      // Update the language packs list
      final finalIndex = _languagePacks
          .indexWhere((pack) => pack.languageCode == languageCode);
      if (finalIndex >= 0) {
        _languagePacks[finalIndex] = completedPack;
      } else {
        _languagePacks.add(completedPack);
      }

      // Notify listeners
      _languagePacksController.add(_languagePacks);
      await _saveLanguagePacks();

      // Close the progress controller
      await progressController.close();
      _downloadProgressControllers.remove(languageCode);

      return completedPack;
    } catch (e) {
      debugPrint('Error downloading language pack: $e');

      // Update the language pack status to error
      final index = _languagePacks
          .indexWhere((pack) => pack.languageCode == languageCode);
      if (index >= 0) {
        final errorPack = _languagePacks[index].copyWith(
          status: LanguagePackStatus.error,
        );
        _languagePacks[index] = errorPack;
        _languagePacksController.add(_languagePacks);
        await _saveLanguagePacks();
      }

      // Close the progress controller
      final progressController = _downloadProgressControllers[languageCode];
      if (progressController != null) {
        await progressController.close();
        _downloadProgressControllers.remove(languageCode);
      }

      return null;
    }
  }

  /// Delete a language pack
  Future<bool> deleteLanguagePack(String languageCode) async {
    try {
      // Find the language pack
      final index = _languagePacks
          .indexWhere((pack) => pack.languageCode == languageCode);
      if (index < 0) {
        return false;
      }

      final pack = _languagePacks[index];

      // Delete the directory
      if (pack.localPath.isNotEmpty) {
        final dir = Directory(pack.localPath);
        if (await dir.exists()) {
          await dir.delete(recursive: true);
        }
      }

      // Remove from the list
      _languagePacks.removeAt(index);

      // Notify listeners
      _languagePacksController.add(_languagePacks);
      await _saveLanguagePacks();

      return true;
    } catch (e) {
      debugPrint('Error deleting language pack: $e');
      return false;
    }
  }

  /// Set the primary language
  Future<void> setPrimaryLanguage(String languageCode) async {
    try {
      // Update all language packs to not be primary
      for (int i = 0; i < _languagePacks.length; i++) {
        _languagePacks[i] = _languagePacks[i].copyWith(isPrimary: false);
      }

      // Set the specified language as primary
      final index = _languagePacks
          .indexWhere((pack) => pack.languageCode == languageCode);
      if (index >= 0) {
        _languagePacks[index] = _languagePacks[index].copyWith(isPrimary: true);
      }

      // Notify listeners
      _languagePacksController.add(_languagePacks);
      await _saveLanguagePacks();
    } catch (e) {
      debugPrint('Error setting primary language: $e');
    }
  }

  /// Set the selected dialect for a language
  Future<void> setSelectedDialect(String languageCode, String dialect) async {
    try {
      // Find the language pack
      final index = _languagePacks
          .indexWhere((pack) => pack.languageCode == languageCode);
      if (index < 0) {
        return;
      }

      // Update the selected dialect
      _languagePacks[index] =
          _languagePacks[index].copyWith(selectedDialect: dialect);

      // Notify listeners
      _languagePacksController.add(_languagePacks);
      await _saveLanguagePacks();
    } catch (e) {
      debugPrint('Error setting selected dialect: $e');
    }
  }

  /// Check if a language pack is downloaded
  bool isLanguagePackDownloaded(String languageCode) {
    return _languagePacks.any(
      (pack) =>
          pack.languageCode == languageCode &&
          pack.status == LanguagePackStatus.downloaded,
    );
  }

  /// Get a language pack
  LanguagePackModel? getLanguagePack(String languageCode) {
    try {
      return _languagePacks.firstWhere(
        (pack) => pack.languageCode == languageCode,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get the primary language pack
  LanguagePackModel? getPrimaryLanguagePack() {
    try {
      return _languagePacks.firstWhere(
        (pack) => pack.isPrimary,
      );
    } catch (e) {
      return _languagePacks.isNotEmpty ? _languagePacks.first : null;
    }
  }

  /// Get the download progress stream for a language
  Stream<double>? getDownloadProgressStream(String languageCode) {
    return _downloadProgressControllers[languageCode]?.stream;
  }

  /// Get the language packs stream
  Stream<List<LanguagePackModel>> get languagePacksStream =>
      _languagePacksController.stream;

  /// Get the downloaded language packs
  List<LanguagePackModel> get languagePacks => _languagePacks;

  /// Dispose the service
  Future<void> dispose() async {
    await _languagePacksController.close();

    // Close all progress controllers
    for (final controller in _downloadProgressControllers.values) {
      await controller.close();
    }
    _downloadProgressControllers.clear();
  }
}

/// Provider for the language pack service
final languagePackServiceProvider = Provider<LanguagePackService>((ref) {
  const apiKey =
      'mock_api_key'; // In a real app, this would come from environment variables
  final client = http.Client();
  final prefs = ref.watch(sharedPreferencesProvider);

  final service = LanguagePackService(
    apiKey,
    client,
    prefs,
  );

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
