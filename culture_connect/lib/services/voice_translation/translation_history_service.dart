import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:culture_connect/models/translation/translation_history_entry.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/services/logging_service.dart';

/// A service for managing translation history
class TranslationHistoryService {
  /// The shared preferences instance
  final SharedPreferences _prefs;

  /// The logging service
  final LoggingService _loggingService;

  /// The connectivity service
  final Connectivity _connectivity;

  /// Whether the service is currently online
  bool _isOnline = true;

  /// Subscription to connectivity changes
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  /// The translation history entries
  final List<TranslationHistoryEntry> _historyEntries = [];

  /// The history stream controller
  final StreamController<List<TranslationHistoryEntry>> _historyController =
      StreamController<List<TranslationHistoryEntry>>.broadcast();

  /// The error stream controller
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();

  /// The maximum number of history entries to keep
  final int _maxHistoryEntries;

  /// Whether to sync history with the cloud
  bool _syncWithCloud = false;

  /// Creates a new translation history service
  TranslationHistoryService(
    this._prefs,
    this._loggingService,
    this._connectivity, {
    int? maxHistoryEntries,
  }) : _maxHistoryEntries = maxHistoryEntries ?? 100 {
    _loadHistory();
    _initConnectivity();
  }

  /// The history stream
  Stream<List<TranslationHistoryEntry>> get historyStream =>
      _historyController.stream;

  /// The error stream
  Stream<String> get errorStream => _errorController.stream;

  /// Whether the service is currently online
  bool get isOnline => _isOnline;

  /// Whether to sync history with the cloud
  bool get syncWithCloud => _syncWithCloud;

  /// Initialize connectivity monitoring
  void _initConnectivity() {
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
    // Check initial connection state
    _checkConnectivity();
  }

  /// Check current connectivity
  Future<void> _checkConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
    } on Exception catch (e) {
      debugPrint('Error checking connectivity: $e');
      _loggingService.error(
          'TranslationHistoryService', 'Error checking connectivity: $e');
      _isOnline = false;
    }
  }

  /// Update connection status based on connectivity result
  void _updateConnectionStatus(ConnectivityResult result) {
    _isOnline = result != ConnectivityResult.none;
    _loggingService.info('TranslationHistoryService',
        'Connectivity changed: ${_isOnline ? 'Online' : 'Offline'}');

    // If we're back online and sync is enabled, sync with cloud
    if (_isOnline && _syncWithCloud) {
      _syncWithCloudImpl();
    }
  }

  /// Load history from shared preferences
  Future<void> _loadHistory() async {
    try {
      final String? historyJson = _prefs.getString('translation_history');
      if (historyJson != null) {
        final List<dynamic> decoded = jsonDecode(historyJson);
        _historyEntries.clear();
        for (final item in decoded) {
          _historyEntries.add(TranslationHistoryEntry.fromJson(item));
        }

        // Sort by timestamp (newest first)
        _historyEntries.sort((a, b) => b.timestamp.compareTo(a.timestamp));

        _loggingService.info('TranslationHistoryService',
            'Loaded ${_historyEntries.length} history entries');
      }

      // Load sync setting
      _syncWithCloud = _prefs.getBool('translation_history_sync') ?? false;

      // Notify listeners
      _historyController.add(_historyEntries);
    } catch (e) {
      final errorMsg = 'Error loading translation history: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      _loggingService.error('TranslationHistoryService', errorMsg);
    }
  }

  /// Save history to shared preferences
  Future<bool> _saveHistory() async {
    try {
      final List<Map<String, dynamic>> encoded =
          _historyEntries.map((e) => e.toJson()).toList();
      final String historyJson = jsonEncode(encoded);
      final result = await _prefs.setString('translation_history', historyJson);

      if (result) {
        _loggingService.info('TranslationHistoryService',
            'Saved ${_historyEntries.length} history entries');
      } else {
        _loggingService.error(
            'TranslationHistoryService', 'Failed to save translation history');
        _errorController.add('Failed to save translation history');
      }

      return result;
    } catch (e) {
      final errorMsg = 'Error saving translation history: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      _loggingService.error('TranslationHistoryService', errorMsg);
      return false;
    }
  }

  /// Add a translation to history
  Future<TranslationHistoryEntry> addTranslation({
    required String source,
    String? translation,
    required String sourceLanguageCode,
    required String targetLanguageCode,
    required String sourceLanguageName,
    required String targetLanguageName,
    required TranslationType type,
    TranslationStatus status = TranslationStatus.completed,
    String? category,
    String? sourceAudioPath,
    String? translatedAudioPath,
    String? errorMessage,
  }) async {
    try {
      // Create a new history entry
      final entry = TranslationHistoryEntry(
        id: const Uuid().v4(),
        source: source,
        translation: translation,
        sourceLanguageCode: sourceLanguageCode,
        targetLanguageCode: targetLanguageCode,
        sourceLanguageName: sourceLanguageName,
        targetLanguageName: targetLanguageName,
        type: type,
        status: status,
        timestamp: DateTime.now(),
        category: category,
        sourceAudioPath: sourceAudioPath,
        translatedAudioPath: translatedAudioPath,
        errorMessage: errorMessage,
      );

      // Add to history
      _historyEntries.insert(0, entry);

      // Trim history if needed
      if (_historyEntries.length > _maxHistoryEntries) {
        _historyEntries.removeLast();
      }

      // Save to shared preferences
      await _saveHistory();

      // Notify listeners
      _historyController.add(_historyEntries);

      // Sync with cloud if enabled
      if (_syncWithCloud && _isOnline) {
        _syncWithCloudImpl();
      }

      _loggingService.info('TranslationHistoryService',
          'Added translation to history: ${entry.id}');

      return entry;
    } catch (e) {
      final errorMsg = 'Error adding translation to history: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      _loggingService.error('TranslationHistoryService', errorMsg);
      rethrow;
    }
  }

  /// Update a translation in history
  Future<TranslationHistoryEntry?> updateTranslation(
    String id, {
    String? translation,
    TranslationStatus? status,
    bool? isFavorite,
    String? category,
    String? translatedAudioPath,
    String? errorMessage,
  }) async {
    try {
      // Find the entry
      final index = _historyEntries.indexWhere((e) => e.id == id);
      if (index < 0) {
        _loggingService.error(
            'TranslationHistoryService', 'Translation not found: $id');
        _errorController.add('Translation not found: $id');
        return null;
      }

      // Update the entry
      final entry = _historyEntries[index];
      final updatedEntry = entry.copyWith(
        translation: translation,
        status: status,
        isFavorite: isFavorite,
        category: category,
        translatedAudioPath: translatedAudioPath,
        errorMessage: errorMessage,
      );

      _historyEntries[index] = updatedEntry;

      // Save to shared preferences
      await _saveHistory();

      // Notify listeners
      _historyController.add(_historyEntries);

      // Sync with cloud if enabled
      if (_syncWithCloud && _isOnline) {
        _syncWithCloudImpl();
      }

      _loggingService.info(
          'TranslationHistoryService', 'Updated translation in history: $id');

      return updatedEntry;
    } catch (e) {
      final errorMsg = 'Error updating translation in history: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      _loggingService.error('TranslationHistoryService', errorMsg);
      return null;
    }
  }

  /// Remove a translation from history
  Future<bool> removeTranslation(String id) async {
    try {
      // Find the entry
      final index = _historyEntries.indexWhere((e) => e.id == id);
      if (index < 0) {
        _loggingService.error(
            'TranslationHistoryService', 'Translation not found: $id');
        _errorController.add('Translation not found: $id');
        return false;
      }

      // Remove the entry
      _historyEntries.removeAt(index);

      // Save to shared preferences
      await _saveHistory();

      // Notify listeners
      _historyController.add(_historyEntries);

      // Sync with cloud if enabled
      if (_syncWithCloud && _isOnline) {
        _syncWithCloudImpl();
      }

      _loggingService.info(
          'TranslationHistoryService', 'Removed translation from history: $id');

      return true;
    } catch (e) {
      final errorMsg = 'Error removing translation from history: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      _loggingService.error('TranslationHistoryService', errorMsg);
      return false;
    }
  }

  /// Clear all history
  Future<bool> clearHistory() async {
    try {
      // Clear history
      _historyEntries.clear();

      // Save to shared preferences
      await _saveHistory();

      // Notify listeners
      _historyController.add(_historyEntries);

      // Sync with cloud if enabled
      if (_syncWithCloud && _isOnline) {
        _syncWithCloudImpl();
      }

      _loggingService.info(
          'TranslationHistoryService', 'Cleared translation history');

      return true;
    } catch (e) {
      final errorMsg = 'Error clearing translation history: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      _loggingService.error('TranslationHistoryService', errorMsg);
      return false;
    }
  }

  /// Get all history entries
  List<TranslationHistoryEntry> getAllEntries() {
    return List.unmodifiable(_historyEntries);
  }

  /// Get favorite history entries
  List<TranslationHistoryEntry> getFavoriteEntries() {
    return _historyEntries.where((e) => e.isFavorite).toList();
  }

  /// Get history entries by category
  List<TranslationHistoryEntry> getEntriesByCategory(String category) {
    return _historyEntries.where((e) => e.category == category).toList();
  }

  /// Get history entries by type
  List<TranslationHistoryEntry> getEntriesByType(TranslationType type) {
    return _historyEntries.where((e) => e.type == type).toList();
  }

  /// Set whether to sync history with the cloud
  Future<bool> setSyncWithCloud(bool value) async {
    try {
      _syncWithCloud = value;
      final result = await _prefs.setBool('translation_history_sync', value);

      if (result) {
        _loggingService.info(
            'TranslationHistoryService', 'Set sync with cloud: $value');

        // If sync is enabled and we're online, sync with cloud
        if (_syncWithCloud && _isOnline) {
          _syncWithCloudImpl();
        }
      } else {
        _loggingService.error(
            'TranslationHistoryService', 'Failed to set sync with cloud');
        _errorController.add('Failed to set sync with cloud');
      }

      return result;
    } catch (e) {
      final errorMsg = 'Error setting sync with cloud: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      _loggingService.error('TranslationHistoryService', errorMsg);
      return false;
    }
  }

  /// Sync history with the cloud (mock implementation)
  Future<void> _syncWithCloudImpl() async {
    // This is a mock implementation for demo purposes
    // In a real app, this would sync with a cloud service

    _loggingService.info(
        'TranslationHistoryService', 'Syncing translation history with cloud');

    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    _loggingService.info(
        'TranslationHistoryService', 'Translation history synced with cloud');
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _historyController.close();
    _errorController.close();
    _loggingService.info(
        'TranslationHistoryService', 'Translation history service disposed');
  }
}

/// Provider for the translation history service
final translationHistoryServiceProvider =
    Provider<TranslationHistoryService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final loggingService = LoggingService();
  final connectivity = Connectivity();

  final service =
      TranslationHistoryService(prefs, loggingService, connectivity);

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
