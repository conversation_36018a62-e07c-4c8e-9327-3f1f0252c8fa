/// Custom exceptions for payment operations
class PaymentException implements Exception {
  final String message;
  final String? code;
  final Map<String, dynamic>? details;

  const PaymentException(
    this.message, {
    this.code,
    this.details,
  });

  @override
  String toString() => 'PaymentException: $message';
}

/// Exception for payment API communication errors
class PaymentApiException extends PaymentException {
  final int? statusCode;
  final String? correlationId;
  final bool isNetworkError;

  const PaymentApiException(
    super.message, {
    super.code,
    super.details,
    this.statusCode,
    this.correlationId,
    this.isNetworkError = false,
  });

  @override
  String toString() => 'PaymentApiException: $message (Status: $statusCode, Correlation: $correlationId)';
}

/// Exception for payment provider specific errors
class PaymentProviderException extends PaymentException {
  final String provider;
  final String? providerCode;

  const PaymentProviderException(
    super.message, {
    required this.provider,
    this.providerCode,
    super.code,
    super.details,
  });

  @override
  String toString() => 'PaymentProviderException [$provider]: $message';
}

/// Exception for payment validation errors
class PaymentValidationException extends PaymentException {
  final Map<String, List<String>> fieldErrors;

  const PaymentValidationException(
    super.message, {
    required this.fieldErrors,
    super.code,
    super.details,
  });

  @override
  String toString() => 'PaymentValidationException: $message';
}

/// Exception for payment timeout errors
class PaymentTimeoutException extends PaymentException {
  final Duration timeout;

  const PaymentTimeoutException(
    super.message, {
    required this.timeout,
    super.code,
    super.details,
  });

  @override
  String toString() => 'PaymentTimeoutException: $message (Timeout: ${timeout.inSeconds}s)';
}
