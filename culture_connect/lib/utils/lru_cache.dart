import 'dart:collection';

/// A Least Recently Used (LRU) cache implementation.
///
/// This cache has a maximum capacity and evicts the least recently used items
/// when the capacity is exceeded.
class LRUCache<K, V> {
  /// The maximum number of items the cache can hold
  final int capacity;

  /// The internal linked hash map that stores the cache entries
  final LinkedHashMap<K, V> _cache = LinkedHashMap<K, V>();

  /// Creates a new LRU cache with the specified capacity
  LRUCache({required this.capacity}) {
    if (capacity <= 0) {
      throw ArgumentError('Capacity must be greater than 0');
    }
  }

  /// Gets the value associated with the key.
  ///
  /// If the key exists, it is moved to the end of the cache (most recently used).
  /// Returns null if the key does not exist.
  V? get(K key) {
    final value = _cache.remove(key);
    if (value != null) {
      // Put the entry at the end (most recently used)
      _cache[key] = value;
    }
    return value;
  }

  /// Puts a key-value pair in the cache.
  ///
  /// If the key already exists, its value is updated and it becomes the most recently used.
  /// If the cache is at capacity, the least recently used entry is evicted.
  void put(K key, V value) {
    // Remove the key if it exists to update its position
    _cache.remove(key);

    // If we're at capacity, remove the least recently used item (first item)
    if (_cache.length >= capacity && !_cache.containsKey(key)) {
      _cache.remove(_cache.keys.first);
    }

    // Add the new entry at the end (most recently used)
    _cache[key] = value;
  }

  /// Removes a key from the cache.
  ///
  /// Returns the value associated with the key, or null if the key does not exist.
  V? remove(K key) {
    return _cache.remove(key);
  }

  /// Clears all entries from the cache.
  void clear() {
    _cache.clear();
  }

  /// Returns true if the cache contains the key.
  bool containsKey(K key) {
    return _cache.containsKey(key);
  }

  /// Returns the number of entries in the cache.
  int get size => _cache.length;

  /// Returns true if the cache is empty.
  bool get isEmpty => _cache.isEmpty;

  /// Returns true if the cache is at capacity.
  bool get isFull => _cache.length >= capacity;

  /// Returns the keys in the cache, ordered from least recently used to most recently used.
  Iterable<K> get keys => _cache.keys;

  /// Returns the values in the cache, ordered from least recently used to most recently used.
  Iterable<V> get values => _cache.values;

  /// Returns the entries in the cache, ordered from least recently used to most recently used.
  Iterable<MapEntry<K, V>> get entries => _cache.entries;
}

/// A specialized LRU cache for translations with additional metrics tracking.
class TranslationLRUCache<K, V> extends LRUCache<K, V> {
  /// The number of cache hits
  int _hits = 0;

  /// The number of cache misses
  int _misses = 0;

  /// The total time spent retrieving items from the cache (in milliseconds)
  int _totalRetrievalTimeMs = 0;

  /// Creates a new translation LRU cache with the specified capacity
  TranslationLRUCache({required super.capacity});

  @override
  V? get(K key) {
    final stopwatch = Stopwatch()..start();
    final value = super.get(key);
    stopwatch.stop();

    _totalRetrievalTimeMs += stopwatch.elapsedMilliseconds;

    if (value != null) {
      _hits++;
    } else {
      _misses++;
    }

    return value;
  }

  /// Gets the number of cache hits
  int get hits => _hits;

  /// Gets the number of cache misses
  int get misses => _misses;

  /// Gets the total number of cache accesses (hits + misses)
  int get accesses => _hits + _misses;

  /// Gets the cache hit rate (hits / accesses)
  double get hitRate => accesses == 0 ? 0 : _hits / accesses;

  /// Gets the cache miss rate (misses / accesses)
  double get missRate => accesses == 0 ? 0 : _misses / accesses;

  /// Gets the average retrieval time in milliseconds
  double get averageRetrievalTimeMs =>
      accesses == 0 ? 0 : _totalRetrievalTimeMs / accesses;

  /// Resets the cache metrics
  void resetMetrics() {
    _hits = 0;
    _misses = 0;
    _totalRetrievalTimeMs = 0;
  }

  /// Gets a map of the cache metrics
  Map<String, dynamic> getMetrics() {
    return {
      'capacity': capacity,
      'size': size,
      'hits': _hits,
      'misses': _misses,
      'accesses': accesses,
      'hitRate': hitRate,
      'missRate': missRate,
      'averageRetrievalTimeMs': averageRetrievalTimeMs,
    };
  }

  @override
  void clear() {
    super.clear();
    resetMetrics();
  }
}
