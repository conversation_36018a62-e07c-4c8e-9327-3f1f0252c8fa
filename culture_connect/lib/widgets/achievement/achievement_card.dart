// Flutter imports
import 'package:flutter/material.dart';

// Project imports
import 'package:culture_connect/models/achievement/user_achievement.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Widget for displaying achievement cards with progress and status
class AchievementCard extends StatelessWidget {
  /// The user achievement to display
  final UserAchievement userAchievement;

  /// Whether to show the card in compact mode
  final bool isCompact;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Whether to show the celebration animation
  final bool showCelebration;

  const AchievementCard({
    super.key,
    required this.userAchievement,
    this.isCompact = false,
    this.onTap,
    this.showCelebration = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final achievement = userAchievement.achievement;

    return Card(
      elevation: userAchievement.isUnlocked ? 8 : 2,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        side: userAchievement.isUnlocked
            ? BorderSide(color: achievement.color, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
            gradient: userAchievement.isUnlocked
                ? LinearGradient(
                    colors: [
                      achievement.color.withAlpha(26),
                      achievement.color.withAlpha(13),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : null,
          ),
          child: isCompact
              ? _buildCompactContent(theme)
              : _buildFullContent(theme),
        ),
      ),
    );
  }

  Widget _buildCompactContent(ThemeData theme) {
    final achievement = userAchievement.achievement;

    return Row(
      children: [
        // Achievement icon
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: userAchievement.isUnlocked
                ? achievement.color
                : theme.colorScheme.surfaceContainerHighest,
            shape: BoxShape.circle,
          ),
          child: Icon(
            achievement.icon,
            color: userAchievement.isUnlocked
                ? Colors.white
                : theme.colorScheme.onSurfaceVariant,
            size: 24,
          ),
        ),

        const SizedBox(width: 12),

        // Achievement info
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                achievement.title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: userAchievement.isUnlocked
                      ? achievement.color
                      : theme.colorScheme.onSurface,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                userAchievement.statusDescription,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),

        // Progress indicator
        if (!userAchievement.isUnlocked) ...[
          const SizedBox(width: 8),
          SizedBox(
            width: 32,
            height: 32,
            child: CircularProgressIndicator(
              value: userAchievement.progress,
              strokeWidth: 3,
              backgroundColor: theme.colorScheme.surfaceContainerHighest,
              valueColor: AlwaysStoppedAnimation<Color>(achievement.color),
            ),
          ),
        ] else ...[
          const SizedBox(width: 8),
          Icon(
            Icons.check_circle,
            color: achievement.color,
            size: 32,
          ),
        ],
      ],
    );
  }

  Widget _buildFullContent(ThemeData theme) {
    final achievement = userAchievement.achievement;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header row
        Row(
          children: [
            // Achievement icon
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: userAchievement.isUnlocked
                    ? achievement.color
                    : theme.colorScheme.surfaceContainerHighest,
                shape: BoxShape.circle,
                boxShadow: userAchievement.isUnlocked
                    ? [
                        BoxShadow(
                          color: achievement.color.withAlpha(77),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ]
                    : null,
              ),
              child: Icon(
                achievement.icon,
                color: userAchievement.isUnlocked
                    ? Colors.white
                    : theme.colorScheme.onSurfaceVariant,
                size: 32,
              ),
            ),

            const SizedBox(width: 16),

            // Achievement info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    achievement.title,
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: userAchievement.isUnlocked
                          ? achievement.color
                          : theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: achievement.color.withAlpha(26),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          achievement.difficultyDisplayName,
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: achievement.color,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          achievement.categoryDisplayName,
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Status indicator
            if (userAchievement.isUnlocked) ...[
              Icon(
                Icons.check_circle,
                color: achievement.color,
                size: 32,
              ),
            ] else if (userAchievement.canUnlock) ...[
              const Icon(
                Icons.star,
                color: Colors.amber,
                size: 32,
              ),
            ],
          ],
        ),

        const SizedBox(height: 16),

        // Description
        Text(
          achievement.description,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),

        const SizedBox(height: 16),

        // Progress section
        if (!userAchievement.isUnlocked) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Progress',
                style: theme.textTheme.labelMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                '${userAchievement.progressPercentage}%',
                style: theme.textTheme.labelMedium?.copyWith(
                  color: achievement.color,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: userAchievement.progress,
            backgroundColor: theme.colorScheme.surfaceContainerHighest,
            valueColor: AlwaysStoppedAnimation<Color>(achievement.color),
            minHeight: 6,
          ),
          const SizedBox(height: 8),
          Text(
            userAchievement.nextMilestone,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ] else ...[
          // Reward section
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: achievement.color.withAlpha(26),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.card_giftcard,
                  color: achievement.color,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Reward: ${achievement.rewardDisplayName}',
                  style: theme.textTheme.labelMedium?.copyWith(
                    color: achievement.color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                if (userAchievement.timesEarned > 1)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: achievement.color,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      '×${userAchievement.timesEarned}',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ],
    );
  }
}
