import 'package:flutter/material.dart';

/// A widget for AR controls
class ARControls extends StatelessWidget {
  /// Callback when the info button is tapped
  final VoidCallback? onInfoToggle;

  /// Callback when the share button is tapped
  final VoidCallback? onShare;

  /// Callback when the download button is tapped
  final VoidCallback? onDownload;

  /// Whether the content is available offline
  final bool isAvailableOffline;

  /// Creates a new AR controls widget
  const ARControls({
    super.key,
    this.onInfoToggle,
    this.onShare,
    this.onDownload,
    this.isAvailableOffline = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(128),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Info button
          _buildControlButton(
            icon: Icons.info_outline,
            label: 'Info',
            onTap: onInfoToggle,
          ),

          // Share button
          _buildControlButton(
            icon: Icons.share,
            label: 'Share',
            onTap: onShare,
          ),

          // Download/Remove button
          _buildControlButton(
            icon: isAvailableOffline ? Icons.delete : Icons.download,
            label: isAvailableOffline ? 'Remove' : 'Download',
            onTap: onDownload,
            color: isAvailableOffline ? Colors.red : Colors.blue,
          ),
        ],
      ),
    );
  }

  /// Build a control button
  Widget _buildControlButton({
    required IconData icon,
    required String label,
    VoidCallback? onTap,
    Color? color,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon
            Icon(
              icon,
              color: color ?? Colors.white,
              size: 24,
            ),

            const SizedBox(height: 4),

            // Label
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: color ?? Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
