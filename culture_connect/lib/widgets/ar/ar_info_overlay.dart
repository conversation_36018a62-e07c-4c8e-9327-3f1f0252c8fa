import 'package:flutter/material.dart';

import 'package:culture_connect/models/ar/ar_content_marker.dart';

/// A widget for displaying AR content information
class ARInfoOverlay extends StatelessWidget {
  /// The title of the AR content
  final String title;

  /// The description of the AR content
  final String? description;

  /// The type of AR content
  final ARContentType contentType;

  /// Callback when the close button is tapped
  final VoidCallback? onClose;

  /// Creates a new AR info overlay
  const ARInfoOverlay({
    super.key,
    required this.title,
    this.description,
    required this.contentType,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(179),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              // Content type icon
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: contentType.color.withAlpha(51),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Icon(
                    contentType.icon,
                    color: contentType.color,
                    size: 20,
                  ),
                ),
              ),

              const SizedBox(width: 12),

              // Title
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),

              // Close button
              IconButton(
                icon: const Icon(Icons.close, color: Colors.white),
                onPressed: onClose,
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Content type
          Row(
            children: [
              Icon(
                contentType.icon,
                size: 16,
                color: contentType.color,
              ),
              const SizedBox(width: 8),
              Text(
                contentType.displayName,
                style: TextStyle(
                  fontSize: 14,
                  color: contentType.color,
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Description
          if (description != null) ...[
            Text(
              description!,
              style: TextStyle(
                fontSize: 14,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Instructions
          Text(
            'Instructions:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),

          const SizedBox(height: 8),

          // Pinch to zoom
          Row(
            children: [
              Icon(
                Icons.pinch,
                size: 16,
                color: Colors.white,
              ),
              const SizedBox(width: 8),
              Text(
                'Pinch to zoom in and out',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                ),
              ),
            ],
          ),

          const SizedBox(height: 4),

          // Rotate
          Row(
            children: [
              Icon(
                Icons.rotate_right,
                size: 16,
                color: Colors.white,
              ),
              const SizedBox(width: 8),
              Text(
                'Rotate with two fingers',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                ),
              ),
            ],
          ),

          const SizedBox(height: 4),

          // Tap
          Row(
            children: [
              Icon(
                Icons.touch_app,
                size: 16,
                color: Colors.white,
              ),
              const SizedBox(width: 8),
              Text(
                'Tap to interact with the content',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
