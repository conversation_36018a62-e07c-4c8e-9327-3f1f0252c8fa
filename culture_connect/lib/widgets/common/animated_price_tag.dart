import 'package:flutter/material.dart';

/// A widget that displays an animated price tag
class AnimatedPriceTag extends StatefulWidget {
  /// The price to display
  final double price;

  /// The currency to display
  final String currency;

  /// The text style
  final TextStyle? style;

  /// The number of decimal places to show
  final int decimalPlaces;

  /// The duration of the animation
  final Duration duration;

  /// Creates a new animated price tag
  const AnimatedPriceTag({
    super.key,
    required this.price,
    required this.currency,
    this.style,
    this.decimalPlaces = 2,
    this.duration = const Duration(milliseconds: 500),
  });

  @override
  State<AnimatedPriceTag> createState() => _AnimatedPriceTagState();
}

class _AnimatedPriceTagState extends State<AnimatedPriceTag>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  late double _oldPrice;
  late double _newPrice;

  @override
  void initState() {
    super.initState();
    _oldPrice = widget.price;
    _newPrice = widget.price;

    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );

    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(AnimatedPriceTag oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.price != widget.price) {
      _oldPrice = oldWidget.price;
      _newPrice = widget.price;
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        final currentPrice =
            _oldPrice + (_newPrice - _oldPrice) * _animation.value;

        return Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.currency,
              style: widget.style,
            ),
            Text(
              currentPrice.toStringAsFixed(widget.decimalPlaces),
              style: widget.style,
            ),
          ],
        );
      },
    );
  }
}
