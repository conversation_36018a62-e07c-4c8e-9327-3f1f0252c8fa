import 'package:flutter/material.dart';

/// A widget for displaying a section with an animated transition
class AnimatedSection extends StatefulWidget {
  /// The title of the section
  final String title;

  /// The content of the section
  final Widget content;

  /// Whether the section is initially expanded
  final bool initiallyExpanded;

  /// The style of the title
  final TextStyle? titleStyle;

  /// The padding around the section
  final EdgeInsetsGeometry padding;

  /// The margin around the section
  final EdgeInsetsGeometry margin;

  /// The background color of the section
  final Color? backgroundColor;

  /// The border radius of the section
  final BorderRadius? borderRadius;

  /// The elevation of the section
  final double elevation;

  /// Whether to show a divider between the title and content
  final bool showDivider;

  /// Whether the section is collapsible
  final bool isCollapsible;

  /// Creates a new animated section
  const AnimatedSection({
    super.key,
    required this.title,
    required this.content,
    this.initiallyExpanded = true,
    this.titleStyle,
    this.padding = const EdgeInsets.all(16),
    this.margin = const EdgeInsets.only(bottom: 16),
    this.backgroundColor,
    this.borderRadius,
    this.elevation = 1,
    this.showDivider = true,
    this.isCollapsible = true,
  });

  @override
  State<AnimatedSection> createState() => _AnimatedSectionState();
}

class _AnimatedSectionState extends State<AnimatedSection>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _heightFactor;
  late Animation<double> _iconTurns;
  late Animation<double> _opacity;

  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _heightFactor = _controller.drive(CurveTween(curve: Curves.easeInOut));
    _iconTurns = _controller.drive(Tween<double>(begin: 0.0, end: 0.5)
        .chain(CurveTween(curve: Curves.easeInOut)));
    _opacity = _controller.drive(Tween<double>(begin: 0.0, end: 1.0)
        .chain(CurveTween(curve: Curves.easeInOut)));

    _isExpanded = widget.initiallyExpanded;
    if (_isExpanded) {
      _controller.value = 1.0;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final titleStyle = widget.titleStyle ?? theme.textTheme.titleLarge;

    return Card(
      margin: widget.margin,
      elevation: widget.elevation,
      color: widget.backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title bar
          InkWell(
            onTap: widget.isCollapsible ? _toggleExpanded : null,
            borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
            child: Padding(
              padding: widget.padding,
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      widget.title,
                      style: titleStyle,
                    ),
                  ),
                  if (widget.isCollapsible)
                    RotationTransition(
                      turns: _iconTurns,
                      child: Icon(
                        Icons.keyboard_arrow_down,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Divider
          if (widget.showDivider)
            AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return Divider(
                  height: 1,
                  thickness: 1,
                  color: theme.colorScheme.outlineVariant
                      .withAlpha(_isExpanded ? 255 : 0),
                );
              },
            ),

          // Content
          ClipRect(
            child: AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return Align(
                  alignment: Alignment.topCenter,
                  heightFactor: _heightFactor.value,
                  child: Opacity(
                    opacity: _opacity.value,
                    child: child,
                  ),
                );
              },
              child: Padding(
                padding: widget.padding,
                child: widget.content,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
