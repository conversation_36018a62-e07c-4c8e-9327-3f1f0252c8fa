import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';

/// A custom date and time picker field
class DateTimePickerField extends StatelessWidget {
  /// The label text to display
  final String label;

  /// The currently selected date and time
  final DateTime? selectedDateTime;

  /// Callback when a date and time is selected
  final Function(DateTime) onDateTimeSelected;

  /// The validator function
  final String? Function(DateTime?)? validator;

  /// Creates a new date time picker field
  const DateTimePickerField({
    super.key,
    required this.label,
    this.selectedDateTime,
    required this.onDateTimeSelected,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    final errorText =
        selectedDateTime != null ? validator?.call(selectedDateTime) : null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.subtitle2,
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () => _showDateTimePicker(context),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color:
                    errorText != null ? AppColors.error : Colors.grey.shade300,
                width: errorText != null ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    selectedDateTime != null
                        ? _formatDateTime(selectedDateTime!)
                        : 'Select date and time',
                    style: selectedDateTime != null
                        ? AppTextStyles.body1
                        : AppTextStyles.body2.copyWith(
                            color: AppColors.textSecondary,
                          ),
                  ),
                ),
                const Icon(
                  Icons.calendar_today,
                  color: AppColors.primary,
                ),
              ],
            ),
          ),
        ),
        if (errorText != null)
          Padding(
            padding: EdgeInsets.only(top: 8, left: 16),
            child: Text(
              errorText,
              style: AppTextStyles.caption.copyWith(
                color: AppColors.error,
              ),
            ),
          ),
      ],
    );
  }

  /// Show the date time picker
  Future<void> _showDateTimePicker(BuildContext context) async {
    final initialDate =
        selectedDateTime ?? DateTime.now().add(const Duration(days: 1));

    // Show date picker
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate == null) return;

    // Show time picker
    if (!context.mounted) return;
    final pickedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(
          selectedDateTime ?? DateTime.now().add(const Duration(hours: 1))),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedTime == null) return;

    // Combine date and time
    final dateTime = DateTime(
      pickedDate.year,
      pickedDate.month,
      pickedDate.day,
      pickedTime.hour,
      pickedTime.minute,
    );

    onDateTimeSelected(dateTime);
  }

  /// Format the date time
  String _formatDateTime(DateTime dateTime) {
    final dateFormat = DateFormat('EEE, MMM d, yyyy');
    final timeFormat = DateFormat('h:mm a');
    return '${dateFormat.format(dateTime)} at ${timeFormat.format(dateTime)}';
  }
}
