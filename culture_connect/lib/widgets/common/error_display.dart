import 'package:flutter/material.dart';

/// A widget for displaying error messages
class ErrorDisplay extends StatelessWidget {
  /// The error message to display
  final String message;

  /// The error details (optional)
  final String? details;

  /// The icon to display (defaults to error icon)
  final IconData icon;

  /// The color of the icon and border (defaults to error color)
  final Color? color;

  /// Whether to show a retry button
  final bool showRetry;

  /// Callback when retry button is pressed
  final VoidCallback? onRetry;

  /// Creates a new error display
  const ErrorDisplay({
    super.key,
    required this.message,
    this.details,
    this.icon = Icons.error_outline,
    this.color,
    this.showRetry = false,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final errorColor = color ?? theme.colorScheme.error;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.errorContainer.withAlpha(51), // 0.2 * 255 = 51
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: errorColor.withAlpha(128), // 0.5 * 255 = 127.5, rounded to 128
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: errorColor,
            size: 32,
          ),
          SizedBox(height: 8),
          Text(
            message,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.error,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          if (details != null) ...[
            SizedBox(height: 8),
            Text(
              details!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          if (showRetry && onRetry != null) ...[
            SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
