import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';
import 'package:culture_connect/widgets/common/custom_button.dart';

/// A widget that displays an error state
class ErrorState extends StatelessWidget {
  /// The error message to display
  final String message;

  /// The callback when the retry button is pressed
  final VoidCallback? onRetry;

  /// The retry button text
  final String retryText;

  /// Creates a new error state
  const ErrorState({
    super.key,
    required this.message,
    this.onRetry,
    this.retryText = 'Retry',
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 80,
              color: AppColors.error,
            ),
            const SizedBox(height: 24),
            Text(
              'Something went wrong',
              style: AppTextStyles.headline6,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: AppTextStyles.body2,
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 32),
              CustomButton(
                text: retryText,
                onPressed: onRetry,
                icon: Icons.refresh,
                color: AppColors.error,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
