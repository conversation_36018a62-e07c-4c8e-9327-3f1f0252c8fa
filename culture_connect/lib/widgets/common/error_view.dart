import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_colors.dart';

/// A widget for displaying an error
class ErrorView extends StatelessWidget {
  /// The error message to display
  final String error;

  /// Callback when the retry button is pressed
  final VoidCallback? onRetry;

  /// The icon to display
  final IconData icon;

  /// The color of the icon
  final Color? iconColor;

  /// The title to display
  final String? title;

  /// Creates a new error view
  const ErrorView({
    super.key,
    required this.error,
    this.onRetry,
    this.icon = Icons.error_outline,
    this.iconColor,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: iconColor ?? theme.colorScheme.error,
            size: 48,
          ),
          SizedBox(height: 16),
          Text(
            title ?? 'An error occurred',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8),
          Text(
            error,
            style: theme.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          if (onRetry != null) ...[
            SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                foregroundColor: Colors.white,
                backgroundColor: AppColors.primary,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// A widget that displays an offline error message
class OfflineErrorView extends StatelessWidget {
  /// The callback when the retry button is pressed
  final VoidCallback? onRetry;

  /// The title to display
  final String title;

  /// The message to display
  final String message;

  /// Creates a new offline error view
  const OfflineErrorView({
    super.key,
    this.onRetry,
    this.title = 'You\'re offline',
    this.message = 'Please check your internet connection and try again.',
  });

  @override
  Widget build(BuildContext context) {
    return ErrorView(
      error: message,
      onRetry: onRetry,
      icon: Icons.wifi_off,
      iconColor: Colors.grey,
      title: title,
    );
  }
}

/// A widget that displays an empty state message
class EmptyStateView extends StatelessWidget {
  /// The title to display
  final String title;

  /// The message to display
  final String message;

  /// The icon to display
  final IconData icon;

  /// The color of the icon
  final Color iconColor;

  /// The action button text
  final String? actionText;

  /// The callback when the action button is pressed
  final VoidCallback? onAction;

  /// Creates a new empty state view
  const EmptyStateView({
    super.key,
    required this.title,
    required this.message,
    this.icon = Icons.inbox,
    this.iconColor = Colors.grey,
    this.actionText,
    this.onAction,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64,
              color: iconColor,
            ),
            SizedBox(height: 16),
            Text(
              title,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: theme.textTheme.titleLarge?.color,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              message,
              style: TextStyle(
                fontSize: 16,
                color: theme.textTheme.bodyMedium?.color,
              ),
              textAlign: TextAlign.center,
            ),
            if (actionText != null && onAction != null) ...[
              SizedBox(height: 24),
              ElevatedButton(
                onPressed: onAction,
                style: ElevatedButton.styleFrom(
                  foregroundColor: Colors.white,
                  backgroundColor: AppColors.primary,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
                child: Text(actionText!),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
