import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_colors.dart';

/// A widget for displaying a star rating
class RatingStars extends StatelessWidget {
  /// The rating value (0.0 to 5.0)
  final double rating;

  /// The size of each star
  final double size;

  /// The color of the filled stars
  final Color? color;

  /// The color of the empty stars
  final Color? emptyColor;

  /// Whether to show the rating value
  final bool showRating;

  /// Creates a new rating stars widget
  const RatingStars({
    super.key,
    required this.rating,
    this.size = 24.0,
    this.color,
    this.emptyColor,
    this.showRating = false,
  });

  @override
  Widget build(BuildContext context) {
    final filledColor = color ?? AppColors.warning;
    final unfilledColor = emptyColor ?? Colors.grey.shade300;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(5, (index) {
            final starValue = index + 1;

            if (starValue <= rating) {
              // Full star
              return Icon(
                Icons.star,
                color: filledColor,
                size: size,
              );
            } else if (starValue - 0.5 <= rating && rating < starValue) {
              // Half star
              return Icon(
                Icons.star_half,
                color: filledColor,
                size: size,
              );
            } else {
              // Empty star
              return Icon(
                Icons.star_border,
                color: unfilledColor,
                size: size,
              );
            }
          }),
        ),
        if (showRating) ...[
          const SizedBox(width: 4),
          Text(
            rating.toStringAsFixed(1),
            style: TextStyle(
              fontSize: size * 0.75,
              fontWeight: FontWeight.bold,
              color: filledColor,
            ),
          ),
        ],
      ],
    );
  }
}
