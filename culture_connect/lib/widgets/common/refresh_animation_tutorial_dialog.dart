import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/utils/refresh_animation_utils.dart';
import 'package:culture_connect/providers/refresh_animation_provider.dart';

/// A dialog that introduces users to the new pull-to-refresh animations
class RefreshAnimationTutorialDialog extends ConsumerWidget {
  /// Creates a new refresh animation tutorial dialog
  const RefreshAnimationTutorialDialog({super.key});

  /// Show the tutorial dialog
  static Future<void> show(BuildContext context) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const RefreshAnimationTutorialDialog();
      },
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final selectedAnimationType = ref.watch(refreshAnimationTypeProvider);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title
            Text(
              'New Pull-to-Refresh Animations!',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),

            // Animation icon
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer,
                shape: BoxShape.circle,
              ),
              child: Icon(
                RefreshAnimationUtils.getIcon(selectedAnimationType),
                color: theme.colorScheme.primary,
                size: 40,
              ),
            ),
            SizedBox(height: 16),

            // Description
            Text(
              'We\'ve added beautiful new animations to enhance your pull-to-refresh experience!',
              style: theme.textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),

            // Features
            _buildFeatureItem(
              context,
              icon: Icons.animation,
              title: 'Multiple Animation Styles',
              description: 'Choose from 5 different animation styles',
            ),
            _buildFeatureItem(
              context,
              icon: Icons.touch_app,
              title: 'Haptic Feedback',
              description: 'Feel a subtle vibration when refreshing',
            ),
            _buildFeatureItem(
              context,
              icon: Icons.settings,
              title: 'Customizable',
              description: 'Change your animation style in settings',
            ),
            SizedBox(height: 24),

            // Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: () {
                    // Mark the tutorial as seen
                    ref
                        .read(hasSeenRefreshTutorialProvider.notifier)
                        .markAsSeen();

                    // Navigate to the settings screen
                    Navigator.of(context).pop();
                    Navigator.of(context)
                        .pushNamed('/settings/refresh-animations');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  child: const Text('Try It Now'),
                ),
                SizedBox(width: 16),
                TextButton(
                  onPressed: () {
                    // Mark the tutorial as seen
                    ref
                        .read(hasSeenRefreshTutorialProvider.notifier)
                        .markAsSeen();

                    // Close the dialog
                    Navigator.of(context).pop();
                  },
                  child: const Text('Later'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
  }) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer,
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: theme.colorScheme.primary,
              size: 20,
            ),
          ),
          SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  description,
                  style: theme.textTheme.bodySmall,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
