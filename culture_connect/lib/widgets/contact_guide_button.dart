import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/guide.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/error_handling_service.dart';
import 'package:culture_connect/services/analytics_service.dart';

/// A widget for contacting a guide
class ContactGuideButton extends ConsumerWidget {
  final Guide guide;
  final String? experienceId;
  final String? experienceName;
  final Color? color;
  final bool showLabel;
  final bool isOutlined;
  final VoidCallback? onContactInitiated;

  const ContactGuideButton({
    super.key,
    required this.guide,
    this.experienceId,
    this.experienceName,
    this.color,
    this.showLabel = true,
    this.isOutlined = false,
    this.onContactInitiated,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final loggingService = ref.watch(loggingServiceProvider);
    final errorHandlingService = ref.watch(errorHandlingServiceProvider);
    final analyticsService = ref.watch(analyticsServiceProvider);

    return isOutlined
        ? OutlinedButton.icon(
            onPressed: () => _contactGuide(
              context,
              ref,
              loggingService,
              errorHandlingService,
              analyticsService,
            ),
            icon: const Icon(Icons.message),
            label: showLabel
                ? const Text('Contact Guide')
                : const SizedBox.shrink(),
            style: OutlinedButton.styleFrom(
              foregroundColor: color ?? theme.colorScheme.primary,
            ),
          )
        : ElevatedButton.icon(
            onPressed: () => _contactGuide(
              context,
              ref,
              loggingService,
              errorHandlingService,
              analyticsService,
            ),
            icon: const Icon(Icons.message),
            label: showLabel
                ? const Text('Contact Guide')
                : const SizedBox.shrink(),
            style: ElevatedButton.styleFrom(
              backgroundColor: color ?? theme.colorScheme.primary,
              foregroundColor: theme.colorScheme.onPrimary,
            ),
          );
  }

  Future<void> _contactGuide(
    BuildContext context,
    WidgetRef ref,
    LoggingService? loggingService,
    ErrorHandlingService? errorHandlingService,
    AnalyticsService? analyticsService,
  ) async {
    try {
      // Log the contact attempt
      loggingService?.info(
        'ContactGuideButton',
        'Contacting guide: ${guide.id}',
      );

      // Track the contact event
      analyticsService?.logEvent(
        name: 'contact_guide',
        category: AnalyticsCategory.userAction,
        parameters: {
          'guide_id': guide.id,
          'guide_name': guide.name,
          'experience_id': experienceId ?? 'unknown',
          'experience_name': experienceName ?? 'unknown',
        },
      );

      // In a real implementation, we would get the messaging service and create a conversation
      // For now, we'll just show a snackbar indicating the action
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Contacting guide: ${guide.name}'),
            duration: const Duration(seconds: 2),
          ),
        );
      }

      // Call the onContactInitiated callback
      onContactInitiated?.call();
    } catch (e, stackTrace) {
      // Log the error
      loggingService?.error(
        'ContactGuideButton',
        'Error contacting guide',
        e,
        stackTrace,
      );

      // Handle the error
      errorHandlingService?.handleError(
        error: e,
        stackTrace: stackTrace,
        context: 'ContactGuideButton._contactGuide',
        type: ErrorType.unknown,
        severity: ErrorSeverity.medium,
      );

      // Show an error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to contact guide: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}

/// A widget for displaying a contact guide dialog
class ContactGuideDialog extends ConsumerWidget {
  final Guide guide;
  final String? experienceId;
  final String? experienceName;

  const ContactGuideDialog({
    super.key,
    required this.guide,
    this.experienceId,
    this.experienceName,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircleAvatar(
              radius: 40,
              backgroundImage: guide.profileImageUrl != null
                  ? NetworkImage(guide.profileImageUrl!)
                  : null,
              child: guide.profileImageUrl == null
                  ? const Icon(Icons.person, size: 40)
                  : null,
            ),
            const SizedBox(height: 16),
            Text(
              'Contact ${guide.name}',
              style: theme.textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              guide.bio ?? 'Local guide',
              textAlign: TextAlign.center,
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.star, color: Colors.amber, size: 16),
                const SizedBox(width: 4),
                Text(
                  '${guide.rating?.toStringAsFixed(1) ?? 'N/A'} (${guide.reviewCount ?? 0} reviews)',
                  style: theme.textTheme.bodySmall,
                ),
              ],
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildContactOption(
                  context,
                  Icons.message,
                  'Message',
                  () => _contactVia(context, ref, 'message'),
                ),
                _buildContactOption(
                  context,
                  Icons.email,
                  'Email',
                  () => _contactVia(context, ref, 'email'),
                ),
                _buildContactOption(
                  context,
                  Icons.phone,
                  'Call',
                  () => _contactVia(context, ref, 'call'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactOption(
    BuildContext context,
    IconData icon,
    String label,
    VoidCallback onTap,
  ) {
    final theme = Theme.of(context);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: EdgeInsets.all(8),
        child: Column(
          children: [
            CircleAvatar(
              backgroundColor: theme.colorScheme.primary
                  .withAlpha(26), // 0.1 opacity is approximately 26 alpha
              radius: 24,
              child: Icon(
                icon,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: theme.textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _contactVia(
    BuildContext context,
    WidgetRef ref,
    String method,
  ) async {
    final loggingService = ref.read(loggingServiceProvider);
    final analyticsService = ref.read(analyticsServiceProvider);

    // Track the contact method
    analyticsService.logEvent(
      name: 'contact_guide_via',
      category: AnalyticsCategory.userAction,
      parameters: {
        'guide_id': guide.id,
        'guide_name': guide.name,
        'experience_id': experienceId ?? 'unknown',
        'experience_name': experienceName ?? 'unknown',
        'method': method,
      },
    );

    // Log the contact method
    loggingService.info(
      'ContactGuideDialog',
      'Contacting guide via $method: ${guide.id}',
    );

    // Close the dialog
    Navigator.of(context).pop();

    // Contact the guide based on the method
    switch (method) {
      case 'message':
        // Use the contact guide button to navigate to the chat screen
        final contactButton = ContactGuideButton(
          guide: guide,
          experienceId: experienceId,
          experienceName: experienceName,
        );
        await contactButton._contactGuide(
          context,
          ref,
          loggingService,
          ref.read(errorHandlingServiceProvider),
          analyticsService,
        );
        break;

      case 'email':
        // In a real app, this would open the email app
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Opening email app...'),
            ),
          );
        }
        break;

      case 'call':
        // In a real app, this would open the phone app
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Opening phone app...'),
            ),
          );
        }
        break;
    }
  }
}
