import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

/// A widget that displays an empty state
class EmptyState extends StatelessWidget {
  /// The title of the empty state
  final String title;
  
  /// The message of the empty state
  final String message;
  
  /// The icon to display
  final IconData? icon;
  
  /// The animation asset to display
  final String? animationAsset;
  
  /// The action button text
  final String? actionText;
  
  /// The action button callback
  final VoidCallback? onAction;
  
  /// Creates a new empty state
  const EmptyState({
    super.key,
    required this.title,
    required this.message,
    this.icon,
    this.animationAsset,
    this.actionText,
    this.onAction,
  }) : assert(icon != null || animationAsset != null, 'Either icon or animationAsset must be provided');
  
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (animationAsset != null)
              Lottie.asset(
                animationAsset!,
                width: 150,
                height: 150,
                repeat: true,
              )
            else if (icon != null)
              Icon(
                icon,
                size: 80,
                color: Colors.grey[400],
              ),
            SizedBox(height: 24),
            Text(
              title,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              message,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            if (actionText != null && onAction != null) ...[
              SizedBox(height: 24),
              ElevatedButton(
                onPressed: onAction,
                child: Text(actionText!),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
