// Flutter imports
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/reviews/star_rating_input.dart';
import 'package:culture_connect/models/mood/mood_model.dart';

/// Predefined feedback tags for quick selection
enum FeedbackTag {
  excellent,
  good,
  average,
  poor,
  terrible,
}

/// Extension for feedback tag properties
extension FeedbackTagExtension on FeedbackTag {
  /// Get display name for the tag
  String get displayName {
    switch (this) {
      case FeedbackTag.excellent:
        return 'Excellent';
      case FeedbackTag.good:
        return 'Good';
      case FeedbackTag.average:
        return 'Average';
      case FeedbackTag.poor:
        return 'Poor';
      case FeedbackTag.terrible:
        return 'Terrible';
    }
  }

  /// Get color for the tag
  Color get color {
    switch (this) {
      case FeedbackTag.excellent:
        return const Color(0xFF4CAF50); // Green
      case FeedbackTag.good:
        return const Color(0xFF8BC34A); // Light Green
      case FeedbackTag.average:
        return const Color(0xFF9E9E9E); // Grey
      case FeedbackTag.poor:
        return const Color(0xFFFF9800); // Orange
      case FeedbackTag.terrible:
        return const Color(0xFFF44336); // Red
    }
  }

  /// Get corresponding mood type
  MoodType get correspondingMood {
    switch (this) {
      case FeedbackTag.excellent:
        return MoodType.veryHappy;
      case FeedbackTag.good:
        return MoodType.happy;
      case FeedbackTag.average:
        return MoodType.neutral;
      case FeedbackTag.poor:
        return MoodType.sad;
      case FeedbackTag.terrible:
        return MoodType.verySad;
    }
  }
}

/// A comprehensive satisfaction rating widget with multiple input methods
class SatisfactionWidget extends ConsumerStatefulWidget {
  /// Initial rating value (1-5)
  final double? initialRating;

  /// Initial selected tags
  final List<FeedbackTag>? initialTags;

  /// Initial feedback text
  final String? initialFeedback;

  /// Callback when rating changes
  final ValueChanged<double>? onRatingChanged;

  /// Callback when feedback text changes
  final ValueChanged<String>? onFeedbackChanged;

  /// Callback when tags change
  final ValueChanged<List<FeedbackTag>>? onTagsChanged;

  /// Whether to show quick feedback tags
  final bool showQuickTags;

  /// Whether to enable detailed feedback input
  final bool enableDetailedFeedback;

  /// Custom predefined tags
  final List<String>? customTags;

  /// Whether to enable haptic feedback
  final bool enableHapticFeedback;

  /// Maximum characters for feedback text
  final int maxFeedbackLength;

  /// Whether the widget is enabled
  final bool enabled;

  /// Creates a new satisfaction widget
  const SatisfactionWidget({
    super.key,
    this.initialRating,
    this.initialTags,
    this.initialFeedback,
    this.onRatingChanged,
    this.onFeedbackChanged,
    this.onTagsChanged,
    this.showQuickTags = true,
    this.enableDetailedFeedback = true,
    this.customTags,
    this.enableHapticFeedback = true,
    this.maxFeedbackLength = 500,
    this.enabled = true,
  });

  @override
  ConsumerState<SatisfactionWidget> createState() => _SatisfactionWidgetState();
}

class _SatisfactionWidgetState extends ConsumerState<SatisfactionWidget>
    with TickerProviderStateMixin {
  double _rating = 0.0;
  List<FeedbackTag> _selectedTags = [];
  String _feedbackText = '';
  bool _showDetailedFeedback = false;

  late TextEditingController _feedbackController;
  late AnimationController _expandController;
  late Animation<double> _expandAnimation;

  @override
  void initState() {
    super.initState();

    _rating = widget.initialRating ?? 0.0;
    _selectedTags = List.from(widget.initialTags ?? []);
    _feedbackText = widget.initialFeedback ?? '';

    _feedbackController = TextEditingController(text: _feedbackText);

    _expandController = AnimationController(
      duration: AppTheme.mediumAnimation,
      vsync: this,
    );

    _expandAnimation = CurvedAnimation(
      parent: _expandController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _feedbackController.dispose();
    _expandController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Star Rating Section
        _buildRatingSection(theme),

        const SizedBox(height: AppTheme.spacingLarge),

        // Quick Tags Section
        if (widget.showQuickTags) ...[
          _buildQuickTagsSection(theme),
          const SizedBox(height: AppTheme.spacingLarge),
        ],

        // Detailed Feedback Section
        if (widget.enableDetailedFeedback) ...[
          _buildDetailedFeedbackSection(theme),
        ],
      ],
    );
  }

  /// Build star rating section
  Widget _buildRatingSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'How would you rate your experience?',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        Center(
          child: StarRatingInput(
            initialRating: _rating,
            starSize: 48,
            spacing: 8,
            readOnly: !widget.enabled,
            onChanged: widget.enabled ? _handleRatingChange : null,
          ),
        ),
        if (_rating > 0) ...[
          const SizedBox(height: AppTheme.spacingSmall),
          Center(
            child: Text(
              _getRatingDescription(_rating),
              style: theme.textTheme.bodyMedium?.copyWith(
                color: _getRatingColor(_rating),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// Build quick feedback tags section
  Widget _buildQuickTagsSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick feedback',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMedium),
        Wrap(
          spacing: AppTheme.spacingSmall,
          runSpacing: AppTheme.spacingSmall,
          children: FeedbackTag.values
              .map((tag) => _buildTagChip(
                    tag: tag,
                    theme: theme,
                  ))
              .toList(),
        ),
      ],
    );
  }

  /// Build detailed feedback section
  Widget _buildDetailedFeedbackSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Additional feedback',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            TextButton.icon(
              onPressed: widget.enabled ? _toggleDetailedFeedback : null,
              icon: AnimatedRotation(
                turns: _showDetailedFeedback ? 0.5 : 0.0,
                duration: AppTheme.shortAnimation,
                child: const Icon(Icons.expand_more),
              ),
              label: Text(_showDetailedFeedback ? 'Hide' : 'Show'),
            ),
          ],
        ),
        SizeTransition(
          sizeFactor: _expandAnimation,
          child: Column(
            children: [
              const SizedBox(height: AppTheme.spacingMedium),
              TextField(
                controller: _feedbackController,
                enabled: widget.enabled,
                maxLines: 4,
                maxLength: widget.maxFeedbackLength,
                decoration: InputDecoration(
                  hintText: 'Tell us more about your experience...',
                  border: OutlineInputBorder(
                    borderRadius:
                        BorderRadius.circular(AppTheme.borderRadiusMedium),
                  ),
                  counterText:
                      '${_feedbackText.length}/${widget.maxFeedbackLength}',
                ),
                onChanged: _handleFeedbackChange,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build individual tag chip
  Widget _buildTagChip({
    required FeedbackTag tag,
    required ThemeData theme,
  }) {
    final isSelected = _selectedTags.contains(tag);

    return FilterChip(
      label: Text(tag.displayName),
      selected: isSelected,
      onSelected: widget.enabled
          ? (selected) => _handleTagSelection(tag, selected)
          : null,
      selectedColor: tag.color.withAlpha(51), // 0.2 opacity
      checkmarkColor: tag.color,
      side: isSelected ? BorderSide(color: tag.color, width: 1.5) : null,
      labelStyle: TextStyle(
        color: isSelected ? tag.color : theme.colorScheme.onSurface,
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
      ),
    );
  }

  /// Handle rating change
  void _handleRatingChange(double rating) async {
    setState(() {
      _rating = rating;
    });

    // Trigger haptic feedback
    if (widget.enableHapticFeedback) {
      await HapticFeedback.selectionClick();
    }

    // Auto-select corresponding tag based on rating
    if (widget.showQuickTags) {
      _autoSelectTagFromRating(rating);
    }

    widget.onRatingChanged?.call(rating);
  }

  /// Handle tag selection
  void _handleTagSelection(FeedbackTag tag, bool selected) async {
    setState(() {
      if (selected) {
        _selectedTags.add(tag);
        // Auto-update rating based on tag
        _rating = tag.correspondingMood.score.toDouble();
      } else {
        _selectedTags.remove(tag);
      }
    });

    // Trigger haptic feedback
    if (widget.enableHapticFeedback) {
      await HapticFeedback.selectionClick();
    }

    widget.onTagsChanged?.call(_selectedTags);
    if (selected) {
      widget.onRatingChanged?.call(_rating);
    }
  }

  /// Handle feedback text change
  void _handleFeedbackChange(String text) {
    setState(() {
      _feedbackText = text;
    });

    widget.onFeedbackChanged?.call(text);
  }

  /// Toggle detailed feedback section
  void _toggleDetailedFeedback() async {
    setState(() {
      _showDetailedFeedback = !_showDetailedFeedback;
    });

    if (_showDetailedFeedback) {
      await _expandController.forward();
    } else {
      await _expandController.reverse();
    }

    // Trigger haptic feedback
    if (widget.enableHapticFeedback) {
      await HapticFeedback.selectionClick();
    }
  }

  /// Auto-select tag based on rating
  void _autoSelectTagFromRating(double rating) {
    _selectedTags.clear();

    if (rating >= 4.5) {
      _selectedTags.add(FeedbackTag.excellent);
    } else if (rating >= 3.5) {
      _selectedTags.add(FeedbackTag.good);
    } else if (rating >= 2.5) {
      _selectedTags.add(FeedbackTag.average);
    } else if (rating >= 1.5) {
      _selectedTags.add(FeedbackTag.poor);
    } else if (rating > 0) {
      _selectedTags.add(FeedbackTag.terrible);
    }

    widget.onTagsChanged?.call(_selectedTags);
  }

  /// Get rating description text
  String _getRatingDescription(double rating) {
    if (rating >= 4.5) return 'Excellent';
    if (rating >= 3.5) return 'Good';
    if (rating >= 2.5) return 'Average';
    if (rating >= 1.5) return 'Poor';
    if (rating > 0) return 'Terrible';
    return '';
  }

  /// Get rating color
  Color _getRatingColor(double rating) {
    if (rating >= 4.5) return const Color(0xFF4CAF50);
    if (rating >= 3.5) return const Color(0xFF8BC34A);
    if (rating >= 2.5) return const Color(0xFF9E9E9E);
    if (rating >= 1.5) return const Color(0xFFFF9800);
    if (rating > 0) return const Color(0xFFF44336);
    return Colors.grey;
  }
}
