import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A loading indicator with a message and optional progress bar
class LoadingIndicator extends StatelessWidget {
  /// The message to display
  final String? message;

  /// The progress value (0.0 to 1.0)
  final double? progress;

  /// The color of the progress indicator
  final Color color;

  /// The text color
  final Color textColor;

  /// Creates a new loading indicator
  const LoadingIndicator({
    super.key,
    this.message,
    this.progress,
    this.color = AppTheme.primaryColor,
    this.textColor = Colors.white,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (progress != null)
          SizedBox(
            width: 100,
            height: 100,
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Progress indicator
                CircularProgressIndicator(
                  value: progress,
                  valueColor: AlwaysStoppedAnimation<Color>(color),
                  strokeWidth: 8,
                ),

                // Percentage text
                Text(
                  '${(progress! * 100).toInt()}%',
                  style: TextStyle(
                    color: textColor,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          )
        else
          SizedBox(
            width: 60,
            height: 60,
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(color),
              strokeWidth: 6,
            ),
          ),
        if (message != null) ...[
          SizedBox(height: 24),

          // Message
          Text(
            message!,
            style: TextStyle(
              color: textColor,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ]
      ],
    );
  }
}
