import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/services/location_service.dart';

/// A dialog for requesting location permission
class LocationPermissionDialog extends ConsumerWidget {
  /// Constructor
  const LocationPermissionDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final locationService = ref.watch(locationServiceProvider);

    return AlertDialog(
      title: const Text('Location Permission Required'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'CultureConnect needs access to your location to show nearby experiences and provide navigation.',
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              const Expanded(
                child: Text('Find experiences near you'),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.navigation,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              const Expanded(
                child: Text('Get directions to experiences'),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.map,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              const Expanded(
                child: Text('See your location on the map'),
              ),
            ],
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop(false);
          },
          child: const Text('Not Now'),
        ),
        ElevatedButton(
          onPressed: () async {
            final granted = await locationService.requestPermission();
            if (context.mounted) {
              Navigator.of(context).pop(granted);
            }
          },
          child: const Text('Grant Permission'),
        ),
      ],
    );
  }
}

/// A dialog for when location permission is denied permanently
class LocationPermissionDeniedDialog extends StatelessWidget {
  /// Constructor
  const LocationPermissionDeniedDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Location Permission Denied'),
      content: const Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'You have denied location permission permanently. Some features will not work without location access.',
          ),
          SizedBox(height: 16),
          Text(
            'To enable location access, please go to your device settings and grant location permission to CultureConnect.',
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop(false);
          },
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () async {
            final locationService = LocationService();
            await locationService.openAppSettings();
            if (context.mounted) {
              Navigator.of(context).pop(true);
            }
          },
          child: const Text('Open Settings'),
        ),
      ],
    );
  }
}

/// A dialog for when location services are disabled
class LocationServicesDisabledDialog extends StatelessWidget {
  /// Constructor
  const LocationServicesDisabledDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Location Services Disabled'),
      content: const Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Location services are disabled on your device. Some features will not work without location services.',
          ),
          SizedBox(height: 16),
          Text(
            'To enable location services, please go to your device settings and turn on location services.',
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop(false);
          },
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () async {
            final locationService = LocationService();
            await locationService.openLocationSettings();
            if (context.mounted) {
              Navigator.of(context).pop(true);
            }
          },
          child: const Text('Open Settings'),
        ),
      ],
    );
  }
}

/// A widget that shows a banner when location permission is needed
class LocationPermissionBanner extends ConsumerWidget {
  /// Callback when the banner is dismissed
  final VoidCallback? onDismiss;

  /// Constructor
  const LocationPermissionBanner({
    super.key,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final permissionStatus = ref.watch(locationPermissionStatusProvider);

    return permissionStatus.when(
      data: (status) {
        if (status == LocationPermissionStatus.granted) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          color: _getBannerColor(status),
          child: Row(
            children: [
              Icon(
                _getBannerIcon(status),
                color: Colors.white,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  _getBannerMessage(status),
                  style: const TextStyle(color: Colors.white),
                ),
              ),
              TextButton(
                onPressed: () => _handleBannerAction(context, ref, status),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.white,
                ),
                child: Text(_getBannerActionText(status)),
              ),
              if (onDismiss != null)
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: onDismiss,
                  tooltip: 'Dismiss',
                ),
            ],
          ),
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  /// Get the banner color based on the permission status
  Color _getBannerColor(LocationPermissionStatus status) {
    switch (status) {
      case LocationPermissionStatus.denied:
      case LocationPermissionStatus.notDetermined:
        return Colors.orange;
      case LocationPermissionStatus.deniedPermanently:
      case LocationPermissionStatus.disabled:
        return Colors.red;
      case LocationPermissionStatus.granted:
        return Colors.green;
    }
  }

  /// Get the banner icon based on the permission status
  IconData _getBannerIcon(LocationPermissionStatus status) {
    switch (status) {
      case LocationPermissionStatus.denied:
      case LocationPermissionStatus.notDetermined:
        return Icons.location_off;
      case LocationPermissionStatus.deniedPermanently:
        return Icons.block;
      case LocationPermissionStatus.disabled:
        return Icons.gps_off;
      case LocationPermissionStatus.granted:
        return Icons.location_on;
    }
  }

  /// Get the banner message based on the permission status
  String _getBannerMessage(LocationPermissionStatus status) {
    switch (status) {
      case LocationPermissionStatus.denied:
      case LocationPermissionStatus.notDetermined:
        return 'Location permission is required for some features';
      case LocationPermissionStatus.deniedPermanently:
        return 'Location permission is permanently denied';
      case LocationPermissionStatus.disabled:
        return 'Location services are disabled on your device';
      case LocationPermissionStatus.granted:
        return 'Location permission granted';
    }
  }

  /// Get the banner action text based on the permission status
  String _getBannerActionText(LocationPermissionStatus status) {
    switch (status) {
      case LocationPermissionStatus.denied:
      case LocationPermissionStatus.notDetermined:
        return 'GRANT';
      case LocationPermissionStatus.deniedPermanently:
      case LocationPermissionStatus.disabled:
        return 'SETTINGS';
      case LocationPermissionStatus.granted:
        return 'OK';
    }
  }

  /// Handle the banner action based on the permission status
  Future<void> _handleBannerAction(
    BuildContext context,
    WidgetRef ref,
    LocationPermissionStatus status,
  ) async {
    final locationService = ref.read(locationServiceProvider);

    switch (status) {
      case LocationPermissionStatus.denied:
      case LocationPermissionStatus.notDetermined:
        await locationService.requestPermission();
        break;
      case LocationPermissionStatus.deniedPermanently:
        await locationService.openAppSettings();
        break;
      case LocationPermissionStatus.disabled:
        await locationService.openLocationSettings();
        break;
      case LocationPermissionStatus.granted:
        // Do nothing
        break;
    }
  }
}
