import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/loyalty/loyalty_program_model.dart';
import 'package:culture_connect/models/loyalty/loyalty_reward.dart';
import 'package:culture_connect/providers/loyalty_provider.dart';
import 'package:culture_connect/screens/loyalty/loyalty_reward_details_screen.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget that displays a list of loyalty rewards
class LoyaltyRewardsList extends ConsumerWidget {
  /// The loyalty program
  final LoyaltyProgramModel loyaltyProgram;

  /// The maximum number of rewards to display
  final int? maxRewards;

  /// Whether to show the see all button
  final bool showSeeAllButton;

  /// Callback when the see all button is tapped
  final VoidCallback? onSeeAllTapped;

  /// Whether to show only available rewards
  final bool showOnlyAvailable;

  /// Creates a new loyalty rewards list widget
  const LoyaltyRewardsList({
    super.key,
    required this.loyaltyProgram,
    this.maxRewards,
    this.showSeeAllButton = false,
    this.onSeeAllTapped,
    this.showOnlyAvailable = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final rewardsAsync = ref.watch(loyaltyRewardsProvider);

    return rewardsAsync.when(
      data: (rewards) {
        // Filter rewards based on showOnlyAvailable
        final filteredRewards = showOnlyAvailable
            ? rewards
                .where(
                    (reward) => reward.status == LoyaltyRewardStatus.available)
                .toList()
            : rewards;

        // Sort rewards by points required (lowest first)
        filteredRewards
            .sort((a, b) => a.pointsRequired.compareTo(b.pointsRequired));

        // Limit the number of rewards if maxRewards is provided
        final displayedRewards = maxRewards != null
            ? filteredRewards.take(maxRewards!).toList()
            : filteredRewards;

        if (displayedRewards.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.card_giftcard,
                  size: 48,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                const Text(
                  'No rewards available',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Check back later for new rewards',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Rewards',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  if (showSeeAllButton &&
                      filteredRewards.length > (maxRewards ?? 0))
                    TextButton(
                      onPressed: onSeeAllTapped,
                      child: const Text(
                        'See All',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                ],
              ),
            ),

            const SizedBox(height: 8),

            // Rewards list
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: displayedRewards.length,
              itemBuilder: (context, index) {
                final reward = displayedRewards[index];
                return _buildRewardItem(context, reward, ref);
              },
            ),
          ],
        );
      },
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, stackTrace) => Center(
        child: Text(
          'Error loading rewards: $error',
          style: const TextStyle(
            fontSize: 16,
            color: Colors.red,
          ),
        ),
      ),
    );
  }

  Widget _buildRewardItem(
      BuildContext context, LoyaltyReward reward, WidgetRef ref) {
    final canRedeem = loyaltyProgram.pointsBalance >= reward.pointsRequired &&
        (reward.minimumTier == null ||
            loyaltyProgram.tier.index >= reward.minimumTier!.index);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => LoyaltyRewardDetailsScreen(
                reward: reward,
                loyaltyProgram: loyaltyProgram,
              ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Reward type and points
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Reward type
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor
                          .withAlpha(26), // 0.1 * 255 = 25.5, rounded to 26
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          reward.type.icon,
                          size: 16,
                          color: AppTheme.primaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          reward.type.displayName,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Points required
                  Row(
                    children: [
                      const Icon(
                        Icons.star,
                        size: 16,
                        color: AppTheme.primaryColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${reward.pointsRequired} points',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Reward name
              Text(
                reward.name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),

              const SizedBox(height: 4),

              // Reward description
              Text(
                reward.description,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondaryColor,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 12),

              // Minimum tier and value
              Row(
                children: [
                  if (reward.minimumTier != null) ...[
                    Icon(
                      reward.minimumTier!.icon,
                      size: 16,
                      color: reward.minimumTier!.color,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${reward.minimumTier!.displayName} tier required',
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      width: 1,
                      height: 16,
                      color: Colors.grey[300],
                    ),
                    const SizedBox(width: 8),
                  ],
                  if (reward.formattedValue != null) ...[
                    const Icon(
                      Icons.monetization_on,
                      size: 16,
                      color: Colors.amber,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Value: ${reward.formattedValue}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 16),

              // Redeem button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: canRedeem
                      ? () => _redeemReward(context, reward, ref)
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    canRedeem ? 'Redeem Reward' : 'Not Enough Points',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _redeemReward(
      BuildContext context, LoyaltyReward reward, WidgetRef ref) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Redeem Reward'),
        content: Text(
          'Are you sure you want to redeem ${reward.name} for ${reward.pointsRequired} points?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Redeem'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // Show loading indicator
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Processing redemption...'),
              duration: Duration(seconds: 1),
            ),
          );
        }

        // Redeem the reward
        await ref
            .read(redeemLoyaltyRewardNotifierProvider.notifier)
            .redeemReward(reward.id);

        if (context.mounted) {
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Successfully redeemed ${reward.name}'),
              backgroundColor: Colors.green,
            ),
          );

          // Navigate to reward details screen
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => LoyaltyRewardDetailsScreen(
                reward: reward.copyWith(
                  status: LoyaltyRewardStatus.redeemed,
                  redemptionDate: DateTime.now(),
                ),
                loyaltyProgram: loyaltyProgram.copyWith(
                  pointsBalance:
                      loyaltyProgram.pointsBalance - reward.pointsRequired,
                ),
              ),
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error redeeming reward: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        // Reset the notifier
        ref.read(redeemLoyaltyRewardNotifierProvider.notifier).reset();
      }
    }
  }
}
