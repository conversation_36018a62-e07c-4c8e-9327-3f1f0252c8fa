import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/loyalty/loyalty_program_model.dart';

/// A widget that displays a loyalty tier card
class LoyaltyTierCard extends ConsumerWidget {
  /// The loyalty program
  final LoyaltyProgramModel loyaltyProgram;

  /// Whether to show the progress bar
  final bool showProgressBar;

  /// Whether to show the next tier
  final bool showNextTier;

  /// Whether to show the benefits
  final bool showBenefits;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Creates a new loyalty tier card
  const LoyaltyTierCard({
    super.key,
    required this.loyaltyProgram,
    this.showProgressBar = true,
    this.showNextTier = true,
    this.showBenefits = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentTier = loyaltyProgram.tier;
    final nextTier = loyaltyProgram.nextTier;
    final progressToNextTier = loyaltyProgram.progressToNextTier;
    final pointsToNextTier = loyaltyProgram.pointsToNextTier;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                currentTier.color.withAlpha(204), // 0.8 * 255 = 204
                currentTier.color.withAlpha(102), // 0.4 * 255 = 102
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Tier badge and points
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Tier badge
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.white.withAlpha(
                            128), // 0.5 * 255 = 127.5, rounded to 128
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          currentTier.icon,
                          size: 16,
                          color: Colors.white,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          currentTier.displayName,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Points
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '${loyaltyProgram.pointsBalance}',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        'Points',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white.withAlpha(204), // 0.8 * 255 = 204
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Program name
              Text(
                loyaltyProgram.name,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),

              const SizedBox(height: 4),

              // Member since
              Text(
                'Member since ${loyaltyProgram.enrollmentDate.year}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white.withAlpha(204), // 0.8 * 255 = 204
                ),
              ),

              // Progress to next tier
              if (showProgressBar &&
                  nextTier != null &&
                  progressToNextTier != null &&
                  pointsToNextTier != null) ...[
                const SizedBox(height: 16),

                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Progress to ${nextTier.displayName}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white.withAlpha(204), // 0.8 * 255 = 204
                      ),
                    ),
                    Text(
                      '$pointsToNextTier points to go',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // Progress bar
                ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: LinearProgressIndicator(
                    value: progressToNextTier,
                    backgroundColor:
                        Colors.white.withAlpha(51), // 0.2 * 255 = 51
                    valueColor:
                        const AlwaysStoppedAnimation<Color>(Colors.white),
                    minHeight: 8,
                  ),
                ),
              ],

              // Next tier info
              if (showNextTier && nextTier != null) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white
                        .withAlpha(26), // 0.1 * 255 = 25.5, rounded to 26
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.arrow_upward,
                        size: 20,
                        color: Colors.white,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Next Tier: ${nextTier.displayName}',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Earn ${pointsToNextTier ?? 0} more points to unlock ${nextTier.discountPercentage}% discount and more benefits!',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.white
                                    .withAlpha(204), // 0.8 * 255 = 204
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Benefits
              if (showBenefits) ...[
                const SizedBox(height: 16),
                const Text(
                  'Your Benefits',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                ...currentTier.benefits.map((benefit) => Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Icon(
                            Icons.check_circle,
                            size: 16,
                            color: Colors.white,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              benefit,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.white
                                    .withAlpha(204), // 0.8 * 255 = 204
                              ),
                            ),
                          ),
                        ],
                      ),
                    )),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
