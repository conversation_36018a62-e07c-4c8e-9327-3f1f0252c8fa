import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/providers/message_translation_provider.dart';
import 'package:culture_connect/providers/voice_translation_enhanced_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for message translation settings
class MessageTranslationSettings extends ConsumerWidget {
  /// Creates a new message translation settings widget
  const MessageTranslationSettings({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final autoTranslate = ref.watch(autoTranslateMessagesProvider);
    final targetLanguage = ref.watch(messageTranslationTargetLanguageProvider);
    final useOfflineMode = ref.watch(useOfflineModeProvider);
    final useDialectRecognition = ref.watch(useDialectRecognitionProvider);
    final useCustomVocabulary = ref.watch(useCustomVocabularyProvider);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          const Text(
            'Translation Settings',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),

          const SizedBox(height: 16),

          // Auto-translate toggle
          SwitchListTile(
            title: const Text(
              'Auto-translate incoming messages',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            subtitle: const Text(
              'Automatically translate messages in other languages',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            value: autoTranslate,
            onChanged: (value) {
              ref.read(autoTranslateMessagesProvider.notifier).state = value;
            },
            activeColor: AppTheme.primaryColor,
          ),

          const SizedBox(height: 16),

          // Target language selector
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Translate to:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                _buildLanguageSelector(ref, targetLanguage),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Cultural Context Settings
          ListTile(
            title: const Text(
              'Cultural Context Settings',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            subtitle: const Text(
              'Manage cultural context awareness and settings',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.pushNamed(context, '/cultural_context_settings');
            },
          ),

          const SizedBox(height: 16),

          // Slang & Idiom Settings
          ListTile(
            title: const Text(
              'Slang & Idiom Settings',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            subtitle: const Text(
              'Manage slang and idiom detection settings',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.pushNamed(context, '/slang_idiom_settings');
            },
          ),

          const SizedBox(height: 16),

          // Pronunciation Settings
          ListTile(
            title: const Text(
              'Pronunciation Settings',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            subtitle: const Text(
              'Manage pronunciation guidance settings',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.pushNamed(context, '/pronunciation_settings');
            },
          ),

          const SizedBox(height: 16),

          // Advanced settings
          ExpansionTile(
            title: const Text(
              'Advanced Settings',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            children: [
              // Offline mode
              SwitchListTile(
                title: const Text(
                  'Offline Mode',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                subtitle: const Text(
                  'Use downloaded language packs when available',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
                value: useOfflineMode,
                onChanged: (value) {
                  // Update offline mode setting
                  ref.read(useOfflineModeProvider.notifier).state = value;
                },
                activeColor: AppTheme.primaryColor,
                dense: true,
              ),

              // Dialect recognition
              SwitchListTile(
                title: const Text(
                  'Dialect Recognition',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                subtitle: const Text(
                  'Recognize and adapt to regional dialects',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
                value: useDialectRecognition,
                onChanged: (value) {
                  // Update dialect recognition setting
                  ref.read(useDialectRecognitionProvider.notifier).state =
                      value;
                },
                activeColor: AppTheme.primaryColor,
                dense: true,
              ),

              // Custom vocabulary
              SwitchListTile(
                title: const Text(
                  'Custom Vocabulary',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                subtitle: const Text(
                  'Use your custom vocabulary for translations',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
                value: useCustomVocabulary,
                onChanged: (value) {
                  // Update custom vocabulary setting
                  ref.read(useCustomVocabularyProvider.notifier).state = value;
                },
                activeColor: AppTheme.primaryColor,
                dense: true,
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () {
                  ref
                      .read(messageTranslationNotifierProvider.notifier)
                      .clearCache();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Translation cache cleared'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
                child: const Text(
                  'Clear Cache',
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 14,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text(
                  'Done',
                  style: TextStyle(
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build the language selector
  Widget _buildLanguageSelector(WidgetRef ref, LanguageModel currentLanguage) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButton<LanguageModel>(
        value: currentLanguage,
        isExpanded: true,
        underline: const SizedBox(),
        onChanged: (LanguageModel? newValue) {
          if (newValue != null) {
            ref.read(messageTranslationTargetLanguageProvider.notifier).state =
                newValue;
          }
        },
        items: supportedLanguages
            .map<DropdownMenuItem<LanguageModel>>((LanguageModel language) {
          return DropdownMenuItem<LanguageModel>(
            value: language,
            child: Text(
              language.name,
              style: const TextStyle(
                fontSize: 14,
                color: AppTheme.textPrimaryColor,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
