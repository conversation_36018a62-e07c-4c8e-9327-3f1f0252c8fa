import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/message_model.dart';

import 'package:culture_connect/theme/app_theme.dart';

/// A widget for toggling message translation
class MessageTranslationToggle extends ConsumerWidget {
  /// The message to translate
  final MessageModel message;

  /// Whether the message is currently translated
  final bool isTranslated;

  /// Whether to show the original text
  final bool showOriginal;

  /// Callback when the translation is toggled
  final Function(bool) onToggle;

  /// Whether to use light colors
  final bool useLight;

  /// Creates a new message translation toggle
  const MessageTranslationToggle({
    super.key,
    required this.message,
    required this.isTranslated,
    this.showOriginal = false,
    required this.onToggle,
    this.useLight = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final primaryColor = useLight ? Colors.white : AppTheme.primaryColor;
    final secondaryColor = useLight ? Colors.white.withAlpha(150) : Colors.grey;

    return GestureDetector(
      onTap: () => onToggle(!showOriginal),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: useLight
              ? Colors.white.withAlpha(50)
              : (isTranslated
                  ? AppTheme.primaryColor.withAlpha(25)
                  : Colors.grey.withAlpha(25)),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              showOriginal ? Icons.translate_outlined : Icons.translate,
              size: 14,
              color: showOriginal ? secondaryColor : primaryColor,
            ),
            const SizedBox(width: 4),
            Text(
              showOriginal ? 'Show Translation' : 'Show Original',
              style: TextStyle(
                fontSize: 12,
                color: showOriginal ? secondaryColor : primaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
