import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/payment/payment_method_model.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/payment/credit_card_form.dart';

/// A bottom sheet for adding a new payment method
class AddPaymentMethodSheet extends ConsumerStatefulWidget {
  /// Callback when a payment method is added
  final Function(PaymentMethodModel) onPaymentMethodAdded;

  /// Creates a new add payment method sheet
  const AddPaymentMethodSheet({
    super.key,
    required this.onPaymentMethodAdded,
  });

  @override
  ConsumerState<AddPaymentMethodSheet> createState() =>
      _AddPaymentMethodSheetState();
}

class _AddPaymentMethodSheetState extends ConsumerState<AddPaymentMethodSheet> {
  PaymentMethodType _selectedType = PaymentMethodType.creditCard;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16),
        ),
      ),
      child: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Add Payment Method',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              SizedBox(height: 24),

              // Payment method type selection
              Text(
                'Select Payment Method Type',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 16),
              _buildPaymentMethodTypeSelector(),
              SizedBox(height: 24),

              // Payment method form
              _buildPaymentMethodForm(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentMethodTypeSelector() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildPaymentTypeOption(
              PaymentMethodType.creditCard, Icons.credit_card),
          _buildPaymentTypeOption(PaymentMethodType.paypal, Icons.payment),
          _buildPaymentTypeOption(PaymentMethodType.applePay, Icons.apple),
          _buildPaymentTypeOption(
              PaymentMethodType.googlePay, Icons.g_mobiledata),
          _buildPaymentTypeOption(
              PaymentMethodType.bankTransfer, Icons.account_balance),
        ],
      ),
    );
  }

  Widget _buildPaymentTypeOption(PaymentMethodType type, IconData icon) {
    final isSelected = _selectedType == type;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedType = type;
        });
      },
      child: Container(
        width: 80,
        margin: EdgeInsets.only(right: 12),
        padding: EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryColor : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : Colors.grey,
              size: 24,
            ),
            SizedBox(height: 8),
            Text(
              type.displayName,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.black,
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodForm() {
    switch (_selectedType) {
      case PaymentMethodType.creditCard:
        return CreditCardForm(
          onSubmit: _addCreditCard,
          isLoading: _isLoading,
        );
      case PaymentMethodType.paypal:
        return _buildDigitalWalletForm('PayPal');
      case PaymentMethodType.applePay:
        return _buildDigitalWalletForm('Apple Pay');
      case PaymentMethodType.googlePay:
        return _buildDigitalWalletForm('Google Pay');
      case PaymentMethodType.bankTransfer:
        return _buildBankAccountForm();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildDigitalWalletForm(String walletName) {
    // Placeholder for digital wallet form
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 24),
        child: Column(
          children: [
            Icon(
              Icons.account_balance_wallet,
              size: 48,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              '$walletName integration coming soon',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'This payment method will be available in a future update.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBankAccountForm() {
    // Placeholder for bank account form
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 24),
        child: Column(
          children: [
            Icon(
              Icons.account_balance,
              size: 48,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'Bank Account integration coming soon',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'This payment method will be available in a future update.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _addCreditCard({
    required String cardNumber,
    required String cardHolderName,
    required int expiryMonth,
    required int expiryYear,
    required String cvv,
    required bool setAsDefault,
  }) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Implement payment method addition with enhanced payment service
      // For now, create a mock payment method for UI demonstration

      // Format the card number for display
      final formattedCardNumber = cardNumber.replaceAll(' ', '');
      final last4 =
          formattedCardNumber.substring(formattedCardNumber.length - 4);
      final brand = _getCardBrand(formattedCardNumber);

      // Create a mock payment method
      final paymentMethod = PaymentMethodModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: PaymentMethodType.creditCard,
        name: '$brand ending in $last4',
        isDefault: setAsDefault,
        details: {'last4': last4, 'brand': brand},
      );

      // Call the callback
      widget.onPaymentMethodAdded(paymentMethod);

      // Close the sheet
      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding payment method: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _getCardBrand(String cardNumber) {
    // Basic card brand detection
    if (cardNumber.startsWith('4')) {
      return 'Visa';
    } else if (cardNumber.startsWith(RegExp(r'5[1-5]'))) {
      return 'Mastercard';
    } else if (cardNumber.startsWith(RegExp(r'3[47]'))) {
      return 'American Express';
    } else if (cardNumber.startsWith(RegExp(r'6(?:011|5[0-9]{2})'))) {
      return 'Discover';
    }
    return 'Card';
  }
}
