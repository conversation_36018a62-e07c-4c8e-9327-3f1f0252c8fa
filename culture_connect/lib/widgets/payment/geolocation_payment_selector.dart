import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Widget for geolocation-aware payment method selection
class GeolocationPaymentSelector extends StatefulWidget {
  final GeolocationData? geolocationData;
  final List<PaymentMethodType> availablePaymentMethods;
  final PaymentMethodType? selectedPaymentMethod;
  final Function(PaymentMethodType) onPaymentMethodSelected;
  final bool isLoading;

  const GeolocationPaymentSelector({
    super.key,
    this.geolocationData,
    required this.availablePaymentMethods,
    this.selectedPaymentMethod,
    required this.onPaymentMethodSelected,
    this.isLoading = false,
  });

  @override
  State<GeolocationPaymentSelector> createState() =>
      _GeolocationPaymentSelectorState();
}

class _GeolocationPaymentSelectorState extends State<GeolocationPaymentSelector>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(
      duration: AppTheme.mediumAnimation,
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: AppTheme.shortAnimation,
      vsync: this,
    );

    _slideController.forward();
    _fadeController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Location indicator
        if (widget.geolocationData != null)
          _buildLocationIndicator(theme)
              .animate(controller: _fadeController)
              .fadeIn()
              .slideY(begin: -0.2, end: 0),

        const SizedBox(height: 16),

        // Payment methods header
        Text(
          'Choose Payment Method',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),

        const SizedBox(height: 8),

        // Payment methods list
        if (widget.isLoading)
          _buildLoadingState(theme)
        else
          _buildPaymentMethodsList(theme),
      ],
    );
  }

  /// Build location indicator widget
  Widget _buildLocationIndicator(ThemeData theme) {
    final location = widget.geolocationData!;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withAlpha(51),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.primary.withAlpha(77),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.location_on,
            color: theme.colorScheme.primary,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Payment optimized for your location',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '${location.city}, ${location.countryName}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withAlpha(179),
                  ),
                ),
              ],
            ),
          ),
          if (location.isVpnDetected)
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 2,
              ),
              decoration: BoxDecoration(
                color: theme.colorScheme.error.withAlpha(26),
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
              ),
              child: Text(
                'VPN',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.error,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Build loading state
  Widget _buildLoadingState(ThemeData theme) {
    return Column(
      children: List.generate(3, (index) {
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          height: 72,
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
          ),
        ).animate(delay: Duration(milliseconds: index * 100)).shimmer(
              duration: const Duration(milliseconds: 1200),
              color: theme.colorScheme.onSurface.withAlpha(26),
            );
      }),
    );
  }

  /// Build payment methods list
  Widget _buildPaymentMethodsList(ThemeData theme) {
    return Column(
      children: widget.availablePaymentMethods.asMap().entries.map((entry) {
        final index = entry.key;
        final method = entry.value;
        final isRecommended = _isRecommendedMethod(method);
        final isSelected = widget.selectedPaymentMethod == method;

        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: _buildPaymentMethodCard(
            theme: theme,
            method: method,
            isRecommended: isRecommended,
            isSelected: isSelected,
            onTap: () => _onMethodSelected(method),
          ),
        )
            .animate(delay: Duration(milliseconds: index * 100))
            .slideX(begin: 0.3, end: 0)
            .fadeIn();
      }).toList(),
    );
  }

  /// Build individual payment method card
  Widget _buildPaymentMethodCard({
    required ThemeData theme,
    required PaymentMethodType method,
    required bool isRecommended,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        child: AnimatedContainer(
          duration: AppTheme.shortAnimation,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isSelected
                ? theme.colorScheme.primaryContainer.withAlpha(77)
                : theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
            border: Border.all(
              color: isSelected
                  ? theme.colorScheme.primary
                  : isRecommended
                      ? theme.colorScheme.primary.withAlpha(77)
                      : theme.colorScheme.outline.withAlpha(51),
              width: isSelected ? 2 : 1,
            ),
            boxShadow:
                isSelected ? AppTheme.mediumShadow : AppTheme.smallShadow,
          ),
          child: Row(
            children: [
              // Payment method icon
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: _getMethodColor(method, theme).withAlpha(26),
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: Icon(
                  _getMethodIcon(method),
                  color: _getMethodColor(method, theme),
                  size: 24,
                ),
              ),

              const SizedBox(width: 16),

              // Method details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          _getMethodTitle(method),
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        if (isRecommended) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primary,
                              borderRadius: BorderRadius.circular(
                                  AppTheme.borderRadiusSmall),
                            ),
                            child: Text(
                              'Recommended',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 2),
                    Text(
                      _getMethodDescription(method),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withAlpha(179),
                      ),
                    ),
                  ],
                ),
              ),

              // Selection indicator
              AnimatedContainer(
                duration: AppTheme.shortAnimation,
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isSelected
                      ? theme.colorScheme.primary
                      : Colors.transparent,
                  border: Border.all(
                    color: isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.outline.withAlpha(128),
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 16,
                      )
                    : null,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Handle method selection with haptic feedback
  void _onMethodSelected(PaymentMethodType method) {
    HapticFeedback.lightImpact();
    widget.onPaymentMethodSelected(method);
  }

  /// Check if method is recommended for current location
  bool _isRecommendedMethod(PaymentMethodType method) {
    final location = widget.geolocationData;
    if (location == null) return false;

    if (location.isAfricanRegion) {
      return method == PaymentMethodType.card ||
          method == PaymentMethodType.bankTransfer ||
          method == PaymentMethodType.ussd;
    } else {
      return method == PaymentMethodType.card;
    }
  }

  /// Get method icon
  IconData _getMethodIcon(PaymentMethodType method) {
    switch (method) {
      case PaymentMethodType.card:
        return Icons.credit_card;
      case PaymentMethodType.bankTransfer:
        return Icons.account_balance;
      case PaymentMethodType.ussd:
        return Icons.phone;
      case PaymentMethodType.mobileMoney:
        return Icons.mobile_friendly;
      case PaymentMethodType.crypto:
        return Icons.currency_bitcoin;
    }
  }

  /// Get method color
  Color _getMethodColor(PaymentMethodType method, ThemeData theme) {
    switch (method) {
      case PaymentMethodType.card:
        return theme.colorScheme.primary;
      case PaymentMethodType.bankTransfer:
        return theme.colorScheme.secondary;
      case PaymentMethodType.ussd:
        return AppTheme.successColor;
      case PaymentMethodType.mobileMoney:
        return AppTheme.warningColor;
      case PaymentMethodType.crypto:
        return AppTheme.infoColor;
    }
  }

  /// Get method title
  String _getMethodTitle(PaymentMethodType method) {
    switch (method) {
      case PaymentMethodType.card:
        return 'Credit/Debit Card';
      case PaymentMethodType.bankTransfer:
        return 'Bank Transfer';
      case PaymentMethodType.ussd:
        return 'USSD';
      case PaymentMethodType.mobileMoney:
        return 'Mobile Money';
      case PaymentMethodType.crypto:
        return 'Cryptocurrency';
    }
  }

  /// Get method description
  String _getMethodDescription(PaymentMethodType method) {
    final location = widget.geolocationData;

    switch (method) {
      case PaymentMethodType.card:
        return location?.isAfricanRegion == true
            ? 'Fast and secure card payments • Optimized for African markets'
            : 'Global payment processing • Multi-currency support';
      case PaymentMethodType.bankTransfer:
        return 'Direct bank transfer • Secure and reliable';
      case PaymentMethodType.ussd:
        return 'Pay with your mobile phone • No internet required';
      case PaymentMethodType.mobileMoney:
        return 'Mobile wallet payments • Quick and convenient';
      case PaymentMethodType.crypto:
        return 'Bitcoin, Ethereum, USDT, USDC • Decentralized payments';
    }
  }
}
