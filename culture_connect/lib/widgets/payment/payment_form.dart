import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/payment/card_payment_method_model.dart';
import 'package:culture_connect/models/payment/payment_method_model.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget that displays a payment form
class PaymentForm extends ConsumerStatefulWidget {
  /// Whether to save the payment method
  final bool savePaymentMethod;

  /// Whether to show the save payment method option
  final bool showSaveOption;

  /// Callback when the payment method is created
  final Function(PaymentMethodModel) onPaymentMethodCreated;

  /// Creates a new payment form
  const PaymentForm({
    super.key,
    this.savePaymentMethod = false,
    this.showSaveOption = true,
    required this.onPaymentMethodCreated,
  });

  @override
  ConsumerState<PaymentForm> createState() => _PaymentFormState();
}

class _PaymentFormState extends ConsumerState<PaymentForm> {
  final _formKey = GlobalKey<FormState>();
  final _cardNumberController = TextEditingController();
  final _cardHolderNameController = TextEditingController();
  final _expiryDateController = TextEditingController();
  final _cvvController = TextEditingController();

  bool _saveCard = false;
  CardType _cardType = CardType.unknown;

  @override
  void initState() {
    super.initState();
    _saveCard = widget.savePaymentMethod;

    // Add listeners to detect card type
    _cardNumberController.addListener(_updateCardType);
  }

  @override
  void dispose() {
    _cardNumberController.dispose();
    _cardHolderNameController.dispose();
    _expiryDateController.dispose();
    _cvvController.dispose();
    super.dispose();
  }

  void _updateCardType() {
    final cardNumber = _cardNumberController.text;
    setState(() {
      _cardType = CardPaymentMethodModel.detectCardType(cardNumber);
    });
  }

  Future<void> _submitForm() async {
    if (!mounted) return;

    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Parse expiry date
    final expiryParts = _expiryDateController.text.split('/');
    if (expiryParts.length != 2) {
      final scaffoldMessenger = ScaffoldMessenger.of(context);
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Invalid expiry date format'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final expiryMonth = int.tryParse(expiryParts[0]);
    final expiryYear = int.tryParse('20${expiryParts[1]}');

    if (expiryMonth == null || expiryYear == null) {
      final scaffoldMessenger = ScaffoldMessenger.of(context);
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Invalid expiry date'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Create a masked card number
    final cardNumber = _cardNumberController.text.replaceAll(' ', '');
    final last4 = cardNumber.substring(cardNumber.length - 4);
    final maskedCardNumber = '**** **** **** $last4';

    // Create a payment method
    final paymentMethod = CardPaymentMethodModel(
      id: '',
      type: PaymentMethodType.creditCard,
      name: '${_cardType.displayName} ending in $last4',
      maskedCardNumber: maskedCardNumber,
      cardHolderName: _cardHolderNameController.text,
      expirationMonth: expiryMonth,
      expirationYear: expiryYear,
      cardType: _cardType,
      isDefault: _saveCard,
    );

    try {
      // TODO: Implement payment method saving with enhanced payment service
      // For now, just pass the payment method to the callback
      if (mounted) {
        widget.onPaymentMethodCreated(paymentMethod);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving payment method: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Card number field
          const Text(
            'Card Number',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: _cardNumberController,
            decoration: InputDecoration(
              hintText: '1234 5678 9012 3456',
              prefixIcon: const Icon(Icons.credit_card),
              suffixIcon: _cardType != CardType.unknown
                  ? Padding(
                      padding: EdgeInsets.all(12),
                      child: Text(
                        _cardType.displayName,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(16),
              _CardNumberFormatter(),
            ],
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a card number';
              }

              final cardNumber = value.replaceAll(' ', '');
              if (cardNumber.length < 13 || cardNumber.length > 19) {
                return 'Card number must be between 13 and 19 digits';
              }

              if (!CardPaymentMethodModel.validateCardNumber(cardNumber)) {
                return 'Invalid card number';
              }

              return null;
            },
          ),

          const SizedBox(height: 16),

          // Card holder name field
          const Text(
            'Cardholder Name',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: _cardHolderNameController,
            decoration: InputDecoration(
              hintText: 'John Doe',
              prefixIcon: const Icon(Icons.person),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            textCapitalization: TextCapitalization.words,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter the cardholder name';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // Expiry date and CVV fields
          Row(
            children: [
              // Expiry date field
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Expiry Date',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: _expiryDateController,
                      decoration: InputDecoration(
                        hintText: 'MM/YY',
                        prefixIcon: const Icon(Icons.calendar_today),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(4),
                        _ExpiryDateFormatter(),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter expiry date';
                        }

                        if (value.length < 5) {
                          return 'Invalid format';
                        }

                        final parts = value.split('/');
                        if (parts.length != 2) {
                          return 'Invalid format';
                        }

                        final month = int.tryParse(parts[0]);
                        final year = int.tryParse('20${parts[1]}');

                        if (month == null || year == null) {
                          return 'Invalid date';
                        }

                        if (month < 1 || month > 12) {
                          return 'Invalid month';
                        }

                        final now = DateTime.now();
                        final currentYear = now.year;
                        final currentMonth = now.month;

                        if (year < currentYear ||
                            (year == currentYear && month < currentMonth)) {
                          return 'Card expired';
                        }

                        return null;
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 16),

              // CVV field
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'CVV',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: _cvvController,
                      decoration: InputDecoration(
                        hintText: '123',
                        prefixIcon: const Icon(Icons.security),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(4),
                      ],
                      obscureText: true,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter CVV';
                        }

                        if (value.length < 3) {
                          return 'Invalid CVV';
                        }

                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Save card option
          if (widget.showSaveOption)
            Row(
              children: [
                Checkbox(
                  value: _saveCard,
                  onChanged: (value) {
                    setState(() {
                      _saveCard = value ?? false;
                    });
                  },
                  activeColor: AppTheme.primaryColor,
                ),
                const Text(
                  'Save this card for future payments',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
              ],
            ),

          const SizedBox(height: 24),

          // Submit button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _submitForm,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Continue',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Formatter for card number input
class _CardNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;

    if (text.isEmpty) {
      return newValue;
    }

    // Remove all spaces
    final digitsOnly = text.replaceAll(' ', '');

    // Add a space after every 4 digits
    final buffer = StringBuffer();
    for (int i = 0; i < digitsOnly.length; i++) {
      buffer.write(digitsOnly[i]);
      if ((i + 1) % 4 == 0 && i != digitsOnly.length - 1) {
        buffer.write(' ');
      }
    }

    final formattedText = buffer.toString();

    return TextEditingValue(
      text: formattedText,
      selection: TextSelection.collapsed(offset: formattedText.length),
    );
  }
}

/// Formatter for expiry date input
class _ExpiryDateFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;

    if (text.isEmpty) {
      return newValue;
    }

    // Remove all non-digit characters
    final digitsOnly = text.replaceAll(RegExp(r'\D'), '');

    // Format as MM/YY
    final buffer = StringBuffer();
    for (int i = 0; i < digitsOnly.length; i++) {
      buffer.write(digitsOnly[i]);
      if (i == 1 && i != digitsOnly.length - 1) {
        buffer.write('/');
      }
    }

    final formattedText = buffer.toString();

    return TextEditingValue(
      text: formattedText,
      selection: TextSelection.collapsed(offset: formattedText.length),
    );
  }
}
