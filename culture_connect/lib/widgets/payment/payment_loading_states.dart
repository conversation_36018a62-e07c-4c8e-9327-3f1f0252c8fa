import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import 'package:culture_connect/theme/app_theme.dart';

/// Beautiful loading states for payment operations with skeleton screens
class PaymentLoadingStates extends StatefulWidget {
  final PaymentLoadingType type;
  final String? message;
  final double? progress;
  final Duration? timeout;
  final VoidCallback? onTimeout;

  const PaymentLoadingStates({
    super.key,
    required this.type,
    this.message,
    this.progress,
    this.timeout,
    this.onTimeout,
  });

  @override
  State<PaymentLoadingStates> createState() => _PaymentLoadingStatesState();
}

class _PaymentLoadingStatesState extends State<PaymentLoadingStates>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _shimmerController;
  late AnimationController _progressController;

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _progressController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _startAnimations();
    _setupTimeout();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _shimmerController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  void _startAnimations() {
    _pulseController.repeat();
    _shimmerController.repeat();

    if (widget.progress != null) {
      _progressController.animateTo(widget.progress!);
    }
  }

  void _setupTimeout() {
    if (widget.timeout != null) {
      Future.delayed(widget.timeout!, () {
        if (mounted) {
          widget.onTimeout?.call();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    switch (widget.type) {
      case PaymentLoadingType.initialization:
        return _buildInitializationLoading(theme);
      case PaymentLoadingType.processing:
        return _buildProcessingLoading(theme);
      case PaymentLoadingType.verification:
        return _buildVerificationLoading(theme);
      case PaymentLoadingType.skeleton:
        return _buildSkeletonLoading(theme);
      case PaymentLoadingType.progress:
        return _buildProgressLoading(theme);
    }
  }

  /// Build initialization loading state
  Widget _buildInitializationLoading(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Animated payment icon
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: theme.colorScheme.primaryContainer.withAlpha(51),
            ),
            child: Icon(
              Icons.payment,
              size: 40,
              color: theme.colorScheme.primary,
            ),
          )
              .animate(controller: _pulseController)
              .scale(begin: const Offset(1.0, 1.0), end: const Offset(1.1, 1.1))
              .then()
              .scale(
                  begin: const Offset(1.1, 1.1), end: const Offset(1.0, 1.0)),

          const SizedBox(height: 24),

          // Loading indicator
          SizedBox(
            width: 32,
            height: 32,
            child: CircularProgressIndicator(
              strokeWidth: 3,
              valueColor: AlwaysStoppedAnimation<Color>(
                theme.colorScheme.primary,
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Message
          Text(
            widget.message ?? 'Initializing payment...',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 8),

          Text(
            'Setting up secure payment environment',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(179),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build processing loading state
  Widget _buildProcessingLoading(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Processing animation
          Stack(
            alignment: Alignment.center,
            children: [
              // Outer ring
              SizedBox(
                width: 100,
                height: 100,
                child: CircularProgressIndicator(
                  strokeWidth: 4,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.primary.withAlpha(77),
                  ),
                ),
              ),

              // Inner ring
              SizedBox(
                width: 60,
                height: 60,
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.primary,
                  ),
                ),
              ),

              // Center icon
              Icon(
                Icons.lock,
                size: 24,
                color: theme.colorScheme.primary,
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Message
          Text(
            widget.message ?? 'Processing payment...',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 8),

          Text(
            'Please do not close this window',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(179),
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Security indicators
          _buildSecurityIndicators(theme),
        ],
      ),
    );
  }

  /// Build verification loading state
  Widget _buildVerificationLoading(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Verification icon with animation
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppTheme.successColor.withAlpha(26),
              border: Border.all(
                color: AppTheme.successColor.withAlpha(77),
                width: 2,
              ),
            ),
            child: const Icon(
              Icons.verified_user,
              size: 40,
              color: AppTheme.successColor,
            ),
          )
              .animate(controller: _pulseController)
              .scale(
                  begin: const Offset(1.0, 1.0), end: const Offset(1.05, 1.05))
              .then()
              .scale(
                  begin: const Offset(1.05, 1.05), end: const Offset(1.0, 1.0)),

          const SizedBox(height: 24),

          // Verification steps
          Column(
            children: [
              _buildVerificationStep(
                theme,
                'Payment submitted',
                true,
                Icons.check_circle,
              ),
              _buildVerificationStep(
                theme,
                'Verifying transaction',
                false,
                Icons.hourglass_empty,
              ),
              _buildVerificationStep(
                theme,
                'Confirming with bank',
                false,
                Icons.account_balance,
              ),
            ],
          ),

          const SizedBox(height: 16),

          Text(
            widget.message ?? 'Verifying payment...',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build skeleton loading state
  Widget _buildSkeletonLoading(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Header skeleton
          _buildSkeletonBox(theme, height: 24, width: 200),
          const SizedBox(height: 16),

          // Payment method skeletons
          ...List.generate(
              3,
              (index) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: _buildPaymentMethodSkeleton(theme),
                  )),

          const SizedBox(height: 24),

          // Button skeleton
          _buildSkeletonBox(theme, height: 48, width: double.infinity),
        ],
      ),
    );
  }

  /// Build progress loading state
  Widget _buildProgressLoading(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Progress indicator
          LinearProgressIndicator(
            value: widget.progress,
            backgroundColor: theme.colorScheme.surfaceContainerHighest,
            valueColor: AlwaysStoppedAnimation<Color>(
              theme.colorScheme.primary,
            ),
          ),

          const SizedBox(height: 8),

          // Progress text
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                widget.message ?? 'Loading...',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withAlpha(179),
                ),
              ),
              if (widget.progress != null)
                Text(
                  '${(widget.progress! * 100).toInt()}%',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withAlpha(179),
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build security indicators
  Widget _buildSecurityIndicators(ThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildSecurityBadge(theme, Icons.security, '256-bit SSL'),
        const SizedBox(width: 16),
        _buildSecurityBadge(theme, Icons.verified, 'PCI Compliant'),
      ],
    );
  }

  /// Build security badge
  Widget _buildSecurityBadge(ThemeData theme, IconData icon, String label) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: AppTheme.successColor,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(179),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// Build verification step
  Widget _buildVerificationStep(
    ThemeData theme,
    String label,
    bool isCompleted,
    IconData icon,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            isCompleted ? Icons.check_circle : icon,
            size: 20,
            color: isCompleted
                ? AppTheme.successColor
                : theme.colorScheme.onSurface.withAlpha(128),
          ),
          const SizedBox(width: 8),
          Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isCompleted
                  ? theme.colorScheme.onSurface
                  : theme.colorScheme.onSurface.withAlpha(179),
              fontWeight: isCompleted ? FontWeight.w500 : FontWeight.normal,
            ),
          ),
          if (!isCompleted) ...[
            const SizedBox(width: 8),
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.primary,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build skeleton box with shimmer effect
  Widget _buildSkeletonBox(ThemeData theme,
      {required double height, double? width}) {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      ),
    ).animate(controller: _shimmerController).shimmer(
          duration: const Duration(milliseconds: 1500),
          color: theme.colorScheme.onSurface.withAlpha(26),
        );
  }

  /// Build payment method skeleton
  Widget _buildPaymentMethodSkeleton(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.outline.withAlpha(51),
        ),
      ),
      child: Row(
        children: [
          _buildSkeletonBox(theme, height: 48, width: 48),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSkeletonBox(theme, height: 16, width: 120),
                const SizedBox(height: 8),
                _buildSkeletonBox(theme, height: 12, width: 200),
              ],
            ),
          ),
          _buildSkeletonBox(theme, height: 24, width: 24),
        ],
      ),
    );
  }
}

/// Types of payment loading states
enum PaymentLoadingType {
  initialization,
  processing,
  verification,
  skeleton,
  progress,
}
