import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/payment/payment_method_model.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/payment/add_payment_method_sheet.dart';

/// A widget for selecting a payment method
class PaymentMethodSelection extends ConsumerStatefulWidget {
  /// The initially selected payment method ID
  final String? initialSelectedId;

  /// Callback when a payment method is selected
  final Function(PaymentMethodModel) onPaymentMethodSelected;

  /// Whether to show the add payment method button
  final bool showAddButton;

  /// Creates a new payment method selection widget
  const PaymentMethodSelection({
    super.key,
    this.initialSelectedId,
    required this.onPaymentMethodSelected,
    this.showAddButton = true,
  });

  @override
  ConsumerState<PaymentMethodSelection> createState() =>
      _PaymentMethodSelectionState();
}

class _PaymentMethodSelectionState
    extends ConsumerState<PaymentMethodSelection> {
  String? _selectedPaymentMethodId;
  bool _isLoading = true;
  List<PaymentMethodModel> _paymentMethods = [];

  @override
  void initState() {
    super.initState();
    _selectedPaymentMethodId = widget.initialSelectedId;
    _loadPaymentMethods();
  }

  Future<void> _loadPaymentMethods() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Implement payment method loading with enhanced payment service
      // For now, return empty list as enhanced service focuses on payment processing
      final methods = <PaymentMethodModel>[];

      setState(() {
        _paymentMethods = methods;

        // If no payment method is selected, select the default one
        if (_selectedPaymentMethodId == null && methods.isNotEmpty) {
          final defaultMethod = methods.firstWhere(
            (method) => method.isDefault,
            orElse: () => methods.first,
          );
          _selectedPaymentMethodId = defaultMethod.id;
          widget.onPaymentMethodSelected(defaultMethod);
        }

        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading payment methods: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showAddPaymentMethodSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddPaymentMethodSheet(
        onPaymentMethodAdded: (newMethod) {
          setState(() {
            _paymentMethods.add(newMethod);
            _selectedPaymentMethodId = newMethod.id;
          });
          widget.onPaymentMethodSelected(newMethod);
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Payment Method',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (widget.showAddButton)
              TextButton.icon(
                onPressed: _showAddPaymentMethodSheet,
                icon: const Icon(
                  Icons.add,
                  size: 18,
                  color: AppTheme.primaryColor,
                ),
                label: const Text(
                  'Add New',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 16),
        if (_isLoading)
          const Center(
            child: CircularProgressIndicator(),
          )
        else if (_paymentMethods.isEmpty)
          _buildEmptyState()
        else
          _buildPaymentMethodsList(),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.credit_card,
            size: 48,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            'No payment methods found',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Add a payment method to continue',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _showAddPaymentMethodSheet,
            icon: const Icon(Icons.add),
            label: const Text('Add Payment Method'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodsList() {
    return Column(
      children: _paymentMethods.map((method) {
        final isSelected = method.id == _selectedPaymentMethodId;

        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedPaymentMethodId = method.id;
            });
            widget.onPaymentMethodSelected(method);
          },
          child: Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppTheme.primaryColor.withAlpha(26)
                  : Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color:
                    isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                // Payment method icon
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withAlpha(26),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    method.type.icon,
                    color: AppTheme.primaryColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),

                // Payment method details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        method.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (method.details['last4'] != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          'Ending in ${method.details['last4']}',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // Selection indicator
                if (isSelected)
                  const Icon(
                    Icons.check_circle,
                    color: AppTheme.primaryColor,
                    size: 24,
                  )
                else
                  const Icon(
                    Icons.circle_outlined,
                    color: Colors.grey,
                    size: 24,
                  ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }
}
