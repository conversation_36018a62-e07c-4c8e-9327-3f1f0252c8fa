import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/services/payment_api_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Widget for real-time payment status monitoring
class PaymentStatusMonitor extends StatefulWidget {
  final String transactionReference;
  final PaymentApiService paymentApiService;
  final LoggingService loggingService;
  final Function(PaymentVerificationResponse) onStatusUpdate;
  final Function(PaymentVerificationResponse)? onPaymentCompleted;
  final Function(String)? onError;
  final Duration pollingInterval;
  final Duration timeout;

  const PaymentStatusMonitor({
    super.key,
    required this.transactionReference,
    required this.paymentApiService,
    required this.loggingService,
    required this.onStatusUpdate,
    this.onPaymentCompleted,
    this.onError,
    this.pollingInterval = const Duration(seconds: 5),
    this.timeout = const Duration(minutes: 10),
  });

  @override
  State<PaymentStatusMonitor> createState() => _PaymentStatusMonitorState();
}

class _PaymentStatusMonitorState extends State<PaymentStatusMonitor>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _progressController;

  Timer? _pollingTimer;
  Timer? _timeoutTimer;
  PaymentStatus _currentStatus = PaymentStatus.pending;
  String? _errorMessage;
  DateTime? _startTime;
  bool _isMonitoring = false;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _progressController = AnimationController(
      duration: widget.timeout,
      vsync: this,
    );

    _startMonitoring();
  }

  @override
  void dispose() {
    _stopMonitoring();
    _pulseController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  /// Start payment status monitoring
  ///
  /// TODO: WebSocket Integration
  /// - Implement WebSocket connection for real-time updates
  /// - Fall back to polling if WebSocket fails
  /// - Handle connection drops and reconnection
  void _startMonitoring() {
    if (_isMonitoring) return;

    _startTime = DateTime.now();
    _isMonitoring = true;

    // Start animations
    _pulseController.repeat();
    _progressController.forward();

    // Start polling timer
    _pollingTimer = Timer.periodic(widget.pollingInterval, (_) {
      _checkPaymentStatus();
    });

    // Start timeout timer
    _timeoutTimer = Timer(widget.timeout, () {
      _handleTimeout();
    });

    // Initial status check
    _checkPaymentStatus();

    widget.loggingService.info(
      'PaymentStatusMonitor',
      'Started monitoring payment status',
      {
        'transactionReference': widget.transactionReference,
        'pollingInterval': widget.pollingInterval.inSeconds,
        'timeout': widget.timeout.inMinutes,
      },
    );
  }

  /// Stop payment status monitoring
  void _stopMonitoring() {
    _isMonitoring = false;
    _pollingTimer?.cancel();
    _timeoutTimer?.cancel();
    _pulseController.stop();
    _progressController.stop();
  }

  /// Check payment status with backend
  ///
  /// TODO: Backend Integration Required
  /// - Call GET /api/payments/status/{reference}
  /// - Handle different payment statuses
  /// - Implement exponential backoff on errors
  Future<void> _checkPaymentStatus() async {
    if (!_isMonitoring) return;

    try {
      final response = await widget.paymentApiService.getPaymentStatus(
        widget.transactionReference,
      );

      if (!mounted || !_isMonitoring) return;

      setState(() {
        _currentStatus = response.status;
        _errorMessage = null;
      });

      // Notify parent of status update
      widget.onStatusUpdate(response);

      // Handle completion
      if (response.isSuccessful) {
        _stopMonitoring();
        widget.onPaymentCompleted?.call(response);

        widget.loggingService.info(
          'PaymentStatusMonitor',
          'Payment completed successfully',
          {
            'transactionReference': widget.transactionReference,
            'processingTime': DateTime.now().difference(_startTime!).inSeconds,
          },
        );
      } else if (response.isFailed) {
        _stopMonitoring();
        final error = response.failureReason ?? 'Payment failed';
        setState(() {
          _errorMessage = error;
        });
        widget.onError?.call(error);

        widget.loggingService.warning(
          'PaymentStatusMonitor',
          'Payment failed',
          {
            'transactionReference': widget.transactionReference,
            'failureReason': response.failureReason,
          },
        );
      }
    } catch (e) {
      if (!mounted || !_isMonitoring) return;

      widget.loggingService.error(
        'PaymentStatusMonitor',
        'Failed to check payment status',
        {
          'transactionReference': widget.transactionReference,
          'error': e.toString(),
        },
      );

      // Don't stop monitoring on temporary errors
      // The polling will continue and retry
    }
  }

  /// Handle monitoring timeout
  void _handleTimeout() {
    if (!_isMonitoring) return;

    _stopMonitoring();

    final error =
        'Payment monitoring timed out after ${widget.timeout.inMinutes} minutes';
    setState(() {
      _errorMessage = error;
    });

    widget.onError?.call(error);

    widget.loggingService.warning(
      'PaymentStatusMonitor',
      'Payment monitoring timed out',
      {
        'transactionReference': widget.transactionReference,
        'timeout': widget.timeout.inMinutes,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        side: BorderSide(
          color: theme.colorScheme.outline.withAlpha(51),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                _buildStatusIcon(theme),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Payment Status',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        _getStatusText(),
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: _getStatusColor(theme),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Progress indicator
            if (_isMonitoring) _buildProgressIndicator(theme),

            // Error message
            if (_errorMessage != null) _buildErrorMessage(theme),

            // Transaction reference
            _buildTransactionReference(theme),
          ],
        ),
      ),
    );
  }

  /// Build status icon with animation
  Widget _buildStatusIcon(ThemeData theme) {
    IconData icon;
    Color color;

    switch (_currentStatus) {
      case PaymentStatus.pending:
      case PaymentStatus.processing:
        icon = Icons.hourglass_empty;
        color = theme.colorScheme.primary;
        break;
      case PaymentStatus.successful:
        icon = Icons.check_circle;
        color = AppTheme.successColor;
        break;
      case PaymentStatus.failed:
        icon = Icons.error;
        color = AppTheme.errorColor;
        break;
      case PaymentStatus.cancelled:
        icon = Icons.cancel;
        color = theme.colorScheme.onSurface.withAlpha(128);
        break;
      case PaymentStatus.expired:
        icon = Icons.access_time;
        color = AppTheme.warningColor;
        break;
    }

    Widget iconWidget = Icon(
      icon,
      color: color,
      size: 32,
    );

    // Add pulse animation for pending/processing states
    if (_isMonitoring &&
        (_currentStatus == PaymentStatus.pending ||
            _currentStatus == PaymentStatus.processing)) {
      iconWidget = iconWidget
          .animate(controller: _pulseController)
          .scale(begin: const Offset(1.0, 1.0), end: const Offset(1.2, 1.2))
          .then()
          .scale(begin: const Offset(1.2, 1.2), end: const Offset(1.0, 1.0));
    }

    return iconWidget;
  }

  /// Build progress indicator
  Widget _buildProgressIndicator(ThemeData theme) {
    return Column(
      children: [
        LinearProgressIndicator(
          value: _progressController.value,
          backgroundColor: theme.colorScheme.surfaceContainerHighest,
          valueColor: AlwaysStoppedAnimation<Color>(
            theme.colorScheme.primary,
          ),
        ),
        const SizedBox(height: AppTheme.spacingSmall),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Monitoring payment...',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withAlpha(179),
              ),
            ),
            Text(
              _getElapsedTime(),
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withAlpha(179),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build error message
  Widget _buildErrorMessage(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingSmall),
      decoration: BoxDecoration(
        color: AppTheme.errorColor.withAlpha(26),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.error_outline,
            color: AppTheme.errorColor,
            size: 16,
          ),
          const SizedBox(width: AppTheme.spacingSmall),
          Expanded(
            child: Text(
              _errorMessage!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: AppTheme.errorColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build transaction reference
  Widget _buildTransactionReference(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingSmall),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      ),
      child: Row(
        children: [
          Icon(
            Icons.receipt,
            color: theme.colorScheme.onSurface.withAlpha(128),
            size: 16,
          ),
          const SizedBox(width: AppTheme.spacingSmall),
          Expanded(
            child: Text(
              'Ref: ${widget.transactionReference}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withAlpha(179),
                fontFamily: 'monospace',
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Get status text
  String _getStatusText() {
    switch (_currentStatus) {
      case PaymentStatus.pending:
        return 'Waiting for payment...';
      case PaymentStatus.processing:
        return 'Processing payment...';
      case PaymentStatus.successful:
        return 'Payment successful!';
      case PaymentStatus.failed:
        return 'Payment failed';
      case PaymentStatus.cancelled:
        return 'Payment cancelled';
      case PaymentStatus.expired:
        return 'Payment expired';
    }
  }

  /// Get status color
  Color _getStatusColor(ThemeData theme) {
    switch (_currentStatus) {
      case PaymentStatus.pending:
      case PaymentStatus.processing:
        return theme.colorScheme.primary;
      case PaymentStatus.successful:
        return AppTheme.successColor;
      case PaymentStatus.failed:
        return AppTheme.errorColor;
      case PaymentStatus.cancelled:
        return theme.colorScheme.onSurface.withAlpha(128);
      case PaymentStatus.expired:
        return AppTheme.warningColor;
    }
  }

  /// Get elapsed time string
  String _getElapsedTime() {
    if (_startTime == null) return '0s';

    final elapsed = DateTime.now().difference(_startTime!);
    if (elapsed.inMinutes > 0) {
      return '${elapsed.inMinutes}m ${elapsed.inSeconds % 60}s';
    } else {
      return '${elapsed.inSeconds}s';
    }
  }
}
