import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/services/review_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/reviews/star_rating_input.dart';

/// A form for submitting a review
class ReviewSubmissionForm extends ConsumerStatefulWidget {
  /// The experience being reviewed
  final Experience experience;

  /// The booking ID associated with this review
  final String? bookingId;

  /// Callback when the review is submitted
  final Function(bool success)? onSubmitted;

  /// Creates a new review submission form
  const ReviewSubmissionForm({
    super.key,
    required this.experience,
    this.bookingId,
    this.onSubmitted,
  });

  @override
  ConsumerState<ReviewSubmissionForm> createState() =>
      _ReviewSubmissionFormState();
}

class _ReviewSubmissionFormState extends ConsumerState<ReviewSubmissionForm> {
  final _formKey = GlobalKey<FormState>();
  final _contentController = TextEditingController();
  double _rating = 0;
  final List<File> _selectedPhotos = [];
  final List<String> _selectedTags = [];
  bool _isSubmitting = false;
  bool _hasError = false;
  String _errorMessage = '';

  final List<String> _availableTags = [
    'Accurate Description',
    'Great Value',
    'Friendly Guide',
    'Informative',
    'Beautiful Location',
    'Authentic Experience',
    'Well Organized',
    'Recommended',
    'Family Friendly',
    'Unique',
  ];

  @override
  void dispose() {
    _contentController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 80,
    );

    if (pickedFile != null) {
      setState(() {
        _selectedPhotos.add(File(pickedFile.path));
      });
    }
  }

  void _removePhoto(int index) {
    setState(() {
      _selectedPhotos.removeAt(index);
    });
  }

  void _toggleTag(String tag) {
    setState(() {
      if (_selectedTags.contains(tag)) {
        _selectedTags.remove(tag);
      } else {
        if (_selectedTags.length < 3) {
          _selectedTags.add(tag);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('You can select up to 3 tags'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    });
  }

  Future<void> _submitReview() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    if (_rating == 0) {
      setState(() {
        _hasError = true;
        _errorMessage = 'Please select a rating';
      });
      return;
    }

    final content = _contentController.text.trim();
    if (content.length < 10) {
      setState(() {
        _hasError = true;
        _errorMessage = 'Review must be at least 10 characters long';
      });
      return;
    }

    final currentUser = ref.read(currentUserProvider).value;

    if (currentUser == null) {
      setState(() {
        _hasError = true;
        _errorMessage = 'You must be logged in to submit a review';
      });
      return;
    }

    setState(() {
      _isSubmitting = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      final reviewService = ref.read(reviewServiceProvider);

      await reviewService.createReview(
        experienceId: widget.experience.id,
        bookingId: widget.bookingId ?? '',
        userId: currentUser.uid,
        userName: currentUser.displayName ?? 'Anonymous',
        userProfileImageUrl: currentUser.photoURL,
        rating: _rating,
        content: content,
        photos: _selectedPhotos,
        tags: _selectedTags,
      );

      if (mounted) {
        widget.onSubmitted?.call(true);

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Review submitted successfully'),
            backgroundColor: Colors.green,
          ),
        );

        Navigator.of(context).pop();
      }
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
      });
      widget.onSubmitted?.call(false);
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Rating
          Center(
            child: Column(
              children: [
                const Text(
                  'Rate your experience',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                StarRatingInput(
                  initialRating: _rating,
                  starSize: 48,
                  // spacing: 8, // Remove this parameter as it's not defined
                  onChanged: (value) {
                    setState(() {
                      _rating = value;
                      _hasError = false;
                    });
                  },
                ),
                const SizedBox(height: 8),
                Text(
                  _getRatingText(),
                  style: TextStyle(
                    fontSize: 16,
                    color: _rating > 0 ? AppTheme.primaryColor : Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Review content
          const Text(
            'Write your review',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: _contentController,
            maxLines: 5,
            maxLength: 500,
            decoration: InputDecoration(
              hintText: 'Share your experience with others...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              filled: true,
              fillColor: Colors.grey.shade50,
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter your review';
              }
              if (value.trim().length < 10) {
                return 'Review must be at least 10 characters long';
              }
              return null;
            },
            onChanged: (_) {
              if (_hasError) {
                setState(() {
                  _hasError = false;
                });
              }
            },
          ),

          const SizedBox(height: 16),

          // Tags
          const Text(
            'Add tags (optional, up to 3)',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _availableTags.map((tag) {
              final isSelected = _selectedTags.contains(tag);
              return FilterChip(
                label: Text(tag),
                selected: isSelected,
                onSelected: (_) => _toggleTag(tag),
                backgroundColor: Colors.grey.shade100,
                selectedColor: AppTheme.primaryColor.withAlpha(51),
                checkmarkColor: AppTheme.primaryColor,
                labelStyle: TextStyle(
                  color: isSelected ? AppTheme.primaryColor : Colors.black,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              );
            }).toList(),
          ),

          const SizedBox(height: 24),

          // Photo upload
          const Text(
            'Add photos (optional)',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              // Add photo button
              InkWell(
                onTap: _selectedPhotos.length < 5 ? _pickImage : null,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.add_photo_alternate,
                        size: 32,
                        color: _selectedPhotos.length < 5
                            ? AppTheme.primaryColor
                            : Colors.grey,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${_selectedPhotos.length}/5',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // Selected photos
              Expanded(
                child: SizedBox(
                  height: 80,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _selectedPhotos.length,
                    itemBuilder: (context, index) {
                      return Stack(
                        children: [
                          Container(
                            width: 80,
                            height: 80,
                            margin: const EdgeInsets.only(right: 8),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              image: DecorationImage(
                                image: FileImage(_selectedPhotos[index]),
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                          Positioned(
                            top: 4,
                            right: 12,
                            child: GestureDetector(
                              onTap: () => _removePhoto(index),
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: const BoxDecoration(
                                  color: Colors.white,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.close,
                                  size: 16,
                                  color: Colors.red,
                                ),
                              ),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),
            ],
          ),

          // Error message
          if (_hasError) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _errorMessage,
                      style: const TextStyle(
                        color: Colors.red,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: 24),

          // Submit button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isSubmitting ? null : _submitReview,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _isSubmitting
                  ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : const Text(
                      'Submit Review',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  String _getRatingText() {
    if (_rating == 0) return 'Tap to rate';
    if (_rating == 1) return 'Poor';
    if (_rating == 2) return 'Fair';
    if (_rating == 3) return 'Good';
    if (_rating == 4) return 'Very Good';
    return 'Excellent';
  }
}
