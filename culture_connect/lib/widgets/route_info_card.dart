import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/services/location_service.dart';
import 'package:culture_connect/models/location/lat_lng.dart';

/// A widget that displays route information
class RouteInfoCard extends ConsumerWidget {
  /// The origin point
  final LatLng origin;

  /// The destination point
  final LatLng destination;

  /// The route points
  final List<LatLng> route;

  /// The travel mode
  final String travelMode;

  /// Callback when the close button is pressed
  final VoidCallback onClose;

  /// Callback when the travel mode is changed
  final Function(String) onTravelModeChanged;

  /// Constructor
  const RouteInfoCard({
    super.key,
    required this.origin,
    required this.destination,
    required this.route,
    required thisoute,
    required this.travelMode,
    required this.onClose,
    required this.onTravelModeChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final locationService = ref.watch(locationServiceProvider);

    // Calculate route distance
    final distance = locationService.calculateRouteDistance(route);

    // Estimate travel time
    final travelTime = locationService.estimateTravelTime(distance, travelMode);

    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.route),
                const SizedBox(width: 8),
                Text(
                  'Route Information',
                  style: theme.textTheme.titleMedium,
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: onClose,
                  tooltip: 'Close',
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 8),

            // Distance
            Row(
              children: [
                const Icon(Icons.straighten, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Distance: ${locationService.formatDistance(distance)}',
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Travel time
            Row(
              children: [
                const Icon(Icons.access_time, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Estimated time: ${_formatTravelTime(travelTime)}',
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Travel mode selector
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _TravelModeButton(
                  icon: Icons.directions_walk,
                  label: 'Walking',
                  isSelected: travelMode == 'walking',
                  onTap: () => onTravelModeChanged('walking'),
                ),
                _TravelModeButton(
                  icon: Icons.directions_bike,
                  label: 'Cycling',
                  isSelected: travelMode == 'cycling',
                  onTap: () => onTravelModeChanged('cycling'),
                ),
                _TravelModeButton(
                  icon: Icons.directions_car,
                  label: 'Driving',
                  isSelected: travelMode == 'driving',
                  onTap: () => onTravelModeChanged('driving'),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Start navigation button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                icon: const Icon(Icons.navigation),
                label: const Text('Start Navigation'),
                onPressed: () {
                  // In a real app, this would start turn-by-turn navigation
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Navigation started'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Format travel time in minutes
  String _formatTravelTime(int minutes) {
    if (minutes < 60) {
      return '$minutes min';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      return '$hours h ${remainingMinutes > 0 ? '$remainingMinutes min' : ''}';
    }
  }
}

/// A button for selecting travel mode
class _TravelModeButton extends StatelessWidget {
  /// The icon to display
  final IconData icon;

  /// The label to display
  final String label;

  /// Whether this mode is selected
  final bool isSelected;

  /// Callback when the button is tapped
  final VoidCallback onTap;

  /// Constructor
  const _TravelModeButton({
    required this.icon,
    required this.label,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? theme.colorScheme.primary.withAlpha(26)
              : null, // 0.1 opacity is approximately 26 alpha
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color:
                isSelected ? theme.colorScheme.primary : Colors.grey.shade300,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? theme.colorScheme.primary : Colors.grey,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? theme.colorScheme.primary : Colors.grey,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
