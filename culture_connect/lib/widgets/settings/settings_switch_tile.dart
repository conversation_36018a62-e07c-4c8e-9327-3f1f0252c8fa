import 'package:flutter/material.dart';

/// A switch tile widget for settings screens
class SettingsSwitchTile extends StatelessWidget {
  /// The title of the switch tile
  final String title;

  /// Optional subtitle text
  final String? subtitle;

  /// Optional icon to display
  final IconData? icon;

  /// Whether the switch is on or off
  final bool value;

  /// Callback when the switch value changes
  final ValueChanged<bool> onChanged;

  /// Optional enabled state
  final bool enabled;

  /// Optional padding
  final EdgeInsetsGeometry? padding;

  /// Optional icon color
  final Color? iconColor;

  /// Optional active color for the switch
  final Color? activeColor;

  /// Optional track color for the switch
  final Color? trackColor;

  /// Creates a settings switch tile widget
  const SettingsSwitchTile({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    required this.value,
    required this.onChanged,
    this.enabled = true,
    this.padding,
    this.iconColor,
    this.activeColor,
    this.trackColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return ListTile(
      enabled: enabled,
      contentPadding:
          padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      leading: icon != null
          ? Icon(
              icon,
              color: enabled
                  ? (iconColor ?? theme.colorScheme.primary)
                  : theme.disabledColor,
              size: 24,
            )
          : null,
      title: Text(
        title,
        style: theme.textTheme.bodyLarge?.copyWith(
          color: enabled ? theme.colorScheme.onSurface : theme.disabledColor,
        ),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: enabled
                    ? theme.colorScheme.onSurface.withAlpha(179)
                    : theme.disabledColor,
              ),
            )
          : null,
      trailing: Switch.adaptive(
        value: value,
        onChanged: enabled ? onChanged : null,
        activeColor: activeColor ?? theme.colorScheme.primary,
        activeTrackColor:
            trackColor ?? theme.colorScheme.primary.withAlpha(128),
      ),
      onTap: enabled
          ? () {
              onChanged(!value);
            }
          : null,
    );
  }
}
