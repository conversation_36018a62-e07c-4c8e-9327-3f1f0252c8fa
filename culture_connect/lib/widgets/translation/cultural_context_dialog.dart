import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/translation/cultural_context_model.dart';
import 'package:culture_connect/models/translation/translation_cultural_context.dart';
import 'package:culture_connect/providers/cultural_context_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/translation/cultural_context_indicator.dart';

/// A dialog for displaying cultural context information
class CulturalContextDialog extends ConsumerStatefulWidget {
  /// The cultural context information
  final TranslationCulturalContext culturalContext;

  /// Creates a new cultural context dialog
  const CulturalContextDialog({
    super.key,
    required this.culturalContext,
  });

  @override
  ConsumerState<CulturalContextDialog> createState() =>
      _CulturalContextDialogState();
}

class _CulturalContextDialogState extends ConsumerState<CulturalContextDialog>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  CulturalContextType? _selectedType;
  bool _showSensitiveContent = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _showSensitiveContent = ref.read(showSensitiveContentProvider);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: double.infinity,
        constraints: BoxConstraints(
          maxWidth: 500,
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: AppTheme.primaryColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.info_outline,
                    size: 24,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'Cultural Context',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),

            // Tab bar
            TabBar(
              controller: _tabController,
              labelColor: AppTheme.primaryColor,
              unselectedLabelColor: Colors.grey,
              indicatorColor: AppTheme.primaryColor,
              tabs: const [
                Tab(text: 'Notes'),
                Tab(text: 'About'),
              ],
            ),

            // Tab content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  // Notes tab
                  _buildNotesTab(),

                  // About tab
                  _buildAboutTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the notes tab
  Widget _buildNotesTab() {
    // Filter notes based on selected type and sensitivity
    final notes = widget.culturalContext.notes.where((note) {
      if (_selectedType != null && note.type != _selectedType) {
        return false;
      }
      if (note.isSensitive && !_showSensitiveContent) {
        return false;
      }
      return true;
    }).toList();

    return Column(
      children: [
        // Filter bar
        Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Type filter
              const Text(
                'Filter by Type:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              const SizedBox(height: 8),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    // All types option
                    _buildTypeFilterChip(null, 'All'),
                    const SizedBox(width: 8),

                    // Type options
                    ...CulturalContextType.values.map((type) {
                      return Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: _buildTypeFilterChip(type, type.displayName),
                      );
                    }),
                  ],
                ),
              ),

              const SizedBox(height: 12),

              // Sensitive content toggle
              Row(
                children: [
                  const Text(
                    'Show Sensitive Content:',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                  const Spacer(),
                  Switch(
                    value: _showSensitiveContent,
                    onChanged: (value) {
                      setState(() {
                        _showSensitiveContent = value;
                      });
                      ref
                          .read(culturalContextNotifierProvider.notifier)
                          .setShowSensitiveContent(value);
                    },
                    activeColor: AppTheme.primaryColor,
                  ),
                ],
              ),

              // Divider
              const Divider(height: 24),
            ],
          ),
        ),

        // Notes list
        Expanded(
          child: notes.isEmpty
              ? Center(
                  child: Text(
                    _selectedType != null
                        ? 'No ${_selectedType!.displayName} notes available'
                        : 'No cultural context notes available',
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: notes.length,
                  itemBuilder: (context, index) {
                    return CulturalContextNoteCard(note: notes[index]);
                  },
                ),
        ),
      ],
    );
  }

  /// Build the about tab
  Widget _buildAboutTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Language pair information
          const Text(
            'Language Information',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 8),
          _buildLanguagePairInfo(),

          const SizedBox(height: 16),

          // General cultural note
          if (widget.culturalContext.generalCulturalNote != null) ...[
            const Text(
              'Cultural Context',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withAlpha(26),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.blue.withAlpha(77),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 20,
                        color: Colors.blue,
                      ),
                      SizedBox(width: 8),
                      Text(
                        'General Cultural Note',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.culturalContext.generalCulturalNote!,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: 16),

          // Note type information
          const Text(
            'Types of Cultural Notes',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 8),
          ...CulturalContextType.values.map((type) {
            return _buildNoteTypeInfo(type);
          }),
        ],
      ),
    );
  }

  /// Build a type filter chip
  Widget _buildTypeFilterChip(CulturalContextType? type, String label) {
    final isSelected = _selectedType == type;
    final color = type?.color ?? Colors.grey;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedType = isSelected ? null : type;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 6,
        ),
        decoration: BoxDecoration(
          color: isSelected ? color.withAlpha(51) : Colors.grey[100],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (type != null) ...[
              Icon(
                type.icon,
                size: 16,
                color: isSelected ? color : Colors.grey,
              ),
              const SizedBox(width: 4),
            ],
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: isSelected ? color : Colors.grey[700],
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build language pair information
  Widget _buildLanguagePairInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                _getLanguageName(widget.culturalContext.sourceLanguage),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              const SizedBox(width: 8),
              const Icon(
                Icons.arrow_forward,
                size: 16,
                color: Colors.grey,
              ),
              const SizedBox(width: 8),
              Text(
                _getLanguageName(widget.culturalContext.targetLanguage),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ],
          ),
          if (widget.culturalContext.sourceRegion != null ||
              widget.culturalContext.targetRegion != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                if (widget.culturalContext.sourceRegion != null)
                  Text(
                    '${widget.culturalContext.sourceRegion} dialect',
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                if (widget.culturalContext.sourceRegion != null &&
                    widget.culturalContext.targetRegion != null) ...[
                  const SizedBox(width: 8),
                  const Icon(
                    Icons.arrow_forward,
                    size: 14,
                    color: Colors.grey,
                  ),
                  const SizedBox(width: 8),
                ],
                if (widget.culturalContext.targetRegion != null)
                  Text(
                    '${widget.culturalContext.targetRegion} dialect',
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
              ],
            ),
          ],
          const SizedBox(height: 8),
          Text(
            'Cultural context notes: ${widget.culturalContext.noteCount}',
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// Build note type information
  Widget _buildNoteTypeInfo(CulturalContextType type) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            type.icon,
            size: 20,
            color: type.color,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  type.displayName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _getNoteTypeDescription(type),
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Get the language name from a language code
  String _getLanguageName(String code) {
    switch (code) {
      case 'en':
        return 'English';
      case 'fr':
        return 'French';
      case 'yo':
        return 'Yoruba';
      case 'ig':
        return 'Igbo';
      case 'ha':
        return 'Hausa';
      case 'sw':
        return 'Swahili';
      default:
        return code;
    }
  }

  /// Get the description for a note type
  String _getNoteTypeDescription(CulturalContextType type) {
    switch (type) {
      case CulturalContextType.general:
        return 'General cultural information relevant to the translation.';
      case CulturalContextType.idiom:
        return 'Expressions that cannot be understood from the individual meanings of their words.';
      case CulturalContextType.reference:
        return 'References to cultural elements, events, or figures that may not be familiar.';
      case CulturalContextType.taboo:
        return 'Content that may be considered sensitive or inappropriate in certain contexts.';
      case CulturalContextType.regional:
        return 'Usage specific to certain regions or dialects.';
      case CulturalContextType.slang:
        return 'Informal language or expressions used by particular groups.';
      case CulturalContextType.formality:
        return 'Information about formal vs. informal usage and social context.';
      case CulturalContextType.historical:
        return 'Historical context that helps understand the meaning or significance.';
      case CulturalContextType.religious:
        return 'References to religious concepts, practices, or beliefs.';
    }
  }
}
