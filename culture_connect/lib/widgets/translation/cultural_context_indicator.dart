import 'package:flutter/material.dart';
import 'package:culture_connect/models/translation/cultural_context_model.dart';
import 'package:culture_connect/models/translation/translation_cultural_context.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for displaying a cultural context indicator
class CulturalContextIndicator extends StatelessWidget {
  /// The cultural context information
  final TranslationCulturalContext culturalContext;

  /// Whether to show the label
  final bool showLabel;

  /// Whether to use a compact layout
  final bool compact;

  /// Whether to use light colors (for dark backgrounds)
  final bool useLight;

  /// Callback when the indicator is tapped
  final VoidCallback? onTap;

  /// Creates a new cultural context indicator
  const CulturalContextIndicator({
    super.key,
    required this.culturalContext,
    this.showLabel = true,
    this.compact = false,
    this.useLight = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    if (!culturalContext.hasContextInformation) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Context icon
          Icon(
            Icons.info_outline,
            size: compact ? 14 : 18,
            color: useLight ? Colors.white70 : AppTheme.primaryColor,
          ),

          if (showLabel) ...[
            const SizedBox(width: 4),

            // Context label
            Text(
              compact ? 'Cultural Note' : 'Cultural Context',
              style: TextStyle(
                fontSize: compact ? 10 : 12,
                color: useLight ? Colors.white70 : AppTheme.textSecondaryColor,
              ),
            ),
          ],

          // Badge with count
          if (culturalContext.noteCount > 0) ...[
            const SizedBox(width: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: useLight
                    ? Colors.white24
                    : AppTheme.primaryColor.withAlpha(51),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                culturalContext.noteCount.toString(),
                style: TextStyle(
                  fontSize: compact ? 8 : 10,
                  fontWeight: FontWeight.bold,
                  color: useLight ? Colors.white : AppTheme.primaryColor,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// A widget for displaying a cultural context note
class CulturalContextNoteCard extends StatelessWidget {
  /// The cultural context note
  final CulturalContextNote note;

  /// Creates a new cultural context note card
  const CulturalContextNoteCard({
    super.key,
    required this.note,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: note.type.color.withAlpha(77),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  note.type.icon,
                  size: 20,
                  color: note.type.color,
                ),
                const SizedBox(width: 8),
                Text(
                  note.type.displayName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                if (note.isSensitive) ...[
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red.withAlpha(26),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.warning_amber_outlined,
                          size: 14,
                          color: Colors.red,
                        ),
                        SizedBox(width: 4),
                        Text(
                          'Sensitive',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),

            const SizedBox(height: 8),

            // Text segment
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: note.type.color.withAlpha(26),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '"${note.textSegment}"',
                style: const TextStyle(
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ),

            const SizedBox(height: 8),

            // Explanation
            Text(
              note.explanation,
              style: const TextStyle(
                fontSize: 14,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            // Alternatives (if available)
            if (note.alternatives != null && note.alternatives!.isNotEmpty) ...[
              const SizedBox(height: 12),
              const Text(
                'Alternative Expressions:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              const SizedBox(height: 4),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: note.alternatives!.map((alternative) {
                  return Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: note.type.color.withAlpha(26),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: note.type.color.withAlpha(77),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      alternative,
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],

            // Additional information
            if (note.region != null || note.formalityLevel != null) ...[
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  if (note.region != null)
                    _buildInfoChip(
                      Icons.location_on_outlined,
                      note.region!,
                      Colors.green,
                    ),
                  if (note.formalityLevel != null)
                    _buildInfoChip(
                      Icons.person_outline,
                      note.formalityLevel!,
                      Colors.blue,
                    ),
                ],
              ),
            ],

            // Resources (if available)
            if (note.resources != null && note.resources!.isNotEmpty) ...[
              const SizedBox(height: 12),
              const Text(
                'Learn More:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              const SizedBox(height: 4),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: note.resources!.map((resource) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.link,
                          size: 14,
                          color: AppTheme.primaryColor,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            resource,
                            style: const TextStyle(
                              fontSize: 12,
                              color: AppTheme.primaryColor,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Build an info chip
  Widget _buildInfoChip(IconData icon, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: color.withAlpha(26),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
