import 'package:flutter/material.dart';
import 'package:culture_connect/models/translation/custom_vocabulary_model.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget that displays a custom vocabulary term
class CustomVocabularyItem extends StatelessWidget {
  /// The vocabulary term to display
  final CustomVocabularyModel term;

  /// Callback when the item is tapped
  final VoidCallback? onTap;

  /// Callback when the edit button is tapped
  final VoidCallback? onEdit;

  /// Callback when the delete button is tapped
  final VoidCallback? onDelete;

  /// Callback when the favorite button is tapped
  final VoidCallback? onToggleFavorite;

  /// Whether to show the full content
  final bool showFullContent;

  /// Creates a new custom vocabulary item
  const CustomVocabularyItem({
    super.key,
    required this.term,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onToggleFavorite,
    this.showFullContent = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with category and language
              Row(
                children: [
                  // Category badge
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withAlpha(26),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _getCategoryIcon(term.category),
                          size: 16,
                          color: AppTheme.primaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          term.getCategoryName(),
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(width: 8),

                  // Language badge
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _getLanguageFlag(term.originalLanguageCode),
                          style: const TextStyle(
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _getLanguageName(term.originalLanguageCode),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[700],
                          ),
                        ),
                      ],
                    ),
                  ),

                  const Spacer(),

                  // Usage count badge
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.amber.withAlpha(26),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.history,
                          size: 16,
                          color: Colors.amber[700],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Used ${term.usageCount} times',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.amber[700],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Original term
              Text(
                term.originalTerm,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),

              // Description (if available)
              if (term.description != null &&
                  (showFullContent || term.description!.length <= 100)) ...[
                const SizedBox(height: 4),
                Text(
                  term.description!,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black54,
                  ),
                  maxLines: showFullContent ? null : 2,
                  overflow: showFullContent ? null : TextOverflow.ellipsis,
                ),
              ],

              // Translations
              if (term.translations.isNotEmpty &&
                  (showFullContent || term.translations.length <= 3)) ...[
                const SizedBox(height: 12),
                const Text(
                  'Translations:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: term.translations.entries
                      .take(showFullContent ? term.translations.length : 3)
                      .map((entry) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Colors.grey[300]!,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            _getLanguageFlag(entry.key),
                            style: const TextStyle(
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            entry.value,
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
              ],

              // More translations indicator
              if (!showFullContent && term.translations.length > 3) ...[
                const SizedBox(height: 8),
                Text(
                  '+ ${term.translations.length - 3} more translations',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],

              const SizedBox(height: 12),

              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // Edit button
                  if (onEdit != null)
                    IconButton(
                      icon: const Icon(
                        Icons.edit,
                        size: 20,
                        color: AppTheme.primaryColor,
                      ),
                      onPressed: onEdit,
                      tooltip: 'Edit',
                    ),

                  // Favorite button
                  if (onToggleFavorite != null)
                    IconButton(
                      icon: Icon(
                        term.isFavorite
                            ? Icons.favorite
                            : Icons.favorite_border,
                        size: 20,
                        color: term.isFavorite ? Colors.red : Colors.grey,
                      ),
                      onPressed: onToggleFavorite,
                      tooltip: term.isFavorite
                          ? 'Remove from favorites'
                          : 'Add to favorites',
                    ),

                  // Delete button
                  if (onDelete != null)
                    IconButton(
                      icon: const Icon(
                        Icons.delete,
                        size: 20,
                        color: Colors.red,
                      ),
                      onPressed: onDelete,
                      tooltip: 'Delete',
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Get the icon for a category
  IconData _getCategoryIcon(VocabularyCategory category) {
    switch (category) {
      case VocabularyCategory.general:
        return Icons.language;
      case VocabularyCategory.medical:
        return Icons.medical_services;
      case VocabularyCategory.technical:
        return Icons.build;
      case VocabularyCategory.business:
        return Icons.business;
      case VocabularyCategory.legal:
        return Icons.gavel;
      case VocabularyCategory.academic:
        return Icons.school;
      case VocabularyCategory.cultural:
        return Icons.public;
      case VocabularyCategory.travel:
        return Icons.flight;
      case VocabularyCategory.food:
        return Icons.restaurant;
      case VocabularyCategory.custom:
        return Icons.category;
    }
  }

  /// Get the flag for a language code
  String _getLanguageFlag(String languageCode) {
    // This is a simplified version - in a real app, you would use a more comprehensive mapping
    switch (languageCode) {
      case 'en':
        return '🇺🇸';
      case 'fr':
        return '🇫🇷';
      case 'es':
        return '🇪🇸';
      case 'de':
        return '🇩🇪';
      case 'it':
        return '🇮🇹';
      case 'pt':
        return '🇵🇹';
      case 'ru':
        return '🇷🇺';
      case 'zh':
        return '🇨🇳';
      case 'ja':
        return '🇯🇵';
      case 'ko':
        return '🇰🇷';
      case 'ar':
        return '🇸🇦';
      case 'hi':
        return '🇮🇳';
      case 'bn':
        return '🇧🇩';
      case 'sw':
        return '🇰🇪';
      case 'yo':
        return '🇳🇬';
      case 'ha':
        return '🇳🇬';
      case 'ig':
        return '🇳🇬';
      default:
        return '🌐';
    }
  }

  /// Get the name for a language code
  String _getLanguageName(String languageCode) {
    // This is a simplified version - in a real app, you would use a more comprehensive mapping
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'fr':
        return 'French';
      case 'es':
        return 'Spanish';
      case 'de':
        return 'German';
      case 'it':
        return 'Italian';
      case 'pt':
        return 'Portuguese';
      case 'ru':
        return 'Russian';
      case 'zh':
        return 'Chinese';
      case 'ja':
        return 'Japanese';
      case 'ko':
        return 'Korean';
      case 'ar':
        return 'Arabic';
      case 'hi':
        return 'Hindi';
      case 'bn':
        return 'Bengali';
      case 'sw':
        return 'Swahili';
      case 'yo':
        return 'Yoruba';
      case 'ha':
        return 'Hausa';
      case 'ig':
        return 'Igbo';
      default:
        return languageCode;
    }
  }
}
