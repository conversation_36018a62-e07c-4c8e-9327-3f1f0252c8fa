import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/translation/pronunciation_model.dart';
import 'package:culture_connect/models/translation/translation_pronunciation.dart';
import 'package:culture_connect/providers/pronunciation_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/translation/pronunciation_indicator.dart';

/// A dialog for displaying pronunciation information
class PronunciationDialog extends ConsumerStatefulWidget {
  /// The pronunciation information
  final TranslationPronunciation pronunciation;

  /// Creates a new pronunciation dialog
  const PronunciationDialog({
    super.key,
    required this.pronunciation,
  });

  @override
  ConsumerState<PronunciationDialog> createState() =>
      _PronunciationDialogState();
}

class _PronunciationDialogState extends ConsumerState<PronunciationDialog>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  PronunciationGuideType? _selectedType;
  PronunciationDifficulty? _selectedDifficulty;
  bool _showDifficultOnly = false;
  bool _isPlayingFullText = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _showDifficultOnly = ref.read(showDifficultOnlyProvider);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: double.infinity,
        constraints: const BoxConstraints(
          maxWidth: 500,
        ).copyWith(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.teal,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.record_voice_over,
                    size: 24,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'Pronunciation Guide',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),

            // Tab bar
            TabBar(
              controller: _tabController,
              labelColor: Colors.teal,
              unselectedLabelColor: Colors.grey,
              indicatorColor: Colors.teal,
              tabs: const [
                Tab(text: 'Guides'),
                Tab(text: 'About'),
              ],
            ),

            // Tab content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  // Guides tab
                  _buildGuidesTab(),

                  // About tab
                  _buildAboutTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the guides tab
  Widget _buildGuidesTab() {
    // Filter guides based on selected type, difficulty, and show difficult only
    final guides = widget.pronunciation.guides.where((guide) {
      if (_selectedType != null && guide.type != _selectedType) {
        return false;
      }
      if (_selectedDifficulty != null &&
          guide.difficulty != _selectedDifficulty) {
        return false;
      }
      if (_showDifficultOnly &&
          guide.difficulty != PronunciationDifficulty.moderate &&
          guide.difficulty != PronunciationDifficulty.difficult &&
          guide.difficulty != PronunciationDifficulty.veryDifficult) {
        return false;
      }
      return true;
    }).toList();

    return Column(
      children: [
        // Filter bar
        Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Full text audio (if available)
              if (widget.pronunciation.hasFullTextAudio) ...[
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.teal.withAlpha(26),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.teal.withAlpha(77),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.volume_up,
                        size: 24,
                        color: Colors.teal,
                      ),
                      const SizedBox(width: 12),
                      const Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Full Text Pronunciation',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.textPrimaryColor,
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              'Listen to the pronunciation of the entire text',
                              style: TextStyle(
                                fontSize: 12,
                                color: AppTheme.textSecondaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        icon: Icon(
                          _isPlayingFullText ? Icons.stop : Icons.play_arrow,
                          color: Colors.teal,
                        ),
                        onPressed: () {
                          setState(() {
                            _isPlayingFullText = !_isPlayingFullText;
                          });
                          if (_isPlayingFullText) {
                            ref
                                .read(pronunciationNotifierProvider.notifier)
                                .playPronunciationAudio(
                                    widget.pronunciation.fullTextAudioPath!);
                          } else {
                            ref
                                .read(pronunciationNotifierProvider.notifier)
                                .stopAudio();
                          }
                        },
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Type filter
              const Text(
                'Filter by Type:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              const SizedBox(height: 8),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    // All types option
                    _buildTypeFilterChip(null, 'All'),
                    const SizedBox(width: 8),

                    // Type options
                    ...PronunciationGuideType.values.map((type) {
                      // Only show types that have guides
                      if (widget.pronunciation.getGuidesOfType(type).isEmpty) {
                        return const SizedBox.shrink();
                      }
                      return Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: _buildTypeFilterChip(type, type.displayName),
                      );
                    }),
                  ],
                ),
              ),

              const SizedBox(height: 12),

              // Difficulty filter
              const Text(
                'Filter by Difficulty:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              const SizedBox(height: 8),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    // All difficulty levels option
                    _buildDifficultyFilterChip(null, 'All'),
                    const SizedBox(width: 8),

                    // Difficulty level options
                    ...PronunciationDifficulty.values.map((difficulty) {
                      // Check if there are guides with this difficulty level
                      final hasGuides = widget.pronunciation.guides.any(
                        (guide) => guide.difficulty == difficulty,
                      );
                      if (!hasGuides) {
                        return const SizedBox.shrink();
                      }
                      return Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: _buildDifficultyFilterChip(
                            difficulty, difficulty.displayName),
                      );
                    }),
                  ],
                ),
              ),

              const SizedBox(height: 12),

              // Show difficult only toggle
              Row(
                children: [
                  const Text(
                    'Show Difficult Pronunciations Only:',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                  const Spacer(),
                  Switch(
                    value: _showDifficultOnly,
                    onChanged: (value) {
                      setState(() {
                        _showDifficultOnly = value;
                      });
                      ref
                          .read(pronunciationNotifierProvider.notifier)
                          .setShowDifficultOnly(value);
                    },
                    activeColor: Colors.teal,
                  ),
                ],
              ),

              // Divider
              const Divider(height: 24),
            ],
          ),
        ),

        // Guides list
        Expanded(
          child: guides.isEmpty
              ? Center(
                  child: Text(
                    _getEmptyStateMessage(),
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: guides.length,
                  itemBuilder: (context, index) {
                    return PronunciationGuideCard(
                      guide: guides[index],
                      onPlayAudio: (audioPath) {
                        ref
                            .read(pronunciationNotifierProvider.notifier)
                            .playPronunciationAudio(audioPath);
                      },
                    );
                  },
                ),
        ),
      ],
    );
  }

  /// Build the about tab
  Widget _buildAboutTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Language pair information
          const Text(
            'Language Information',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 8),
          _buildLanguagePairInfo(),

          const SizedBox(height: 16),

          // General pronunciation notes
          if (widget.pronunciation.generalPronunciationNotes != null) ...[
            const Text(
              'General Pronunciation Notes',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.teal.withAlpha(26),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.teal.withAlpha(77),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 20,
                        color: Colors.teal,
                      ),
                      SizedBox(width: 8),
                      Text(
                        'About Pronunciation',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.pronunciation.generalPronunciationNotes!,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: 16),

          // Guide type information
          const Text(
            'Types of Pronunciation Guides',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 8),
          ...PronunciationGuideType.values.map((type) {
            return _buildGuideTypeInfo(type);
          }),

          const SizedBox(height: 16),

          // Difficulty levels
          const Text(
            'Difficulty Levels',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 8),
          ...PronunciationDifficulty.values.map((difficulty) {
            return _buildDifficultyLevelInfo(difficulty);
          }),
        ],
      ),
    );
  }

  /// Build a type filter chip
  Widget _buildTypeFilterChip(PronunciationGuideType? type, String label) {
    final isSelected = _selectedType == type;
    const color = Colors.teal;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedType = isSelected ? null : type;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 6,
        ),
        decoration: BoxDecoration(
          color: isSelected ? color.withAlpha(51) : Colors.grey[100],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (type != null) ...[
              Icon(
                type.icon,
                size: 16,
                color: isSelected ? color : Colors.grey,
              ),
              const SizedBox(width: 4),
            ],
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: isSelected ? color : Colors.grey[700],
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build a difficulty filter chip
  Widget _buildDifficultyFilterChip(
      PronunciationDifficulty? difficulty, String label) {
    final isSelected = _selectedDifficulty == difficulty;
    final color = difficulty?.color ?? Colors.grey;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedDifficulty = isSelected ? null : difficulty;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 6,
        ),
        decoration: BoxDecoration(
          color: isSelected ? color.withAlpha(51) : Colors.grey[100],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (difficulty != null) ...[
              Icon(
                difficulty.icon,
                size: 16,
                color: isSelected ? color : Colors.grey,
              ),
              const SizedBox(width: 4),
            ],
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: isSelected ? color : Colors.grey[700],
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build language pair information
  Widget _buildLanguagePairInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                _getLanguageName(widget.pronunciation.sourceLanguage),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              const SizedBox(width: 8),
              const Icon(
                Icons.arrow_forward,
                size: 16,
                color: Colors.grey,
              ),
              const SizedBox(width: 8),
              Text(
                _getLanguageName(widget.pronunciation.targetLanguage),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ],
          ),
          if (widget.pronunciation.sourceRegion != null ||
              widget.pronunciation.targetRegion != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                if (widget.pronunciation.sourceRegion != null)
                  Text(
                    '${widget.pronunciation.sourceRegion} dialect',
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                if (widget.pronunciation.sourceRegion != null &&
                    widget.pronunciation.targetRegion != null) ...[
                  const SizedBox(width: 8),
                  const Icon(
                    Icons.arrow_forward,
                    size: 14,
                    color: Colors.grey,
                  ),
                  const SizedBox(width: 8),
                ],
                if (widget.pronunciation.targetRegion != null)
                  Text(
                    '${widget.pronunciation.targetRegion} dialect',
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
              ],
            ),
          ],
          const SizedBox(height: 8),
          Text(
            'Total pronunciation guides: ${widget.pronunciation.guideCount}',
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
          ),
          if (widget.pronunciation.difficultGuideCount > 0) ...[
            const SizedBox(height: 4),
            Text(
              'Difficult pronunciations: ${widget.pronunciation.difficultGuideCount}',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.orange,
              ),
            ),
          ],
          if (widget.pronunciation.guidesWithAudioCount > 0) ...[
            const SizedBox(height: 4),
            Text(
              'Guides with audio: ${widget.pronunciation.guidesWithAudioCount}',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.teal,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build guide type information
  Widget _buildGuideTypeInfo(PronunciationGuideType type) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            type.icon,
            size: 20,
            color: Colors.teal,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  type.displayName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  type.description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build difficulty level information
  Widget _buildDifficultyLevelInfo(PronunciationDifficulty difficulty) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            difficulty.icon,
            size: 20,
            color: difficulty.color,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  difficulty.displayName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _getDifficultyLevelDescription(difficulty),
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Get the language name from a language code
  String _getLanguageName(String code) {
    switch (code) {
      case 'en':
        return 'English';
      case 'fr':
        return 'French';
      case 'es':
        return 'Spanish';
      case 'yo':
        return 'Yoruba';
      case 'ig':
        return 'Igbo';
      case 'ha':
        return 'Hausa';
      case 'sw':
        return 'Swahili';
      default:
        return code;
    }
  }

  /// Get the description for a difficulty level
  String _getDifficultyLevelDescription(PronunciationDifficulty difficulty) {
    switch (difficulty) {
      case PronunciationDifficulty.veryEasy:
        return 'Sounds that are very similar to English or are intuitive to pronounce.';
      case PronunciationDifficulty.easy:
        return 'Sounds that are somewhat similar to English with minor differences.';
      case PronunciationDifficulty.moderate:
        return 'Sounds that require some practice but are achievable with effort.';
      case PronunciationDifficulty.difficult:
        return 'Sounds that are significantly different from English and require dedicated practice.';
      case PronunciationDifficulty.veryDifficult:
        return 'Sounds that are not found in English and require extensive practice to master.';
    }
  }

  /// Get the empty state message based on the current filters
  String _getEmptyStateMessage() {
    if (_selectedType != null && _selectedDifficulty != null) {
      return 'No ${_selectedType!.displayName.toLowerCase()} guides with ${_selectedDifficulty!.displayName.toLowerCase()} difficulty found';
    } else if (_selectedType != null) {
      return 'No ${_selectedType!.displayName.toLowerCase()} guides found';
    } else if (_selectedDifficulty != null) {
      return 'No guides with ${_selectedDifficulty!.displayName.toLowerCase()} difficulty found';
    } else if (_showDifficultOnly &&
        widget.pronunciation.difficultGuideCount == 0) {
      return 'No difficult pronunciations found';
    } else {
      return 'No pronunciation guides found';
    }
  }
}
