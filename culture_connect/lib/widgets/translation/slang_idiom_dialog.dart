import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/translation/slang_idiom_model.dart';
import 'package:culture_connect/models/translation/translation_slang_idiom.dart';

import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/translation/slang_idiom_indicator.dart';

/// A dialog for displaying slang and idiom information
class SlangIdiomDialog extends ConsumerStatefulWidget {
  /// The slang and idiom information
  final TranslationSlangIdiom slangIdiom;

  /// Creates a new slang and idiom dialog
  const SlangIdiomDialog({
    super.key,
    required this.slangIdiom,
  });

  @override
  ConsumerState<SlangIdiomDialog> createState() => _SlangIdiomDialogState();
}

class _SlangIdiomDialogState extends ConsumerState<SlangIdiomDialog>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  ExpressionType? _selectedType;
  FormalityLevel? _selectedFormalityLevel;
  bool _showPotentiallyOffensiveContent = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _showPotentiallyOffensiveContent =
        false; // Default value, will be updated from provider
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: double.infinity,
        constraints: BoxConstraints(
          maxWidth: 500,
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.purple,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.format_quote,
                    size: 24,
                    color: Colors.white,
                  ),
                  SizedBox(width: 8),
                  Text(
                    'Slang & Idiom Expressions',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),

            // Tab bar
            TabBar(
              controller: _tabController,
              labelColor: Colors.purple,
              unselectedLabelColor: Colors.grey,
              indicatorColor: Colors.purple,
              tabs: const [
                Tab(text: 'Expressions'),
                Tab(text: 'About'),
              ],
            ),

            // Tab content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  // Expressions tab
                  _buildExpressionsTab(),

                  // About tab
                  _buildAboutTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the expressions tab
  Widget _buildExpressionsTab() {
    // Filter expressions based on selected type, formality level, and offensive content
    final expressions = widget.slangIdiom.expressions.where((expression) {
      if (_selectedType != null && expression.type != _selectedType) {
        return false;
      }
      if (_selectedFormalityLevel != null &&
          expression.formalityLevel != _selectedFormalityLevel) {
        return false;
      }
      if (expression.isPotentiallyOffensive &&
          !_showPotentiallyOffensiveContent) {
        return false;
      }
      return true;
    }).toList();

    return Column(
      children: [
        // Filter bar
        Padding(
          padding: EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Type filter
              Text(
                'Filter by Type:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              SizedBox(height: 8),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    // All types option
                    _buildTypeFilterChip(null, 'All'),
                    SizedBox(width: 8),

                    // Type options
                    ...ExpressionType.values.map((type) {
                      // Only show types that have expressions
                      if (widget.slangIdiom
                          .getExpressionsOfType(type)
                          .isEmpty) {
                        return const SizedBox.shrink();
                      }
                      return Padding(
                        padding: EdgeInsets.only(right: 8),
                        child: _buildTypeFilterChip(type, type.displayName),
                      );
                    }),
                  ],
                ),
              ),

              SizedBox(height: 12),

              // Formality level filter
              Text(
                'Filter by Formality:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              SizedBox(height: 8),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    // All formality levels option
                    _buildFormalityFilterChip(null, 'All'),
                    SizedBox(width: 8),

                    // Formality level options
                    ...FormalityLevel.values.map((level) {
                      // Check if there are expressions with this formality level
                      final hasExpressions = widget.slangIdiom.expressions.any(
                        (expression) => expression.formalityLevel == level,
                      );
                      if (!hasExpressions) {
                        return const SizedBox.shrink();
                      }
                      return Padding(
                        padding: EdgeInsets.only(right: 8),
                        child:
                            _buildFormalityFilterChip(level, level.displayName),
                      );
                    }),
                  ],
                ),
              ),

              SizedBox(height: 12),

              // Potentially offensive content toggle
              Row(
                children: [
                  Text(
                    'Show Potentially Offensive Content:',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                  const Spacer(),
                  Switch(
                    value: _showPotentiallyOffensiveContent,
                    onChanged: (value) {
                      setState(() {
                        _showPotentiallyOffensiveContent = value;
                      });
                      // TODO: Update provider when available
                      // ref.read(slangIdiomNotifierProvider.notifier)
                      //     .setShowPotentiallyOffensiveContent(value);
                    },
                    activeColor: Colors.purple,
                  ),
                ],
              ),

              // Divider
              Divider(height: 24),
            ],
          ),
        ),

        // Expressions list
        Expanded(
          child: expressions.isEmpty
              ? Center(
                  child: Text(
                    _getEmptyStateMessage(),
                    style: TextStyle(
                      fontSize: 16,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                )
              : ListView.builder(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  itemCount: expressions.length,
                  itemBuilder: (context, index) {
                    return SlangIdiomExpressionCard(
                        expression: expressions[index]);
                  },
                ),
        ),
      ],
    );
  }

  /// Build the about tab
  Widget _buildAboutTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Language pair information
          Text(
            'Language Information',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 8),
          _buildLanguagePairInfo(),

          const SizedBox(height: 16),

          // General notes
          if (widget.slangIdiom.generalNotes != null) ...[
            Text(
              'General Notes',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.purple.withAlpha(26),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.purple.withAlpha(77),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.info_outline,
                        size: 20,
                        color: Colors.purple,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'About Slang & Idioms',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.slangIdiom.generalNotes!,
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: 16),

          // Expression type information
          Text(
            'Types of Expressions',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 8),
          ...ExpressionType.values.map((type) {
            return _buildExpressionTypeInfo(type);
          }),

          const SizedBox(height: 16),

          // Formality levels
          Text(
            'Formality Levels',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 8),
          ...FormalityLevel.values.map((level) {
            return _buildFormalityLevelInfo(level);
          }),
        ],
      ),
    );
  }

  /// Build a type filter chip
  Widget _buildTypeFilterChip(ExpressionType? type, String label) {
    final isSelected = _selectedType == type;
    final color = type?.color ?? Colors.grey;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedType = isSelected ? null : type;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 6,
        ),
        decoration: BoxDecoration(
          color: isSelected ? color.withAlpha(51) : Colors.grey[100],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (type != null) ...[
              Icon(
                type.icon,
                size: 16,
                color: isSelected ? color : Colors.grey,
              ),
              const SizedBox(width: 4),
            ],
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: isSelected ? color : Colors.grey[700],
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build a formality level filter chip
  Widget _buildFormalityFilterChip(FormalityLevel? level, String label) {
    final isSelected = _selectedFormalityLevel == level;
    final color = level?.color ?? Colors.grey;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedFormalityLevel = isSelected ? null : level;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 6,
        ),
        decoration: BoxDecoration(
          color: isSelected ? color.withAlpha(51) : Colors.grey[100],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: isSelected ? color : Colors.grey[700],
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  /// Build language pair information
  Widget _buildLanguagePairInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                _getLanguageName(widget.slangIdiom.sourceLanguage),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              const SizedBox(width: 8),
              const Icon(
                Icons.arrow_forward,
                size: 16,
                color: Colors.grey,
              ),
              const SizedBox(width: 8),
              Text(
                _getLanguageName(widget.slangIdiom.targetLanguage),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ],
          ),
          if (widget.slangIdiom.sourceRegion != null ||
              widget.slangIdiom.targetRegion != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                if (widget.slangIdiom.sourceRegion != null)
                  Text(
                    '${widget.slangIdiom.sourceRegion} dialect',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                if (widget.slangIdiom.sourceRegion != null &&
                    widget.slangIdiom.targetRegion != null) ...[
                  const SizedBox(width: 8),
                  const Icon(
                    Icons.arrow_forward,
                    size: 14,
                    color: Colors.grey,
                  ),
                  const SizedBox(width: 8),
                ],
                if (widget.slangIdiom.targetRegion != null)
                  Text(
                    '${widget.slangIdiom.targetRegion} dialect',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
              ],
            ),
          ],
          const SizedBox(height: 8),
          Text(
            'Total expressions: ${widget.slangIdiom.expressionCount}',
            style: TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
          ),
          if (widget.slangIdiom.slangCount > 0 ||
              widget.slangIdiom.idiomCount > 0) ...[
            const SizedBox(height: 4),
            Text(
              'Slang: ${widget.slangIdiom.slangCount}, Idioms: ${widget.slangIdiom.idiomCount}',
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build expression type information
  Widget _buildExpressionTypeInfo(ExpressionType type) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            type.icon,
            size: 20,
            color: type.color,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  type.displayName,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _getExpressionTypeDescription(type),
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build formality level information
  Widget _buildFormalityLevelInfo(FormalityLevel level) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: level.color.withAlpha(26),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.person_outline,
              size: 16,
              color: level.color,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  level.displayName,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                SizedBox(height: 2),
                Text(
                  _getFormalityLevelDescription(level),
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Get the language name from a language code
  String _getLanguageName(String code) {
    switch (code) {
      case 'en':
        return 'English';
      case 'fr':
        return 'French';
      case 'es':
        return 'Spanish';
      case 'yo':
        return 'Yoruba';
      case 'ig':
        return 'Igbo';
      case 'ha':
        return 'Hausa';
      case 'sw':
        return 'Swahili';
      default:
        return code;
    }
  }

  /// Get the description for an expression type
  String _getExpressionTypeDescription(ExpressionType type) {
    switch (type) {
      case ExpressionType.slang:
        return 'Informal language used by particular groups, often changing rapidly.';
      case ExpressionType.idiom:
        return 'Expressions that cannot be understood from the individual meanings of their words.';
      case ExpressionType.colloquialism:
        return 'Informal expressions used in everyday conversation rather than formal speech or writing.';
      case ExpressionType.proverb:
        return 'Short, well-known sayings that express a truth or give advice.';
      case ExpressionType.metaphor:
        return 'Expressions that describe something by referring to something else with similar qualities.';
      case ExpressionType.euphemism:
        return 'Mild or indirect expressions used in place of ones considered harsh or blunt.';
      case ExpressionType.jargon:
        return 'Special words or expressions used by a profession or group that are difficult for others to understand.';
    }
  }

  /// Get the description for a formality level
  String _getFormalityLevelDescription(FormalityLevel level) {
    switch (level) {
      case FormalityLevel.veryInformal:
        return 'Used among close friends, family, or in very casual settings. May be inappropriate in formal contexts.';
      case FormalityLevel.informal:
        return 'Used in casual, everyday conversations with friends, family, and peers.';
      case FormalityLevel.neutral:
        return 'Appropriate for most everyday situations, neither particularly formal nor informal.';
      case FormalityLevel.formal:
        return 'Used in professional settings, with people you don\'t know well, or in official communications.';
      case FormalityLevel.veryFormal:
        return 'Used in highly formal situations, official documents, or ceremonial contexts.';
    }
  }

  /// Get the empty state message based on the current filters
  String _getEmptyStateMessage() {
    if (_selectedType != null && _selectedFormalityLevel != null) {
      return 'No ${_selectedType!.displayName.toLowerCase()} expressions with ${_selectedFormalityLevel!.displayName.toLowerCase()} formality level found';
    } else if (_selectedType != null) {
      return 'No ${_selectedType!.displayName.toLowerCase()} expressions found';
    } else if (_selectedFormalityLevel != null) {
      return 'No expressions with ${_selectedFormalityLevel!.displayName.toLowerCase()} formality level found';
    } else if (!_showPotentiallyOffensiveContent &&
        widget.slangIdiom.hasPotentiallyOffensiveContent) {
      return 'Some expressions are hidden because they may be offensive. Enable "Show Potentially Offensive Content" to view them.';
    } else {
      return 'No expressions found';
    }
  }
}
