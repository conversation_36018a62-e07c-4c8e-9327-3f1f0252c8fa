import 'package:flutter/material.dart';
import 'package:culture_connect/models/translation/translation_confidence_model.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for displaying translation confidence
class TranslationConfidenceIndicator extends StatelessWidget {
  /// The confidence model
  final TranslationConfidenceModel confidence;

  /// Whether to show the label
  final bool showLabel;

  /// Whether to use a compact layout
  final bool compact;

  /// Whether to use light colors (for dark backgrounds)
  final bool useLight;

  /// Creates a new translation confidence indicator
  const TranslationConfidenceIndicator({
    super.key,
    required this.confidence,
    this.showLabel = true,
    this.compact = false,
    this.useLight = false,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Confidence icon
        Icon(
          confidence.confidenceLevel.icon,
          size: compact ? 14 : 18,
          color: useLight ? _getLightColor() : confidence.confidenceLevel.color,
        ),

        if (showLabel) ...[
          const SizedBox(width: 4),

          // Confidence label
          Text(
            compact ? _getShortLabel() : confidence.confidenceLevel.displayName,
            style: TextStyle(
              fontSize: compact ? 10 : 12,
              color: useLight ? Colors.white70 : AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ],
    );
  }

  /// Get a light version of the confidence color
  Color _getLightColor() {
    switch (confidence.confidenceLevel) {
      case ConfidenceLevel.veryHigh:
        return Colors.green[300]!;
      case ConfidenceLevel.high:
        return Colors.lightGreen[300]!;
      case ConfidenceLevel.medium:
        return Colors.amber[300]!;
      case ConfidenceLevel.low:
        return Colors.orange[300]!;
      case ConfidenceLevel.veryLow:
        return Colors.red[300]!;
    }
  }

  /// Get a short label for compact display
  String _getShortLabel() {
    switch (confidence.confidenceLevel) {
      case ConfidenceLevel.veryHigh:
        return 'V. High';
      case ConfidenceLevel.high:
        return 'High';
      case ConfidenceLevel.medium:
        return 'Med';
      case ConfidenceLevel.low:
        return 'Low';
      case ConfidenceLevel.veryLow:
        return 'V. Low';
    }
  }
}

/// A widget for displaying a detailed confidence view
class DetailedConfidenceView extends StatelessWidget {
  /// The confidence model
  final TranslationConfidenceModel confidence;

  /// Creates a new detailed confidence view
  const DetailedConfidenceView({
    super.key,
    required this.confidence,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          children: [
            Icon(
              confidence.confidenceLevel.icon,
              size: 20,
              color: confidence.confidenceLevel.color,
            ),
            const SizedBox(width: 8),
            Text(
              'Translation Confidence: ${confidence.confidenceLevel.displayName}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Confidence score
        Row(
          children: [
            const Text(
              'Overall Score:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            const SizedBox(width: 8),
            _buildConfidenceBar(confidence.overallScore),
            const SizedBox(width: 8),
            Text(
              '${(confidence.overallScore * 100).toStringAsFixed(0)}%',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppTheme.textPrimaryColor,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Segment scores (if available)
        if (confidence.segmentScores != null &&
            confidence.segmentScores!.isNotEmpty) ...[
          const Text(
            'Segment Confidence:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 8),
          ...confidence.segmentScores!
              .map((segment) => _buildSegmentItem(segment)),
        ],

        const SizedBox(height: 16),

        // Confidence factors (if available)
        if (confidence.factors != null && confidence.factors!.isNotEmpty) ...[
          const Text(
            'Confidence Factors:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 8),
          ...confidence.factors!.map((factor) => _buildFactorItem(factor)),
        ],
      ],
    );
  }

  /// Build a confidence bar
  Widget _buildConfidenceBar(double score) {
    final color = _getColorForScore(score);

    return Expanded(
      child: Container(
        height: 8,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(4),
        ),
        child: FractionallySizedBox(
          alignment: Alignment.centerLeft,
          widthFactor: score,
          child: Container(
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
      ),
    );
  }

  /// Build a segment item
  Widget _buildSegmentItem(SegmentConfidence segment) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            segment.text,
            style: const TextStyle(
              fontSize: 12,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              _buildConfidenceBar(segment.score),
              const SizedBox(width: 8),
              Text(
                '${(segment.score * 100).toStringAsFixed(0)}%',
                style: const TextStyle(
                  fontSize: 12,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build a factor item
  Widget _buildFactorItem(ConfidenceFactor factor) {
    final isPositive = factor.impact >= 0;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            isPositive ? Icons.arrow_upward : Icons.arrow_downward,
            size: 14,
            color: isPositive ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  factor.type,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                Text(
                  factor.description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '${isPositive ? '+' : ''}${(factor.impact * 100).toStringAsFixed(0)}%',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: isPositive ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  /// Get color for a confidence score
  Color _getColorForScore(double score) {
    if (score >= 0.9) {
      return Colors.green;
    } else if (score >= 0.75) {
      return Colors.lightGreen;
    } else if (score >= 0.5) {
      return Colors.amber;
    } else if (score >= 0.25) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
