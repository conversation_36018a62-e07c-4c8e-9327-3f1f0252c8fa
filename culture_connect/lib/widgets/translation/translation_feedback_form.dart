import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/translation/message_translation_metadata.dart';
import 'package:culture_connect/models/translation/translation_feedback_model.dart';
import 'package:culture_connect/providers/translation_feedback_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for providing translation feedback
class TranslationFeedbackForm extends ConsumerStatefulWidget {
  /// The message that was translated
  final MessageModel message;

  /// The translation metadata
  final MessageTranslationMetadata translationMetadata;

  /// Callback when feedback is submitted
  final VoidCallback? onFeedbackSubmitted;

  /// Creates a new translation feedback form
  const TranslationFeedbackForm({
    super.key,
    required this.message,
    required this.translationMetadata,
    this.onFeedbackSubmitted,
  });

  @override
  ConsumerState<TranslationFeedbackForm> createState() =>
      _TranslationFeedbackFormState();
}

class _TranslationFeedbackFormState
    extends ConsumerState<TranslationFeedbackForm> {
  final TextEditingController _correctionController = TextEditingController();
  final TextEditingController _commentsController = TextEditingController();

  TranslationQuality _selectedQuality = TranslationQuality.fair;
  TranslationFeedbackType _selectedFeedbackType =
      TranslationFeedbackType.general;

  bool _isSubmitting = false;

  @override
  void dispose() {
    _correctionController.dispose();
    _commentsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          const Text(
            'Translation Feedback',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),

          const SizedBox(height: 16),

          // Original text
          _buildTextSection(
            'Original Text (${widget.translationMetadata.sourceLanguage})',
            widget.translationMetadata.originalText,
          ),

          const SizedBox(height: 16),

          // Translated text
          _buildTextSection(
            'Translated Text (${widget.translationMetadata.targetLanguage})',
            widget.translationMetadata.translatedText,
          ),

          const SizedBox(height: 24),

          // Quality rating
          const Text(
            'How would you rate this translation?',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppTheme.textPrimaryColor,
            ),
          ),

          const SizedBox(height: 8),

          _buildQualitySelector(),

          const SizedBox(height: 24),

          // Feedback type
          const Text(
            'What type of feedback are you providing?',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppTheme.textPrimaryColor,
            ),
          ),

          const SizedBox(height: 8),

          _buildFeedbackTypeSelector(),

          const SizedBox(height: 24),

          // Suggested correction
          const Text(
            'Suggested Correction (Optional)',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppTheme.textPrimaryColor,
            ),
          ),

          const SizedBox(height: 8),

          TextField(
            controller: _correctionController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Enter your suggested translation...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.all(12),
            ),
          ),

          const SizedBox(height: 24),

          // Additional comments
          const Text(
            'Additional Comments (Optional)',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppTheme.textPrimaryColor,
            ),
          ),

          const SizedBox(height: 8),

          TextField(
            controller: _commentsController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Enter any additional comments...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.all(12),
            ),
          ),

          const SizedBox(height: 24),

          // Submit button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isSubmitting ? null : _submitFeedback,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _isSubmitting
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : const Text(
                      'Submit Feedback',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build a text section
  Widget _buildTextSection(String title, String text) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppTheme.textSecondaryColor,
          ),
        ),
        const SizedBox(height: 4),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.grey[300]!,
              width: 1,
            ),
          ),
          child: Text(
            text,
            style: const TextStyle(
              fontSize: 16,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ),
      ],
    );
  }

  /// Build the quality selector
  Widget _buildQualitySelector() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: TranslationQuality.values.map((quality) {
        final isSelected = quality == _selectedQuality;

        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedQuality = quality;
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color:
                  isSelected ? quality.color.withAlpha(51) : Colors.grey[100],
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isSelected ? quality.color : Colors.grey[300]!,
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  quality.icon,
                  size: 16,
                  color: isSelected ? quality.color : Colors.grey,
                ),
                const SizedBox(width: 4),
                Text(
                  quality.displayName,
                  style: TextStyle(
                    fontSize: 14,
                    color: isSelected ? quality.color : Colors.grey[700],
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  /// Build the feedback type selector
  Widget _buildFeedbackTypeSelector() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: TranslationFeedbackType.values.map((type) {
        final isSelected = type == _selectedFeedbackType;

        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedFeedbackType = type;
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppTheme.primaryColor.withAlpha(26)
                  : Colors.grey[100],
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isSelected ? AppTheme.primaryColor : Colors.grey[300]!,
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  type.icon,
                  size: 16,
                  color: isSelected ? AppTheme.primaryColor : Colors.grey,
                ),
                const SizedBox(width: 4),
                Text(
                  type.displayName,
                  style: TextStyle(
                    fontSize: 14,
                    color:
                        isSelected ? AppTheme.primaryColor : Colors.grey[700],
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  /// Submit feedback
  Future<void> _submitFeedback() async {
    if (_isSubmitting) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      await ref
          .read(translationFeedbackNotifierProvider.notifier)
          .submitFeedback(
            messageId: widget.message.id,
            sourceLanguage: widget.translationMetadata.sourceLanguage,
            targetLanguage: widget.translationMetadata.targetLanguage,
            originalText: widget.translationMetadata.originalText,
            translatedText: widget.translationMetadata.translatedText,
            suggestedCorrection: _correctionController.text.isNotEmpty
                ? _correctionController.text
                : null,
            quality: _selectedQuality,
            feedbackType: _selectedFeedbackType,
            comments: _commentsController.text.isNotEmpty
                ? _commentsController.text
                : null,
            originalConfidence:
                widget.translationMetadata.confidence?.overallScore,
          );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Thank you for your feedback!'),
            backgroundColor: AppTheme.primaryColor,
            duration: Duration(seconds: 2),
          ),
        );

        Navigator.pop(context);

        if (widget.onFeedbackSubmitted != null) {
          widget.onFeedbackSubmitted!();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to submit feedback: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}
