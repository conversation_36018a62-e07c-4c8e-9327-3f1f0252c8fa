import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/widgets/common/rating_display.dart';

/// A card for displaying a featured travel service
class FeaturedTravelServiceCard extends StatelessWidget {
  /// The travel service to display
  final TravelService travelService;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Creates a new featured travel service card
  const FeaturedTravelServiceCard({
    super.key,
    required this.travelService,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: onTap,
        child: SizedBox(
          width: 280,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image and badges
              Stack(
                children: [
                  // Image
                  SizedBox(
                    height: 140,
                    width: double.infinity,
                    child: Image.network(
                      travelService.imageUrl,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: theme.colorScheme.surfaceContainerHighest,
                          child: Center(
                            child: Icon(
                              Icons.image_not_supported,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        );
                      },
                    ),
                  ),

                  // Featured badge
                  Positioned(
                    top: 8,
                    left: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.star,
                            color: Colors.white,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Featured',
                            style: theme.textTheme.labelSmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Type badge
                  Positioned(
                    bottom: 8,
                    left: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getTravelServiceTypeColor(travelService),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _getTravelServiceTypeIcon(travelService),
                            color: Colors.white,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _getTravelServiceTypeName(travelService),
                            style: theme.textTheme.labelSmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Sale badge
                  if (travelService.isOnSale)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          '${travelService.discountPercentage?.toStringAsFixed(0) ?? ''}% OFF',
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                ],
              ),

              // Content
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    Text(
                      travelService.name,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),

                    // Location
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          size: 16,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            travelService.location,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),

                    // Rating and price
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Rating
                        Row(
                          children: [
                            RatingDisplay(
                              rating: travelService.rating,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '(${travelService.reviewCount})',
                              style: theme.textTheme.bodySmall,
                            ),
                          ],
                        ),

                        // Price
                        Row(
                          children: [
                            if (travelService.isOnSale &&
                                travelService.originalPrice != null) ...[
                              Text(
                                travelService.formattedOriginalPrice!,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  decoration: TextDecoration.lineThrough,
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                              const SizedBox(width: 4),
                            ],
                            Text(
                              travelService.formattedPrice,
                              style: theme.textTheme.titleSmall?.copyWith(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Get the color for the travel service type
  Color _getTravelServiceTypeColor(TravelService travelService) {
    if (travelService is CarRental) {
      return Colors.blue;
    } else if (travelService is PrivateSecurity) {
      return Colors.red;
    } else if (travelService is Hotel) {
      return Colors.purple;
    } else if (travelService is Restaurant) {
      return Colors.orange;
    } else if (travelService is Flight) {
      return Colors.lightBlue;
    } else if (travelService is Cruise) {
      return Colors.teal;
    } else {
      return Colors.grey;
    }
  }

  /// Get the icon for the travel service type
  IconData _getTravelServiceTypeIcon(TravelService travelService) {
    if (travelService is CarRental) {
      return Icons.directions_car;
    } else if (travelService is PrivateSecurity) {
      return Icons.security;
    } else if (travelService is Hotel) {
      return Icons.hotel;
    } else if (travelService is Restaurant) {
      return Icons.restaurant;
    } else if (travelService is Flight) {
      return Icons.flight;
    } else if (travelService is Cruise) {
      return Icons.directions_boat;
    } else {
      return Icons.category;
    }
  }

  /// Get the name for the travel service type
  String _getTravelServiceTypeName(TravelService travelService) {
    if (travelService is CarRental) {
      return 'Car Rental';
    } else if (travelService is PrivateSecurity) {
      return 'Security';
    } else if (travelService is Hotel) {
      return 'Hotel';
    } else if (travelService is Restaurant) {
      return 'Restaurant';
    } else if (travelService is Flight) {
      return 'Flight';
    } else if (travelService is Cruise) {
      return 'Cruise';
    } else {
      return 'Other';
    }
  }
}
