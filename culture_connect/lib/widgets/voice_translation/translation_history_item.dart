import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/translation/voice_translation_model.dart';
import 'package:culture_connect/providers/voice_translation_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for displaying a translation history item
class TranslationHistoryItem extends ConsumerWidget {
  /// The translation to display
  final VoiceTranslationModel translation;

  /// Callback when the item is tapped
  final VoidCallback? onTap;

  /// Creates a new translation history item
  const TranslationHistoryItem({
    super.key,
    required this.translation,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  // Status icon
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: translation.status.color.withAlpha(25),
                    ),
                    child: Center(
                      child: Icon(
                        translation.status.icon,
                        size: 20,
                        color: translation.status.color,
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Languages
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${translation.getSourceLanguageName()} → ${translation.getTargetLanguageName()}',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          translation.formattedTimestamp,
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Favorite button
                  IconButton(
                    icon: Icon(
                      translation.isFavorite
                          ? Icons.favorite
                          : Icons.favorite_border,
                      color: translation.isFavorite ? Colors.red : Colors.grey,
                      size: 24,
                    ),
                    onPressed: () {
                      ref.read(currentVoiceTranslationProvider.notifier).state =
                          translation;
                      ref
                          .read(voiceTranslationNotifierProvider.notifier)
                          .toggleFavorite();
                    },
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Content preview
              if (translation.status == VoiceTranslationStatus.completed) ...[
                // Original text preview
                if (translation.originalText != null &&
                    translation.originalText!.isNotEmpty)
                  _buildTextPreview(
                    'Original:',
                    translation.originalText!,
                  ),

                const SizedBox(height: 8),

                // Translated text preview
                if (translation.translatedText != null &&
                    translation.translatedText!.isNotEmpty)
                  _buildTextPreview(
                    'Translation:',
                    translation.translatedText!,
                  ),
              ] else if (translation.status ==
                  VoiceTranslationStatus.error) ...[
                // Error message
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red.withAlpha(25),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 16,
                        color: Colors.red,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          translation.errorMessage ?? 'An error occurred',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.red,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ] else if (translation.status ==
                  VoiceTranslationStatus.processing) ...[
                // Processing indicator
                const Row(
                  children: [
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                      ),
                    ),
                    SizedBox(width: 8),
                    Text(
                      'Processing translation...',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ],

              const SizedBox(height: 12),

              // Footer
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // Play button
                  if (translation.status == VoiceTranslationStatus.completed &&
                      (translation.originalAudioPath != null ||
                          translation.translatedAudioPath != null))
                    TextButton.icon(
                      icon: const Icon(
                        Icons.play_arrow,
                        size: 16,
                      ),
                      label: const Text(
                        'Play',
                        style: TextStyle(
                          fontSize: 12,
                        ),
                      ),
                      onPressed: () {
                        ref
                            .read(currentVoiceTranslationProvider.notifier)
                            .state = translation;
                        if (translation.translatedAudioPath != null) {
                          ref
                              .read(voiceTranslationNotifierProvider.notifier)
                              .playTranslatedAudio();
                        } else if (translation.originalAudioPath != null) {
                          ref
                              .read(voiceTranslationNotifierProvider.notifier)
                              .playOriginalAudio();
                        }
                      },
                    ),

                  const SizedBox(width: 8),

                  // Delete button
                  TextButton.icon(
                    icon: const Icon(
                      Icons.delete,
                      size: 16,
                      color: Colors.red,
                    ),
                    label: const Text(
                      'Delete',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.red,
                      ),
                    ),
                    onPressed: () {
                      _confirmDelete(context, ref);
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextPreview(String label, String text) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: AppTheme.textSecondaryColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          text,
          style: const TextStyle(
            fontSize: 14,
            color: AppTheme.textPrimaryColor,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  void _confirmDelete(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Translation'),
        content:
            const Text('Are you sure you want to delete this translation?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ref.read(currentVoiceTranslationProvider.notifier).state =
                  translation;
              ref
                  .read(voiceTranslationNotifierProvider.notifier)
                  .deleteTranslation();
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
