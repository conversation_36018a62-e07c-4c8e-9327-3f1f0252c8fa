import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:culture_connect/services/weather_service.dart';
import 'package:culture_connect/services/time_zone_service.dart';
import 'package:intl/intl.dart';

/// A widget for displaying weather information for a location
class WeatherInfoCard extends ConsumerWidget {
  final LatLng coordinates;
  final bool showForecast;
  final bool showLocalTime;
  final VoidCallback? onTap;

  const WeatherInfoCard({
    super.key,
    required this.coordinates,
    this.showForecast = false,
    this.showLocalTime = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final weatherAsync = ref.watch(weatherProvider(coordinates));
    final localTimeAsync = ref.watch(localTimeProvider(coordinates));
    final forecastAsync = showForecast
        ? ref.watch(forecastProvider(coordinates))
        : const AsyncValue<List<WeatherData>>.data([]);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.wb_sunny, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Weather',
                    style: theme.textTheme.titleMedium,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              weatherAsync.when(
                data: (weather) => _buildWeatherInfo(context, weather),
                loading: () => const _LoadingWeatherInfo(),
                error: (error, _) => _ErrorWeatherInfo(error: error.toString()),
              ),
              if (showLocalTime) ...[
                const SizedBox(height: 16),
                Row(
                  children: [
                    const Icon(Icons.access_time, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'Local Time',
                      style: theme.textTheme.titleMedium,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                localTimeAsync.when(
                  data: (localTime) => _buildLocalTimeInfo(context, localTime),
                  loading: () => const _LoadingLocalTimeInfo(),
                  error: (error, _) =>
                      _ErrorLocalTimeInfo(error: error.toString()),
                ),
              ],
              if (showForecast) ...[
                const SizedBox(height: 16),
                Row(
                  children: [
                    const Icon(Icons.calendar_today, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'Forecast',
                      style: theme.textTheme.titleMedium,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                forecastAsync.when(
                  data: (forecast) => _buildForecastInfo(context, forecast),
                  loading: () => const _LoadingForecastInfo(),
                  error: (error, _) =>
                      _ErrorForecastInfo(error: error.toString()),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWeatherInfo(BuildContext context, WeatherData weather) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Image.network(
          weather.weatherIconUrl,
          width: 50,
          height: 50,
          errorBuilder: (context, error, stackTrace) => const Icon(
            Icons.wb_sunny,
            size: 50,
            color: Colors.amber,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                weather.formattedTemperature,
                style: theme.textTheme.headlineSmall,
              ),
              Text(
                weather.weatherDescription,
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Row(
              children: [
                const Icon(Icons.water_drop, size: 16, color: Colors.blue),
                const SizedBox(width: 4),
                Text('${weather.humidity}%'),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.air, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text('${weather.windSpeed} m/s'),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLocalTimeInfo(BuildContext context, DateTime localTime) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('EEEE, MMMM d, yyyy');
    final timeFormat = DateFormat('h:mm a');

    return Row(
      children: [
        const Icon(Icons.access_time, size: 40, color: Colors.blueGrey),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                timeFormat.format(localTime),
                style: theme.textTheme.titleLarge,
              ),
              Text(
                dateFormat.format(localTime),
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildForecastInfo(BuildContext context, List<WeatherData> forecast) {
    if (forecast.isEmpty) {
      return const Text('No forecast data available');
    }

    return SizedBox(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: forecast.length.clamp(0, 5),
        itemBuilder: (context, index) {
          final day = forecast[index];
          return _ForecastDayItem(weather: day);
        },
      ),
    );
  }
}

class _LoadingWeatherInfo extends StatelessWidget {
  const _LoadingWeatherInfo();

  @override
  Widget build(BuildContext context) {
    return const SizedBox(
      height: 50,
      child: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}

class _ErrorWeatherInfo extends StatelessWidget {
  final String error;

  const _ErrorWeatherInfo({required this.error});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 50,
      child: Center(
        child: Text(
          'Failed to load weather data',
          style: TextStyle(color: Theme.of(context).colorScheme.error),
        ),
      ),
    );
  }
}

class _LoadingLocalTimeInfo extends StatelessWidget {
  const _LoadingLocalTimeInfo();

  @override
  Widget build(BuildContext context) {
    return const SizedBox(
      height: 50,
      child: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}

class _ErrorLocalTimeInfo extends StatelessWidget {
  final String error;

  const _ErrorLocalTimeInfo({required this.error});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 50,
      child: Center(
        child: Text(
          'Failed to load local time data',
          style: TextStyle(color: Theme.of(context).colorScheme.error),
        ),
      ),
    );
  }
}

class _LoadingForecastInfo extends StatelessWidget {
  const _LoadingForecastInfo();

  @override
  Widget build(BuildContext context) {
    return const SizedBox(
      height: 100,
      child: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}

class _ErrorForecastInfo extends StatelessWidget {
  final String error;

  const _ErrorForecastInfo({required this.error});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 100,
      child: Center(
        child: Text(
          'Failed to load forecast data',
          style: TextStyle(color: Theme.of(context).colorScheme.error),
        ),
      ),
    );
  }
}

class _ForecastDayItem extends StatelessWidget {
  final WeatherData weather;

  const _ForecastDayItem({required this.weather});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final dayFormat = DateFormat('E');

    return Container(
      width: 70,
      margin: EdgeInsets.only(right: 8),
      padding: EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            dayFormat.format(weather.timestamp),
            style: theme.textTheme.bodySmall,
          ),
          Image.network(
            weather.weatherIconUrl,
            width: 30,
            height: 30,
            errorBuilder: (context, error, stackTrace) => const Icon(
              Icons.wb_sunny,
              size: 30,
              color: Colors.amber,
            ),
          ),
          Text(
            weather.formattedTemperature,
            style: theme.textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }
}
