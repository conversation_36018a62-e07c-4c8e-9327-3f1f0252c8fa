import 'dart:math';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/services/analytics_service.dart';
import 'package:culture_connect/services/error_handling_service.dart';

/// A class that manages A/B testing experiments in the app
class ABTestingService {
  // Singleton instance
  static final ABTestingService _instance = ABTestingService._internal();
  factory ABTestingService() => _instance;
  ABTestingService._internal() {
    final errorHandlingService = ErrorHandlingService(_loggingService);
    _analyticsService = AnalyticsService(_loggingService, errorHandlingService);
  }

  // Dependencies
  final LoggingService _loggingService = LoggingService();
  late final AnalyticsService _analyticsService;

  // Random number generator for variant assignment
  final Random _random = Random();

  // Cache of active experiments and assigned variants
  final Map<String, String> _assignedVariants = {};

  // Flag to track if the service has been initialized
  bool _isInitialized = false;

  /// Initialize the A/B testing service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Load previously assigned variants from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final savedVariants = prefs.getStringList('ab_testing_variants') ?? [];

      for (final variantString in savedVariants) {
        final parts = variantString.split(':');
        if (parts.length == 2) {
          _assignedVariants[parts[0]] = parts[1];
        }
      }

      _isInitialized = true;
      _loggingService.info('ABTestingService',
          'Initialized with ${_assignedVariants.length} saved variants');
    } catch (e, stackTrace) {
      _loggingService.error(
          'ABTestingService', 'Failed to initialize', e, stackTrace);
    }
  }

  /// Get the assigned variant for an experiment
  ///
  /// If the user has not been assigned a variant yet, a random variant will be assigned
  /// based on the provided weights. The assignment will be persisted for future sessions.
  ///
  /// [experimentId] - The unique identifier for the experiment
  /// [variants] - The list of variant identifiers
  /// [weights] - Optional weights for each variant (must match the length of variants)
  ///
  /// Returns the assigned variant identifier
  Future<String> getVariant(
    String experimentId,
    List<String> variants, {
    List<double>? weights,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    // If the user has already been assigned a variant, return it
    if (_assignedVariants.containsKey(experimentId)) {
      return _assignedVariants[experimentId]!;
    }

    // Validate weights if provided
    if (weights != null && weights.length != variants.length) {
      throw ArgumentError('Weights must have the same length as variants');
    }

    // Assign a random variant based on weights
    String assignedVariant;
    if (weights == null) {
      // Equal weights for all variants
      assignedVariant = variants[_random.nextInt(variants.length)];
    } else {
      // Weighted random selection
      final totalWeight = weights.fold(0.0, (sum, weight) => sum + weight);
      final randomValue = _random.nextDouble() * totalWeight;

      double cumulativeWeight = 0.0;
      assignedVariant = variants.last; // Default to last variant

      for (int i = 0; i < variants.length; i++) {
        cumulativeWeight += weights[i];
        if (randomValue <= cumulativeWeight) {
          assignedVariant = variants[i];
          break;
        }
      }
    }

    // Save the assigned variant
    _assignedVariants[experimentId] = assignedVariant;
    await _saveAssignedVariants();

    // Log the assignment for analytics
    _analyticsService.logEvent(
      name: 'ab_test_assignment',
      category: AnalyticsCategory.userAction,
      parameters: {
        'experiment_id': experimentId,
        'variant': assignedVariant,
      },
    );

    return assignedVariant;
  }

  /// Log a conversion event for an experiment
  ///
  /// [experimentId] - The unique identifier for the experiment
  /// [conversionEvent] - The name of the conversion event
  /// [parameters] - Optional additional parameters for the event
  Future<void> logConversion(
    String experimentId,
    String conversionEvent, {
    Map<String, dynamic>? parameters,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (!_assignedVariants.containsKey(experimentId)) {
      _loggingService.warning(
        'ABTestingService',
        'Attempted to log conversion for experiment $experimentId, but user is not in the experiment',
      );
      return;
    }

    final variant = _assignedVariants[experimentId]!;
    final eventParameters = {
      'experiment_id': experimentId,
      'variant': variant,
      'conversion_event': conversionEvent,
      ...?parameters,
    };

    _analyticsService.logEvent(
      name: 'ab_test_conversion',
      category: AnalyticsCategory.userAction,
      parameters: eventParameters,
    );
  }

  /// Save the assigned variants to SharedPreferences
  Future<void> _saveAssignedVariants() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final variantStrings = _assignedVariants.entries
          .map((entry) => '${entry.key}:${entry.value}')
          .toList();

      await prefs.setStringList('ab_testing_variants', variantStrings);
    } catch (e, stackTrace) {
      _loggingService.error(
          'ABTestingService', 'Failed to save variants', e, stackTrace);
    }
  }

  /// Reset all experiment assignments
  ///
  /// This is primarily used for testing purposes
  Future<void> resetAllExperiments() async {
    _assignedVariants.clear();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('ab_testing_variants');
      _loggingService.info(
          'ABTestingService', 'Reset all experiment assignments');
    } catch (e, stackTrace) {
      _loggingService.error(
          'ABTestingService', 'Failed to reset experiments', e, stackTrace);
    }
  }
}

/// A widget that renders different UI variants based on an A/B test
class ABTestWidget extends StatefulWidget {
  /// The unique identifier for the experiment
  final String experimentId;

  /// The map of variant identifiers to widget builders
  final Map<String, WidgetBuilder> variants;

  /// Optional weights for each variant
  final Map<String, double>? weights;

  /// Widget to show while the variant is being determined
  final Widget? loadingWidget;

  const ABTestWidget({
    super.key,
    required this.experimentId,
    required this.variants,
    this.weights,
    this.loadingWidget,
  });

  @override
  State<ABTestWidget> createState() => _ABTestWidgetState();
}

class _ABTestWidgetState extends State<ABTestWidget> {
  String? _assignedVariant;
  final ABTestingService _abTestingService = ABTestingService();

  @override
  void initState() {
    super.initState();
    _determineVariant();
  }

  Future<void> _determineVariant() async {
    final variantIds = widget.variants.keys.toList();
    List<double>? weights;

    if (widget.weights != null) {
      weights = variantIds.map((id) => widget.weights![id] ?? 1.0).toList();
    }

    final variant = await _abTestingService.getVariant(
      widget.experimentId,
      variantIds,
      weights: weights,
    );

    if (mounted) {
      setState(() {
        _assignedVariant = variant;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_assignedVariant == null) {
      return widget.loadingWidget ?? const CircularProgressIndicator();
    }

    final builder = widget.variants[_assignedVariant];
    if (builder == null) {
      // Fallback to the first variant if the assigned variant is not found
      return widget.variants.values.first(context);
    }

    return builder(context);
  }
}
