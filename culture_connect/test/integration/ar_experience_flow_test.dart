import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:culture_connect/main.dart' as app;
import 'package:culture_connect/services/auth_service.dart';
import 'package:culture_connect/services/ar_backend_service.dart';
import 'package:culture_connect/services/ar_voice_command_service.dart';
import 'package:culture_connect/services/ar_recording_service.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/models/landmark.dart';

// Generate mocks for dependencies
@GenerateMocks(
    [AuthService, ARBackendService, ARVoiceCommandService, ARRecordingService])
import 'ar_experience_flow_test.mocks.dart';

// Mock providers
final mockAuthServiceProvider = Provider<AuthService>((ref) {
  return MockAuthService();
});

final mockARBackendServiceProvider = Provider<ARBackendService>((ref) {
  return MockARBackendService();
});

final mockARVoiceCommandServiceProvider =
    Provider<ARVoiceCommandService>((ref) {
  return MockARVoiceCommandService();
});

final mockARRecordingServiceProvider = Provider<ARRecordingService>((ref) {
  return MockARRecordingService();
});

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  late MockAuthService mockAuthService;
  late MockARBackendService mockARBackendService;
  late MockARVoiceCommandService mockARVoiceCommandService;
  late MockARRecordingService mockARRecordingService;

  setUp(() {
    mockAuthService = MockAuthService();
    mockARBackendService = MockARBackendService();
    mockARVoiceCommandService = MockARVoiceCommandService();
    mockARRecordingService = MockARRecordingService();

    // Setup mock auth service
    when(mockAuthService.currentUserModel).thenAnswer((_) async => UserModel(
          id: 'test-uid',
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>',
          phoneNumber: '+**********',
          userType: 'tourist',
          isVerified: true,
          verificationLevel: 1,
          status: 'active',
          createdAt: DateTime.now().toIso8601String(),
          updatedAt: DateTime.now().toIso8601String(),
          lastLogin: DateTime.now().toIso8601String(),
          emailVerified: true,
        ));

    // Setup mock AR backend service
    when(mockARBackendService.initialize()).thenAnswer((_) async => true);
    when(mockARBackendService.fetchNearbyLandmarks(
      latitude: anyNamed('latitude'),
      longitude: anyNamed('longitude'),
      radius: anyNamed('radius'),
    )).thenAnswer((_) async => [
          Landmark(
            id: '1',
            name: 'Eiffel Tower',
            description: 'Famous landmark in Paris',
            latitude: 48.8584,
            longitude: 2.2945,
            hasArContent: true,
            arContentId: 'ar-eiffel-tower',
            imageUrl: 'https://example.com/eiffel.jpg',
          ),
          Landmark(
            id: '2',
            name: 'Statue of Liberty',
            description: 'Famous landmark in New York',
            latitude: 40.6892,
            longitude: -74.0445,
            hasArContent: true,
            arContentId: 'ar-statue-liberty',
            imageUrl: 'https://example.com/liberty.jpg',
          ),
        ]);
    when(mockARBackendService.downloadArContent(
      arContentId: anyNamed('arContentId'),
    )).thenAnswer((_) async => true);

    // Setup mock AR voice command service
    when(mockARVoiceCommandService.initialize()).thenAnswer((_) async => true);
    when(mockARVoiceCommandService.isVoiceCommandsEnabled).thenReturn(true);
    when(mockARVoiceCommandService.startListening())
        .thenAnswer((_) async => true);
    when(mockARVoiceCommandService.stopListening())
        .thenAnswer((_) async => true);

    // Setup mock AR recording service
    when(mockARRecordingService.initialize(
      onRecordingStateChanged: anyNamed('onRecordingStateChanged'),
      onRecordingTimerUpdated: anyNamed('onRecordingTimerUpdated'),
    )).thenAnswer((_) async => true);
    when(mockARRecordingService.isRecording).thenReturn(false);
    when(mockARRecordingService.startRecording()).thenAnswer((_) async => true);
    when(mockARRecordingService.stopRecording())
        .thenAnswer((_) async => 'recording.mp4');
    when(mockARRecordingService.takeScreenshot())
        .thenAnswer((_) async => 'screenshot.jpg');
  });

  testWidgets('Complete AR experience flow test', (WidgetTester tester) async {
    // Override the providers
    app.main();
    await tester.pumpAndSettle();

    // Should show splash screen initially
    expect(find.text('CultureConnect'), findsOneWidget);

    // Wait for splash screen animation and initialization
    await tester.pump(const Duration(seconds: 3));
    await tester.pumpAndSettle();

    // Should navigate to main navigation (since we're mocking an authenticated user)
    expect(find.text('Explore'), findsOneWidget);

    // Tap the AR tab
    await tester.tap(find.text('AR'));
    await tester.pumpAndSettle();

    // Should show AR explore screen
    expect(find.text('AR Explore'), findsOneWidget);

    // Wait for AR initialization
    await tester.pump(const Duration(seconds: 3));

    // Should show landmarks
    expect(find.text('Eiffel Tower'), findsOneWidget);
    expect(find.text('Statue of Liberty'), findsOneWidget);

    // Tap on a landmark
    await tester.tap(find.text('Eiffel Tower'));
    await tester.pumpAndSettle();

    // Should show landmark details
    expect(find.text('Famous landmark in Paris'), findsOneWidget);

    // Tap the close button to dismiss landmark details
    await tester.tap(find.byIcon(Icons.close));
    await tester.pumpAndSettle();

    // Tap the voice command button
    await tester.tap(find.byIcon(Icons.mic));
    await tester.pumpAndSettle();

    // Should show voice command UI
    expect(find.text('Listening...'), findsOneWidget);

    // Tap to stop listening
    await tester.tap(find.text('Tap to stop'));
    await tester.pumpAndSettle();

    // Tap the recording button
    await tester.tap(find.byIcon(Icons.videocam));
    await tester.pumpAndSettle();

    // Should show recording controls
    expect(find.byIcon(Icons.stop), findsOneWidget);

    // Tap the stop button
    await tester.tap(find.byIcon(Icons.stop));
    await tester.pumpAndSettle();

    // Should show share options
    expect(find.text('Share Recording'), findsOneWidget);

    // Tap the close button to dismiss share options
    await tester.tap(find.byIcon(Icons.close));
    await tester.pumpAndSettle();

    // Tap the screenshot button
    await tester.tap(find.byIcon(Icons.camera_alt));
    await tester.pumpAndSettle();

    // Should show screenshot preview
    expect(find.text('Screenshot Captured'), findsOneWidget);

    // Tap the close button to dismiss screenshot preview
    await tester.tap(find.byIcon(Icons.close));
    await tester.pumpAndSettle();

    // Tap the settings button
    await tester.tap(find.byIcon(Icons.settings));
    await tester.pumpAndSettle();

    // Should show AR settings screen
    expect(find.text('AR Settings'), findsOneWidget);

    // Toggle high contrast mode
    await tester.tap(find.text('High Contrast Mode'));
    await tester.pumpAndSettle();

    // Tap the back button
    await tester.tap(find.byIcon(Icons.arrow_back));
    await tester.pumpAndSettle();

    // Should return to AR explore screen
    expect(find.text('AR Explore'), findsOneWidget);

    // Tap the back button to exit AR explore
    await tester.tap(find.byIcon(Icons.arrow_back));
    await tester.pumpAndSettle();

    // Should return to main navigation
    expect(find.text('Explore'), findsOneWidget);
  });
}
