import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:culture_connect/main.dart' as app;
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/group_chat_model.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/models/translation/group_translation_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/group_chat_provider.dart';
import 'package:culture_connect/providers/group_translation_provider.dart';
import 'package:culture_connect/screens/messaging/group_chat_screen.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:math';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  // Sample data for testing
  const sampleGroupId = 'group-123';
  const sampleUserId = 'user-456';

  final sampleUser = UserModel(
    id: sampleUserId,
    firstName: 'Test',
    lastName: 'User',
    email: '<EMAIL>',
    phoneNumber: '+**********',
    userType: 'tourist',
    isVerified: true,
    verificationLevel: 1,
    status: 'active',
    createdAt: DateTime.now().toIso8601String(),
    updatedAt: DateTime.now().toIso8601String(),
    lastLogin: DateTime.now().toIso8601String(),
    emailVerified: true,
  );

  final sampleGroupChat = GroupChatModel(
    id: sampleGroupId,
    name: 'Test Group',
    description: 'A test group for performance testing',
    members: {
      sampleUserId: GroupMember(
        userId: sampleUserId,
        role: GroupMemberRole.member,
        joinedAt: DateTime.now().subtract(const Duration(days: 7)),
      ),
      'user-789': GroupMember(
        userId: 'user-789',
        role: GroupMemberRole.admin,
        joinedAt: DateTime.now().subtract(const Duration(days: 7)),
      ),
    },
    createdAt: DateTime.now().subtract(const Duration(days: 7)),
    lastMessageAt: DateTime.now(),
    lastMessageText: 'Hello, world!',
    lastMessageSenderId: 'user-789',
    isActive: true,
  );

  // Generate a large number of messages for performance testing
  List<MessageModel> generateLargeMessageList(int count) {
    final random = Random();
    final languages = ['en', 'fr', 'es', 'yo', 'ig', 'ha', 'sw'];
    final senders = [sampleUserId, 'user-789'];
    final messages = <MessageModel>[];

    for (int i = 0; i < count; i++) {
      final language = languages[random.nextInt(languages.length)];
      final sender = senders[random.nextInt(senders.length)];

      messages.add(MessageModel(
        id: 'message-$i',
        chatId: sampleGroupId,
        senderId: sender,
        recipientId: '',
        text: 'This is test message number $i in $language',
        timestamp: DateTime.now().subtract(Duration(minutes: count - i)),
        status: MessageStatus.sent,
        type: MessageType.text,
        originalLanguage: language,
      ));
    }

    return messages;
  }

  final sampleGroupMembers = {
    sampleUserId: sampleUser,
    'user-789': UserModel(
      id: 'user-789',
      firstName: 'Other',
      lastName: 'User',
      email: '<EMAIL>',
      phoneNumber: '+1234567891',
      userType: 'tourist',
      isVerified: true,
      verificationLevel: 1,
      status: 'active',
      createdAt: DateTime.now().toIso8601String(),
      updatedAt: DateTime.now().toIso8601String(),
      lastLogin: DateTime.now().toIso8601String(),
      emailVerified: true,
    ),
  };

  final sampleParticipantPreference = ParticipantLanguagePreference(
    userId: sampleUserId,
    displayName: 'Test User',
    preferredLanguage:
        const LanguageModel(code: 'en', name: 'English', flag: '🇺🇸'),
    autoTranslate: true,
    showOriginalText: false,
  );

  final sampleGroupSettings = GroupTranslationSettings(
    groupId: sampleGroupId,
    participantPreferences: {
      sampleUserId: sampleParticipantPreference,
    },
    enableRealTimeTranslation: true,
  );

  group('Group Translation Performance Tests', () {
    testWidgets('Measure rendering performance with 100 messages',
        (WidgetTester tester) async {
      // Generate 100 messages
      final messages = generateLargeMessageList(100);

      // Create a ProviderContainer with overrides for testing
      final container = ProviderContainer(
        overrides: [
          // Override group chat providers
          groupChatDetailsProvider(sampleGroupId)
              .overrideWith((ref) => Stream.value(sampleGroupChat)),

          groupChatMessagesProvider(sampleGroupId)
              .overrideWith((ref) => Stream.value(messages)),

          groupChatMembersProvider(sampleGroupId).overrideWith(
              (ref) => Future.value(sampleGroupMembers.values.toList())),

          // Override group translation providers
          groupTranslationSettingsProvider(sampleGroupId)
              .overrideWith((ref) => Future.value(sampleGroupSettings)),

          participantLanguagePreferenceProvider((
            groupId: sampleGroupId,
            userId: sampleUserId,
          )).overrideWith((ref) => Future.value(sampleParticipantPreference)),
        ],
      );

      // Measure the time it takes to render the screen
      final stopwatch = Stopwatch()..start();

      // Build the app with the overridden providers
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            home: GroupChatScreen(groupId: sampleGroupId),
          ),
        ),
      );

      // Wait for the screen to load
      await tester.pumpAndSettle();

      stopwatch.stop();

      // Print the time it took to render
      print('Time to render 100 messages: ${stopwatch.elapsedMilliseconds}ms');

      // Verify some messages are displayed
      expect(find.textContaining('This is test message'), findsWidgets);
      expect(find.textContaining('Translated:'), findsWidgets);
    });

    testWidgets('Measure scrolling performance with 500 messages',
        (WidgetTester tester) async {
      // Generate 500 messages
      final messages = generateLargeMessageList(500);

      // Create a ProviderContainer with overrides for testing
      final container = ProviderContainer(
        overrides: [
          // Override group chat providers
          groupChatDetailsProvider(sampleGroupId)
              .overrideWith((ref) => Stream.value(sampleGroupChat)),

          groupChatMessagesProvider(sampleGroupId)
              .overrideWith((ref) => Stream.value(messages)),

          groupChatMembersProvider(sampleGroupId).overrideWith(
              (ref) => Future.value(sampleGroupMembers.values.toList())),

          // Override group translation providers
          groupTranslationSettingsProvider(sampleGroupId)
              .overrideWith((ref) => Future.value(sampleGroupSettings)),
        ],
      );

      // Build the app with the overridden providers
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            home: GroupChatScreen(groupId: sampleGroupId),
          ),
        ),
      );

      // Wait for the screen to load
      await tester.pumpAndSettle();

      // Measure scrolling performance
      final stopwatch = Stopwatch()..start();

      // Scroll down
      await tester.fling(
        find.byType(ListView),
        const Offset(0, -500),
        3000,
      );
      await tester.pumpAndSettle();

      // Scroll up
      await tester.fling(
        find.byType(ListView),
        const Offset(0, 500),
        3000,
      );
      await tester.pumpAndSettle();

      stopwatch.stop();

      // Print the time it took to scroll
      print(
          'Time to scroll through 500 messages: ${stopwatch.elapsedMilliseconds}ms');

      // Verify some messages are displayed
      expect(find.textContaining('This is test message'), findsWidgets);
    });

    testWidgets('Measure translation performance for 10 messages',
        (WidgetTester tester) async {
      // Generate 10 messages with non-English languages
      final messages = <MessageModel>[];
      final languages = ['fr', 'es', 'yo', 'ig', 'ha', 'sw'];

      for (int i = 0; i < 10; i++) {
        final language = languages[i % languages.length];

        messages.add(MessageModel(
          id: 'message-$i',
          chatId: sampleGroupId,
          senderId: 'user-789',
          recipientId: '',
          text: 'This is test message number $i in $language',
          timestamp: DateTime.now().subtract(Duration(minutes: 10 - i)),
          status: MessageStatus.sent,
          type: MessageType.text,
          originalLanguage: language,
        ));
      }

      // Create a ProviderContainer with overrides for testing
      final container = ProviderContainer(
        overrides: [
          // Override group chat providers
          groupChatDetailsProvider(sampleGroupId)
              .overrideWith((ref) => Stream.value(sampleGroupChat)),

          groupChatMessagesProvider(sampleGroupId)
              .overrideWith((ref) => Stream.value(messages)),

          groupChatMembersProvider(sampleGroupId).overrideWith(
              (ref) => Future.value(sampleGroupMembers.values.toList())),

          // Override group translation providers
          groupTranslationSettingsProvider(sampleGroupId)
              .overrideWith((ref) => Future.value(sampleGroupSettings)),

          // Use real translation logic for performance testing
          // Other providers are overridden in the previous tests
        ],
      );

      // Measure the time it takes to translate all messages
      final stopwatch = Stopwatch()..start();

      // Build the app with the overridden providers
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            home: GroupChatScreen(groupId: sampleGroupId),
          ),
        ),
      );

      // Wait for the screen to load and translations to complete
      await tester.pumpAndSettle();

      stopwatch.stop();

      // Print the time it took to translate
      print(
          'Time to translate 10 messages: ${stopwatch.elapsedMilliseconds}ms');
      print(
          'Average time per message: ${stopwatch.elapsedMilliseconds / 10}ms');

      // Verify translations are displayed
      expect(find.textContaining('Translated from'), findsWidgets);
    });
  });
}
