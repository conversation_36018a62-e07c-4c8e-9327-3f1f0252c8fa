import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/models/landmark.dart';
import 'package:culture_connect/models/ar_model.dart';
import 'package:culture_connect/services/auth_service.dart';
import 'package:culture_connect/providers/auth_provider.dart';

/// A utility class with helper methods for testing.
class TestHelpers {
  /// Wraps a widget with necessary providers for testing.
  ///
  /// This method wraps the given [child] with a [ProviderScope] and [MaterialApp]
  /// to make it easier to test widgets that depend on providers and material widgets.
  ///
  /// [overrides] is a list of provider overrides to apply.
  ///
  /// Example:
  /// ```dart
  /// await tester.pumpWidget(
  ///   TestHelpers.testableWidget(
  ///     child: MyWidget(),
  ///     overrides: [
  ///       authServiceProvider.overrideWithValue(mockAuthService),
  ///     ],
  ///   ),
  /// );
  /// ```
  static Widget testableWidget({
    required Widget child,
    List<Override> overrides = const [],
  }) {
    return ProviderScope(
      overrides: overrides,
      child: MaterialApp(
        home: child,
      ),
    );
  }

  /// Creates a mock authenticated user for testing.
  ///
  /// This method creates a [UserModel] with the given parameters
  /// for use in tests that require an authenticated user.
  ///
  /// Example:
  /// ```dart
  /// final user = TestHelpers.mockAuthenticatedUser(
  ///   uid: 'test-uid',
  ///   displayName: 'Test User',
  /// );
  /// ```
  static UserModel mockAuthenticatedUser({
    String id = 'test-uid',
    String email = '<EMAIL>',
    String firstName = 'Test',
    String lastName = 'User',
    String phoneNumber = '+**********',
    bool isVerified = true,
  }) {
    return UserModel(
      id: id,
      firstName: firstName,
      lastName: lastName,
      email: email,
      phoneNumber: phoneNumber,
      userType: 'tourist',
      isVerified: isVerified,
      verificationLevel: 1,
      status: 'active',
      createdAt: DateTime.now().toIso8601String(),
      updatedAt: DateTime.now().toIso8601String(),
      lastLogin: DateTime.now().toIso8601String(),
      emailVerified: true,
    );
  }

  /// Creates a mock landmark for testing.
  ///
  /// This method creates a [Landmark] with the given parameters
  /// for use in tests that require landmark data.
  ///
  /// Example:
  /// ```dart
  /// final landmark = TestHelpers.mockLandmark(
  ///   id: 'eiffel-tower',
  ///   name: 'Eiffel Tower',
  /// );
  /// ```
  static Landmark mockLandmark({
    String id = 'landmark-1',
    String name = 'Eiffel Tower',
    String description = 'Famous landmark in Paris',
    double latitude = 48.8584,
    double longitude = 2.2945,
    bool hasArContent = true,
    String? arContentId = 'ar-eiffel-tower',
    String? imageUrl = 'https://example.com/eiffel.jpg',
    String? historicalSignificance = 'Built for the 1889 World Fair',
    String? culturalContext = 'Symbol of Paris and France',
  }) {
    return Landmark(
      id: id,
      name: name,
      description: description,
      latitude: latitude,
      longitude: longitude,
      hasArContent: hasArContent,
      arContentId: arContentId,
      imageUrl: imageUrl,
      historicalSignificance: historicalSignificance,
      culturalContext: culturalContext,
    );
  }

  /// Creates a mock AR model for testing.
  ///
  /// This method creates an [ARModel] with the given parameters
  /// for use in tests that require AR model data.
  ///
  /// Example:
  /// ```dart
  /// final arModel = TestHelpers.mockARModel(
  ///   id: 'eiffel-tower-model',
  ///   name: 'Eiffel Tower 3D Model',
  /// );
  /// ```
  static ARModel mockARModel({
    String id = 'model-1',
    String name = 'Eiffel Tower Model',
    String modelUrl = 'https://example.com/models/eiffel.glb',
    String? textureUrl = 'https://example.com/textures/eiffel.jpg',
    double scale = 1.0,
    List<double> rotation = const [0.0, 0.0, 0.0],
    List<double> position = const [0.0, 0.0, 0.0],
    int fileSize = 1048576, // 1MB
  }) {
    return ARModel(
      id: id,
      name: name,
      modelUrl: modelUrl,
      textureUrl: textureUrl,
      scale: scale,
      rotation: rotation,
      position: position,
      fileSize: fileSize,
    );
  }

  /// Sets up authentication for testing.
  ///
  /// This method configures the [mockAuthService] to return the given [user]
  /// when authentication methods are called.
  ///
  /// Example:
  /// ```dart
  /// TestHelpers.setupAuthentication(
  ///   mockAuthService: mockAuthService,
  ///   user: TestHelpers.mockAuthenticatedUser(),
  /// );
  /// ```
  static void setupAuthentication({
    required MockAuthService mockAuthService,
    required UserModel user,
  }) {
    when(mockAuthService.getCurrentUser()).thenAnswer((_) async => user);
    when(mockAuthService.signInWithEmailAndPassword(
      email: anyNamed('email'),
      password: anyNamed('password'),
    )).thenAnswer((_) async => user);
    when(mockAuthService.registerWithEmailAndPassword(
      email: anyNamed('email'),
      password: anyNamed('password'),
      fullName: anyNamed('fullName'),
    )).thenAnswer((_) async => user);
    when(mockAuthService.signInWithGoogle()).thenAnswer((_) async => user);
  }

  /// Waits for a specific condition to be true.
  ///
  /// This method repeatedly checks the [condition] until it returns true
  /// or the [timeout] is reached.
  ///
  /// Example:
  /// ```dart
  /// await TestHelpers.waitFor(() => myProvider.state.isLoaded);
  /// ```
  static Future<void> waitFor(
    bool Function() condition, {
    Duration timeout = const Duration(seconds: 5),
    Duration interval = const Duration(milliseconds: 50),
  }) async {
    final stopwatch = Stopwatch()..start();
    while (!condition() && stopwatch.elapsed < timeout) {
      await Future.delayed(interval);
    }
    stopwatch.stop();
    if (!condition()) {
      throw TimeoutException('Condition not satisfied within $timeout');
    }
  }

  /// Pumps the widget tree until there are no more frames pending.
  ///
  /// This method repeatedly pumps the widget tree until there are no more
  /// frames pending or the [timeout] is reached.
  ///
  /// Example:
  /// ```dart
  /// await TestHelpers.pumpUntilFound(tester, find.text('Loaded'));
  /// ```
  static Future<void> pumpUntilFound(
    WidgetTester tester,
    Finder finder, {
    Duration timeout = const Duration(seconds: 5),
  }) async {
    final stopwatch = Stopwatch()..start();
    while (!finder.evaluate().isNotEmpty && stopwatch.elapsed < timeout) {
      await tester.pump(const Duration(milliseconds: 50));
    }
    stopwatch.stop();
    expect(finder, findsOneWidget, reason: 'Widget not found within $timeout');
  }

  /// Simulates a delay for testing asynchronous behavior.
  ///
  /// This method creates a delay of the specified [duration] and then
  /// returns the given [value].
  ///
  /// Example:
  /// ```dart
  /// when(mockRepository.fetchData()).thenAnswer(
  ///   (_) => TestHelpers.delayed(
  ///     const Duration(seconds: 1),
  ///     ['item1', 'item2'],
  ///   ),
  /// );
  /// ```
  static Future<T> delayed<T>(Duration duration, T value) async {
    await Future.delayed(duration);
    return value;
  }

  /// Creates a stream that emits the given values with delays.
  ///
  /// This method creates a stream that emits each value in [values]
  /// with a delay of [interval] between emissions.
  ///
  /// Example:
  /// ```dart
  /// when(mockRepository.getDataStream()).thenAnswer(
  ///   (_) => TestHelpers.delayedStream(
  ///     [1, 2, 3],
  ///     interval: const Duration(milliseconds: 100),
  ///   ),
  /// );
  /// ```
  static Stream<T> delayedStream<T>(
    List<T> values, {
    Duration interval = const Duration(milliseconds: 100),
  }) async* {
    for (final value in values) {
      await Future.delayed(interval);
      yield value;
    }
  }

  /// Mocks a successful HTTP response for testing API calls.
  ///
  /// This method creates a mock HTTP response with the given [body],
  /// [statusCode], and [headers].
  ///
  /// Example:
  /// ```dart
  /// when(mockClient.get(any)).thenAnswer(
  ///   (_) async => TestHelpers.mockHttpResponse(
  ///     body: '{"id": 1, "name": "Test"}',
  ///     statusCode: 200,
  ///   ),
  /// );
  /// ```
  static dynamic mockHttpResponse({
    required String body,
    int statusCode = 200,
    Map<String, String> headers = const {'content-type': 'application/json'},
  }) {
    // This is a generic implementation that can be adapted based on the HTTP client used
    // For http package, return http.Response
    // For dio package, return Response
    // The actual implementation depends on the HTTP client used in the project
    return body;
  }
}

/// A mock navigator observer for testing navigation.
class MockNavigatorObserver extends Mock implements NavigatorObserver {
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {}
  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {}
  @override
  void didRemove(Route<dynamic> route, Route<dynamic>? previousRoute) {}
  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {}
}
