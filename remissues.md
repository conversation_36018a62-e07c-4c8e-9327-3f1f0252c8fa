flutter run --debug

The default interactive shell is now zsh.
To update your account to use zsh, please run `chsh -s /bin/zsh`.
For more details, please visit https://support.apple.com/kb/HT208050.
(base) Macs-MacBook-Pro:culture_connect mac$ flutter run --debug
Launching lib/main.dart on iPhone 15 Pro Max in debug mode...
flutter: ⚠️ WARNING [2025-06-30T17:19:02.659313] [PerformanceMonitoringService] High memory usage detected {"memory_mb":159.0}
flutter: 🐛 DEBUG [2025-06-30T17:19:03.640291] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:19:17.123382] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-30T17:19:17.660889] [PerformanceMonitoringService] High memory usage detected {"memory_mb":160.0}
flutter: 🐛 DEBUG [2025-06-30T17:19:24.390278] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-30T17:19:32.660074] [PerformanceMonitoringService] High memory usage detected {"memory_mb":159.0}
flutter: 🐛 DEBUG [2025-06-30T17:19:39.473810] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:19:44.674669] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: ⚠️ WARNING [2025-06-30T17:19:47.663141] [PerformanceMonitoringService] High memory usage detected {"memory_mb":163.0}
flutter: ⚠️ WARNING [2025-06-30T17:20:02.660542] [PerformanceMonitoringService] High memory usage detected {"memory_mb":160.0}
flutter: ⚠️ WARNING [2025-06-30T17:20:17.666092] [PerformanceMonitoringService] High memory usage detected {"memory_mb":165.0}
flutter: ⚠️ WARNING [2025-06-30T17:20:32.661450] [PerformanceMonitoringService] High memory usage detected {"memory_mb":161.0}
flutter: 🐛 DEBUG [2025-06-30T17:20:32.742105] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-30T17:20:32.869725] [PerformanceMonitoringService] Slow frame detected {"duration_ms":77}
flutter: 🐛 DEBUG [2025-06-30T17:20:32.892811] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: ⚠️ WARNING [2025-06-30T17:20:47.661357] [PerformanceMonitoringService] High memory usage detected {"memory_mb":161.0}
flutter: ⚠️ WARNING [2025-06-30T17:21:02.662587] [PerformanceMonitoringService] High memory usage detected {"memory_mb":162.0}
flutter: ⚠️ WARNING [2025-06-30T17:21:17.689071] [PerformanceMonitoringService] High memory usage detected {"memory_mb":188.0}
flutter: 🐛 DEBUG [2025-06-30T17:21:28.655972] [PerformanceMonitoringService] Slow frame detected {"duration_ms":45}
flutter: 🐛 DEBUG [2025-06-30T17:21:28.677976] [PerformanceMonitoringService] Slow frame detected {"duration_ms":20}
flutter: ⚠️ WARNING [2025-06-30T17:21:32.663320] [PerformanceMonitoringService] High memory usage detected {"memory_mb":163.0}
flutter: ⚠️ WARNING [2025-06-30T17:21:47.663416] [PerformanceMonitoringService] High memory usage detected {"memory_mb":163.0}
flutter: 🐛 DEBUG [2025-06-30T17:21:55.978826] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-30T17:22:02.664261] [PerformanceMonitoringService] High memory usage detected {"memory_mb":164.0}
flutter: 🐛 DEBUG [2025-06-30T17:22:11.987680] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:22:12.065827] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-06-30T17:22:12.211595] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:22:12.644957] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-30T17:22:17.669344] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: 🐛 DEBUG [2025-06-30T17:22:25.262049] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:22:25.695442] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:22:25.978685] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:22:27.228702] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:22:27.508479] [PerformanceMonitoringService] Slow frame detected {"duration_ms":263}
flutter: 🐛 DEBUG [2025-06-30T17:22:27.594569] [PerformanceMonitoringService] Slow frame detected {"duration_ms":86}
flutter: 🐛 DEBUG [2025-06-30T17:22:27.683979] [PerformanceMonitoringService] Slow frame detected {"duration_ms":89}
flutter: 🐛 DEBUG [2025-06-30T17:22:27.712074] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-06-30T17:22:27.762424] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:22:27.847319] [PerformanceMonitoringService] Slow frame detected {"duration_ms":35}
flutter: 🐛 DEBUG [2025-06-30T17:22:28.082045] [PerformanceMonitoringService] Slow frame detected {"duration_ms":86}
flutter: 🐛 DEBUG [2025-06-30T17:22:28.528937] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-30T17:22:28.745560] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:22:28.779088] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:22:29.062623] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-06-30T17:22:29.114139] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:22:29.212623] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-06-30T17:22:29.277078] [PerformanceMonitoringService] Slow frame detected {"duration_ms":65}
flutter: 🐛 DEBUG [2025-06-30T17:22:29.295461] [PerformanceMonitoringService] Slow frame detected {"duration_ms":18}
flutter: 🐛 DEBUG [2025-06-30T17:22:29.345629] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-30T17:22:32.665450] [PerformanceMonitoringService] High memory usage detected {"memory_mb":165.0}
flutter: 🐛 DEBUG [2025-06-30T17:22:41.863446] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-30T17:22:47.666400] [PerformanceMonitoringService] High memory usage detected {"memory_mb":166.0}
flutter: 🐛 DEBUG [2025-06-30T17:22:49.396177] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:22:52.929599] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:22:54.529629] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-30T17:23:02.673778] [PerformanceMonitoringService] High memory usage detected {"memory_mb":173.0}
flutter: 🐛 DEBUG [2025-06-30T17:23:05.063246] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:23:05.313358] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:23:05.363861] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:23:16.246882] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:23:16.280831] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:23:16.313595] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-30T17:23:17.666976] [PerformanceMonitoringService] High memory usage detected {"memory_mb":166.0}
flutter: 🐛 DEBUG [2025-06-30T17:23:18.580358] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:23:19.747005] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:23:20.080465] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: ⚠️ WARNING [2025-06-30T17:23:32.667145] [PerformanceMonitoringService] High memory usage detected {"memory_mb":167.0}
flutter: 🐛 DEBUG [2025-06-30T17:23:42.347850] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:23:46.964544] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: ⚠️ WARNING [2025-06-30T17:23:47.666933] [PerformanceMonitoringService] High memory usage detected {"memory_mb":166.0}
flutter: ⚠️ WARNING [2025-06-30T17:24:02.667987] [PerformanceMonitoringService] High memory usage detected {"memory_mb":167.0}
flutter: ⚠️ WARNING [2025-06-30T17:24:17.668555] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: ⚠️ WARNING [2025-06-30T17:24:32.669679] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
Running Xcode build...                                                  
flutter: 🐛 DEBUG [2025-06-30T17:24:46.517304] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-30T17:24:47.669367] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: ⚠️ WARNING [2025-06-30T17:25:02.669885] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: 🐛 DEBUG [2025-06-30T17:25:09.483677] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:25:09.567222] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-06-30T17:25:09.658251] [PerformanceMonitoringService] Slow frame detected {"duration_ms":91}
flutter: 🐛 DEBUG [2025-06-30T17:25:10.000455] [PerformanceMonitoringService] Slow frame detected {"duration_ms":341}
flutter: 🐛 DEBUG [2025-06-30T17:25:10.104801] [PerformanceMonitoringService] Slow frame detected {"duration_ms":103}
flutter: 🐛 DEBUG [2025-06-30T17:25:10.324609] [PerformanceMonitoringService] Slow frame detected {"duration_ms":174}
flutter: 🐛 DEBUG [2025-06-30T17:25:10.350507] [PerformanceMonitoringService] Slow frame detected {"duration_ms":25}
flutter: 🐛 DEBUG [2025-06-30T17:25:10.486804] [PerformanceMonitoringService] Slow frame detected {"duration_ms":85}
flutter: 🐛 DEBUG [2025-06-30T17:25:10.527933] [PerformanceMonitoringService] Slow frame detected {"duration_ms":41}
flutter: 🐛 DEBUG [2025-06-30T17:25:10.570389] [PerformanceMonitoringService] Slow frame detected {"duration_ms":42}
flutter: 🐛 DEBUG [2025-06-30T17:25:10.641335] [PerformanceMonitoringService] Slow frame detected {"duration_ms":70}
flutter: 🐛 DEBUG [2025-06-30T17:25:10.704664] [PerformanceMonitoringService] Slow frame detected {"duration_ms":64}
flutter: 🐛 DEBUG [2025-06-30T17:25:10.803134] [PerformanceMonitoringService] Slow frame detected {"duration_ms":97}
flutter: 🐛 DEBUG [2025-06-30T17:25:11.054341] [PerformanceMonitoringService] Slow frame detected {"duration_ms":237}
flutter: 🐛 DEBUG [2025-06-30T17:25:11.537521] [PerformanceMonitoringService] Slow frame detected {"duration_ms":37}
flutter: ⚠️ WARNING [2025-06-30T17:25:12.746570] [PerformanceMonitoringService] High memory usage detected {"memory_mb":196.0}
flutter: 🐛 DEBUG [2025-06-30T17:25:12.750565] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-06-30T17:25:15.767430] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: 🐛 DEBUG [2025-06-30T17:25:16.189925] [PerformanceMonitoringService] Slow frame detected {"duration_ms":39}
flutter: 🐛 DEBUG [2025-06-30T17:25:16.217569] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-06-30T17:25:16.267105] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:25:16.317369] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-30T17:25:17.671246] [PerformanceMonitoringService] High memory usage detected {"memory_mb":170.0}
flutter: 🐛 DEBUG [2025-06-30T17:25:30.267887] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-30T17:25:32.670748] [PerformanceMonitoringService] High memory usage detected {"memory_mb":170.0}
 └─Compiling, linking and signing...                        56.7s
Xcode build done.                                           268.8s
flutter: ⚠️ WARNING [2025-06-30T17:25:47.671270] [PerformanceMonitoringService] High memory usage detected {"memory_mb":171.0}
no valid “aps-environment” entitlement string found for application
flutter: 🔥 Waiting for Firebase initialization...
flutter: 🚀 Starting app initialization
flutter: ✅ SharedPreferences initialized in 50ms
flutter: ❌ Error preloading asset assets/images/splash.png: Unable to load asset: "assets/images/splash.png".
flutter: The asset does not exist or has empty data.
flutter: ✅ Preloaded asset: assets/animations/splash_animation.json (3741 bytes)
flutter: ✅ Critical assets preloaded in 103ms
flutter: ✅ Firebase core initialized in 351ms
flutter: ✅ App initialization completed in 382ms
flutter: ✅ Firebase initialization completed successfully
flutter: ✅ Firebase full features initialized in 379ms
flutter: ✅ Preloaded asset: assets/images/onboarding_1.png (0 bytes)
flutter: ✅ Preloaded asset: assets/images/onboarding_2.png (0 bytes)
flutter: ✅ Preloaded asset: assets/images/onboarding_3.png (0 bytes)
flutter: ✅ Non-critical assets preloaded in 482ms
flutter: ℹ️ INFO [2025-06-30T17:26:11.453480] [LoggingService] Logging service initialized successfully
flutter: ℹ️ INFO [2025-06-30T17:26:11.468804] [LoggingService] Device info: {name: iPhone 15 Pro Max, model: iPhone, systemName: iOS, systemVersion: 17.2, platform: ios}
flutter: ℹ️ INFO [2025-06-30T17:26:11.469960] [LoggingService] App info: Culture Connect 1.0.0+1
flutter: ℹ️ INFO [2025-06-30T17:26:11.588149] [ErrorHandlingService] Error handling service initialized
flutter: ℹ️ INFO [2025-06-30T17:26:11.656620] [CrashReportingService] Crash reporting service initialized
flutter: ℹ️ INFO [2025-06-30T17:26:11.731336] [AnalyticsService] Analytics service initialized
flutter: 🐛 DEBUG [2025-06-30T17:26:11.749826] [AnalyticsService] Event: app_session_begin {"category":"engagement","parameters":{"timestamp":"2025-06-30T17:26:11.677864"}}
flutter: ℹ️ INFO [2025-06-30T17:26:11.784383] [PerformanceMonitoringService] Performance monitoring service initialized
flutter: ℹ️ INFO [2025-06-30T17:26:11.785065] [App] All services initialized successfully
flutter: 🐛 DEBUG [2025-06-30T17:26:15.936314] [PerformanceMonitoringService] Slow frame detected {"duration_ms":251}
flutter: 🐛 DEBUG [2025-06-30T17:26:15.974729] [PerformanceMonitoringService] Slow frame detected {"duration_ms":32}
flutter: 🐛 DEBUG [2025-06-30T17:26:16.002794] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
Syncing files to device iPhone 15 Pro Max...                     2,828ms
flutter: 🐛 DEBUG [2025-06-30T17:26:16.036112] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}

Flutter run key commands.
r Hot reload. 🔥🔥🔥
R Hot restart.
h List all available interactive commands.
d Detach (terminate "flutter run" but leave application running).
c Clear the screen
q Quit (terminate the application on the device).

A Dart VM Service on iPhone 15 Pro Max is available at: http://127.0.0.1:62879/FXjlpVJ5sFk=/
The Flutter DevTools debugger and profiler on iPhone 15 Pro Max is available at:
http://127.0.0.1:9101?uri=http://127.0.0.1:62879/FXjlpVJ5sFk=/
flutter: ⚠️ WARNING [2025-06-30T17:26:16.780805] [PerformanceMonitoringService] High memory usage detected {"memory_mb":180.0}
flutter: 🐛 DEBUG [2025-06-30T17:26:24.453892] [PerformanceMonitoringService] Slow frame detected {"duration_ms":299}
flutter: 🐛 DEBUG [2025-06-30T17:26:25.462294] [PerformanceMonitoringService] Slow frame detected {"duration_ms":1026}
flutter: 🐛 DEBUG [2025-06-30T17:26:25.536301] [PerformanceMonitoringService] Slow frame detected {"duration_ms":73}
flutter: 🐛 DEBUG [2025-06-30T17:26:31.453232] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:31.486040] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:31.853578] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:32.174664] [PerformanceMonitoringService] Slow frame detected {"duration_ms":205}
flutter: 🐛 DEBUG [2025-06-30T17:26:32.286827] [PerformanceMonitoringService] Slow frame detected {"duration_ms":111}
flutter: 🐛 DEBUG [2025-06-30T17:26:32.320389] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:32.376461] [PerformanceMonitoringService] Slow frame detected {"duration_ms":57}
flutter: 🐛 DEBUG [2025-06-30T17:26:32.403094] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-06-30T17:26:32.453313] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:32.548637] [PerformanceMonitoringService] Slow frame detected {"duration_ms":95}
flutter: 🐛 DEBUG [2025-06-30T17:26:32.603123] [PerformanceMonitoringService] Slow frame detected {"duration_ms":54}
flutter: 🐛 DEBUG [2025-06-30T17:26:32.652896] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:32.687042] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:33.419775] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:33.603165] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:33.736631] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:34.719978] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:34.836508] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:34.986592] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:37.687054] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-06-30T17:26:37.753990] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-30T17:26:38.002922] [PerformanceMonitoringService] Slow frame detected {"duration_ms":233}
flutter: 🐛 DEBUG [2025-06-30T17:26:38.028905] [PerformanceMonitoringService] Slow frame detected {"duration_ms":42}
flutter: 🐛 DEBUG [2025-06-30T17:26:38.053447] [PerformanceMonitoringService] Slow frame detected {"duration_ms":23}
flutter: 🐛 DEBUG [2025-06-30T17:26:43.537409] [PerformanceMonitoringService] Slow frame detected {"duration_ms":450}
flutter: 🐛 DEBUG [2025-06-30T17:26:43.670220] [PerformanceMonitoringService] Slow frame detected {"duration_ms":133}
flutter: 🐛 DEBUG [2025-06-30T17:26:45.293555] [PerformanceMonitoringService] Slow frame detected {"duration_ms":40}
flutter: 🐛 DEBUG [2025-06-30T17:26:45.403490] [PerformanceMonitoringService] Slow frame detected {"duration_ms":109}
flutter: 🐛 DEBUG [2025-06-30T17:26:45.503360] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-06-30T17:26:45.586601] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:46.269951] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-30T17:26:46.782423] [PerformanceMonitoringService] High memory usage detected {"memory_mb":181.0}
flutter: 🐛 DEBUG [2025-06-30T17:26:48.095496] [PerformanceMonitoringService] Slow frame detected {"duration_ms":42}
flutter: 🐛 DEBUG [2025-06-30T17:26:48.120631] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-06-30T17:26:48.286864] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-30T17:26:48.536908] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:48.703611] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:48.836921] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:49.003614] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:49.137554] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:49.320354] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:50.808372] [PerformanceMonitoringService] Slow frame detected {"duration_ms":38}
flutter: 🐛 DEBUG [2025-06-30T17:26:50.853344] [PerformanceMonitoringService] Slow frame detected {"duration_ms":45}
flutter: 🐛 DEBUG [2025-06-30T17:26:52.303785] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:52.520435] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:52.703368] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:53.137102] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:55.036886] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:26:57.570999] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-30T17:27:01.782091] [PerformanceMonitoringService] High memory usage detected {"memory_mb":181.0}
flutter: 🐛 DEBUG [2025-06-30T17:27:03.853600] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:27:09.470799] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-30T17:27:09.527556] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-06-30T17:27:09.554262] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-06-30T17:27:12.888270] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-30T17:27:14.588431] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-30T17:27:16.782137] [PerformanceMonitoringService] High memory usage detected {"memory_mb":181.0}
flutter: 🐛 DEBUG [2025-06-30T17:27:25.588157] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:27:25.704332] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:27:27.404862] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:27:31.188260] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: ⚠️ WARNING [2025-06-30T17:27:31.782607] [PerformanceMonitoringService] High memory usage detected {"memory_mb":182.0}
flutter: 🐛 DEBUG [2025-06-30T17:27:38.688179] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:27:38.755205] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-30T17:27:38.955230] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:27:41.455152] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:27:43.054970] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:27:46.338325] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-30T17:27:46.783324] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: 🐛 DEBUG [2025-06-30T17:27:47.005323] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:27:57.089075] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:27:57.155729] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-30T17:27:57.322703] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-30T17:28:00.322003] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-30T17:28:01.784398] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: 🐛 DEBUG [2025-06-30T17:28:02.689042] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: Firebase Auth Exception: email-already-in-use - The email address is already in use by another account.
flutter: 🐛 DEBUG [2025-06-30T17:28:03.572524] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: ❌ ERROR [2025-06-30T17:28:03.611709] [FlutterError] A RenderFlex overflowed by 196 pixels on the bottom. A RenderFlex overflowed by 196 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #16     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #20     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #21     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #22     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #23     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #24     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #25     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #26     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #27     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #28     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #32     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #37     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #38     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #39     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #40     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #41     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #42     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #43     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #44     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #45     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #46     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #47     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #48     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #49     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #50     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #51     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #52     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #53     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #54     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #55     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #56     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #57     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #58     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #59     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #60     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #61     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #62     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #63     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #64     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #65     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #66     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #67     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #71     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #72     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #73     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #74     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #75     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #76     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #77     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #78     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #79     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #80     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #90     _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #91     _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #92     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #93     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #94     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #95     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #96     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #97     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #102    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #103    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #104    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #105    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #106    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #107    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #108    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #109    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #110    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #111    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #112    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #113    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #114    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #115    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #116    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #117    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #118    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #119    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #120    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #121    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #122    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #123    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #124    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #125    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #126    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #127    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #128    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #129    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #130    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #131    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #132    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #133    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #134    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #135    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #136    _invoke (dart:ui/hooks.dart:312:13)
flutter: #137    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #138    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-06-30T17:28:03.682121] [PerformanceMonitoringService] Slow frame detected {"duration_ms":110}
flutter: 🐛 DEBUG [2025-06-30T17:28:03.705896] [PerformanceMonitoringService] Slow frame detected {"duration_ms":23}
flutter: 🔍 Starting email verification check...
flutter: ❌ No user in currentUserProvider - checking Firebase directly...
flutter: ❌ No user in Firebase Auth either - session lost
flutter: ✅ Firebase app is initialized: [DEFAULT]
flutter: 🐛 DEBUG [2025-06-30T17:28:04.072246] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🔍 Starting email verification check...
flutter: ❌ No user in currentUserProvider - checking Firebase directly...
flutter: ❌ No user in Firebase Auth either - session lost
flutter: ✅ Firebase app is initialized: [DEFAULT]
flutter: 🔍 Starting email verification check...
flutter: ❌ No user in currentUserProvider - checking Firebase directly...
flutter: ❌ No user in Firebase Auth either - session lost
flutter: ✅ Firebase app is initialized: [DEFAULT]
flutter: 🔍 Starting email verification check...
flutter: ❌ No user in currentUserProvider - checking Firebase directly...
flutter: ❌ No user in Firebase Auth either - session lost
flutter: ✅ Firebase app is initialized: [DEFAULT]
flutter: 🔍 Starting email verification check...
flutter: ❌ No user in currentUserProvider - checking Firebase directly...
flutter: ❌ No user in Firebase Auth either - session lost
flutter: ✅ Firebase app is initialized: [DEFAULT]
flutter: 🔍 Starting email verification check...
flutter: ❌ No user in currentUserProvider - checking Firebase directly...
flutter: ❌ No user in Firebase Auth either - session lost
flutter: ✅ Firebase app is initialized: [DEFAULT]
flutter: 🔍 Starting email verification check...
flutter: ❌ No user in currentUserProvider - checking Firebase directly...
flutter: ❌ No user in Firebase Auth either - session lost
flutter: ✅ Firebase app is initialized: [DEFAULT]
flutter: ⚠️ WARNING [2025-06-30T17:28:16.783843] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: 🔍 Starting email verification check...
flutter: ❌ No user in currentUserProvider - checking Firebase directly...
flutter: ❌ No user in Firebase Auth either - session lost
flutter: ✅ Firebase app is initialized: [DEFAULT]
flutter: 🔍 Starting email verification check...
flutter: ❌ No user in currentUserProvider - checking Firebase directly...
flutter: ❌ No user in Firebase Auth either - session lost
flutter: ✅ Firebase app is initialized: [DEFAULT]
flutter: 🔍 Starting email verification check...
flutter: ❌ No user in currentUserProvider - checking Firebase directly...
flutter: ❌ No user in Firebase Auth either - session lost
flutter: ✅ Firebase app is initialized: [DEFAULT]
flutter: 🔍 Starting email verification check...
flutter: ❌ No user in currentUserProvider - checking Firebase directly...
flutter: ❌ No user in Firebase Auth either - session lost
flutter: ✅ Firebase app is initialized: [DEFAULT]
flutter: 🔍 Starting email verification check...
flutter: ❌ No user in currentUserProvider - checking Firebase directly...
flutter: ❌ No user in Firebase Auth either - session lost
flutter: ✅ Firebase app is initialized: [DEFAULT]
flutter: 🔍 Starting email verification check...
flutter: ❌ No user in currentUserProvider - checking Firebase directly...
flutter: ❌ No user in Firebase Auth either - session lost
flutter: ✅ Firebase app is initialized: [DEFAULT]
flutter: 🔍 Starting email verification check...
flutter: ❌ No user in currentUserProvider - checking Firebase directly...
flutter: ❌ No user in Firebase Auth either - session lost
flutter: ✅ Firebase app is initialized: [DEFAULT]
flutter: ⚠️ WARNING [2025-06-30T17:28:31.785053] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: 🔍 Starting email verification check...
flutter: ❌ No user in currentUserProvider - checking Firebase directly...
flutter: ❌ No user in Firebase Auth either - session lost
flutter: ✅ Firebase app is initialized: [DEFAULT]
