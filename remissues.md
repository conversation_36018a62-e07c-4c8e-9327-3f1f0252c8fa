(base) Macs-MacBook-Pro:culture_connect mac$ flutter run -d 9DB20354-1C92-4494-AC81-A517DB5A8DEF
Launching lib/main.dart on iPhone15ProMax_iOS17 in debug mode...
Running Xcode build...                                                  
 └─Compiling, linking and signing...                        43.3s
Xcode build done.                                           200.1s
no valid “aps-environment” entitlement string found for application
flutter: 🚀 Starting app initialization
flutter: ✅ SharedPreferences initialized in 79ms
flutter: ❌ Error preloading asset assets/images/splash.png: Unable to load asset: "assets/images/splash.png".
flutter: The asset does not exist or has empty data.
flutter: ✅ Preloaded asset: assets/animations/splash_animation.json (3741 bytes)
flutter: ✅ Critical assets preloaded in 166ms
flutter: ✅ Firebase core initialized in 419ms
flutter: ✅ App initialization completed in 448ms
flutter: ℹ️ INFO [2025-06-25T03:27:55.493067] [LoggingService] Logging service initialized successfully
flutter: ℹ️ INFO [2025-06-25T03:27:55.504372] [LoggingService] Device info: {name: iPhone15ProMax_iOS17, model: iPhone, systemName: iOS, systemVersion: 17.2, platform: ios}
flutter: ℹ️ INFO [2025-06-25T03:27:55.505849] [LoggingService] App info: Culture Connect 1.0.0+1
flutter: ✅ Preloaded asset: assets/images/onboarding_1.png (0 bytes)
flutter: ✅ Firebase full features initialized in 175ms
flutter: ℹ️ INFO [2025-06-25T03:27:55.650128] [ErrorHandlingService] Error handling service initialized
flutter: ✅ Preloaded asset: assets/images/onboarding_2.png (0 bytes)
flutter: ✅ Preloaded asset: assets/images/onboarding_3.png (0 bytes)
flutter: ✅ Non-critical assets preloaded in 188ms
flutter: ℹ️ INFO [2025-06-25T03:27:55.718697] [CrashReportingService] Crash reporting service initialized
flutter: ℹ️ INFO [2025-06-25T03:27:55.763563] [AnalyticsService] Analytics service initialized
flutter: 🐛 DEBUG [2025-06-25T03:27:55.780305] [AnalyticsService] Event: app_session_begin {"category":"engagement","parameters":{"timestamp":"2025-06-25T03:27:55.741384"}}
flutter: ℹ️ INFO [2025-06-25T03:27:55.791187] [PerformanceMonitoringService] Performance monitoring service initialized
flutter: ℹ️ INFO [2025-06-25T03:27:55.791889] [App] All services initialized successfully
flutter: ⚠️ WARNING [2025-06-25T03:27:55.808968] [PerformanceMonitoringService] High memory usage detected {"memory_mb":189.0}
Syncing files to device iPhone15ProMax_iOS17...                  2,547ms

Flutter run key commands.
r Hot reload. 🔥🔥🔥
R Hot restart.
h List all available interactive commands.
d Detach (terminate "flutter run" but leave application running).
c Clear the screen
q Quit (terminate the application on the device).

A Dart VM Service on iPhone15ProMax_iOS17 is available at: http://127.0.0.1:56463/KvXsB-mMOE0=/
The Flutter DevTools debugger and profiler on iPhone15ProMax_iOS17 is available at:
http://127.0.0.1:9101?uri=http://127.0.0.1:56463/KvXsB-mMOE0=/
flutter: 🐛 DEBUG [2025-06-25T03:28:00.729039] [PerformanceMonitoringService] Slow frame detected {"duration_ms":264}
flutter: 🐛 DEBUG [2025-06-25T03:28:00.783499] [PerformanceMonitoringService] Slow frame detected {"duration_ms":99}
flutter: ⚠️ WARNING [2025-06-25T03:28:00.900994] [PerformanceMonitoringService] High memory usage detected {"memory_mb":193.0}
flutter: 🐛 DEBUG [2025-06-25T03:28:00.923592] [PerformanceMonitoringService] Slow frame detected {"duration_ms":133}
flutter: 🐛 DEBUG [2025-06-25T03:28:00.942636] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:01.008624] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-06-25T03:28:01.260837] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-06-25T03:28:01.697998] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-06-25T03:28:01.726111] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-06-25T03:28:03.308712] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-25T03:28:06.429127] [PerformanceMonitoringService] Slow frame detected {"duration_ms":53}
flutter: 🐛 DEBUG [2025-06-25T03:28:07.758657] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:07.949596] [PerformanceMonitoringService] Slow frame detected {"duration_ms":73}
flutter: 🐛 DEBUG [2025-06-25T03:28:09.298817] [PerformanceMonitoringService] Slow frame detected {"duration_ms":1319}
flutter: 🐛 DEBUG [2025-06-25T03:28:09.833760] [PerformanceMonitoringService] Slow frame detected {"duration_ms":565}
flutter: 🐛 DEBUG [2025-06-25T03:28:09.860078] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-06-25T03:28:10.209088] [PerformanceMonitoringService] Slow frame detected {"duration_ms":150}
flutter: ⚠️ WARNING [2025-06-25T03:28:10.791199] [PerformanceMonitoringService] High memory usage detected {"memory_mb":190.0}
flutter: 🐛 DEBUG [2025-06-25T03:28:13.975586] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:20.559240] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:25.156193] [PerformanceMonitoringService] Slow frame detected {"duration_ms":163}
flutter: 🐛 DEBUG [2025-06-25T03:28:25.242346] [PerformanceMonitoringService] Slow frame detected {"duration_ms":86}
flutter: 🐛 DEBUG [2025-06-25T03:28:25.325582] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-25T03:28:25.359012] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:25.492422] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-06-25T03:28:25.525979] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:25.575570] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-25T03:28:25.790176] [PerformanceMonitoringService] High memory usage detected {"memory_mb":190.0}
flutter: 🐛 DEBUG [2025-06-25T03:28:27.542368] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:27.675954] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:29.044760] [PerformanceMonitoringService] Slow frame detected {"duration_ms":35}
flutter: 🐛 DEBUG [2025-06-25T03:28:29.109178] [PerformanceMonitoringService] Slow frame detected {"duration_ms":64}
flutter: 🐛 DEBUG [2025-06-25T03:28:32.776225] [PerformanceMonitoringService] Slow frame detected {"duration_ms":799}
flutter: 🐛 DEBUG [2025-06-25T03:28:32.956326] [PerformanceMonitoringService] Slow frame detected {"duration_ms":181}
flutter: 🐛 DEBUG [2025-06-25T03:28:33.076355] [PerformanceMonitoringService] Slow frame detected {"duration_ms":118}
flutter: 🐛 DEBUG [2025-06-25T03:28:34.508820] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:37.858919] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:37.942060] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:37.975974] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:38.192507] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-06-25T03:28:38.293288] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: ⚠️ WARNING [2025-06-25T03:28:40.790143] [PerformanceMonitoringService] High memory usage detected {"memory_mb":190.0}
flutter: 🐛 DEBUG [2025-06-25T03:28:42.860074] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-06-25T03:28:42.926508] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-25T03:28:43.009226] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:49.179273] [PerformanceMonitoringService] Slow frame detected {"duration_ms":85}
flutter: 🐛 DEBUG [2025-06-25T03:28:49.258959] [PerformanceMonitoringService] Slow frame detected {"duration_ms":80}
flutter: 🐛 DEBUG [2025-06-25T03:28:49.342456] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:49.809538] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:49.859911] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-25T03:28:49.925670] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-25T03:28:49.977624] [PerformanceMonitoringService] Slow frame detected {"duration_ms":51}
flutter: 🐛 DEBUG [2025-06-25T03:28:50.017330] [PerformanceMonitoringService] Slow frame detected {"duration_ms":39}
flutter: 🐛 DEBUG [2025-06-25T03:28:50.066901] [PerformanceMonitoringService] Slow frame detected {"duration_ms":41}
flutter: 🐛 DEBUG [2025-06-25T03:28:50.092347] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:50.125865] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:50.192367] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:50.258999] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:50.292770] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:50.325628] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:50.375630] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:50.409071] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:54.991595] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-06-25T03:28:55.025842] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:55.059193] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:55.109215] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:55.142760] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:55.193310] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:55.259085] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-25T03:28:55.790407] [PerformanceMonitoringService] High memory usage detected {"memory_mb":190.0}
flutter: 🐛 DEBUG [2025-06-25T03:28:57.393631] [PerformanceMonitoringService] Slow frame detected {"duration_ms":133}
flutter: 🐛 DEBUG [2025-06-25T03:28:57.426147] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:28:57.526186] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:29:02.676812] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-25T03:29:05.560382] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-06-25T03:29:05.592773] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:29:05.692647] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:29:05.859109] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-25T03:29:10.790983] [PerformanceMonitoringService] High memory usage detected {"memory_mb":190.0}
flutter: 🐛 DEBUG [2025-06-25T03:29:12.709104] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:29:12.875835] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:29:12.925925] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-25T03:29:12.975807] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:29:13.026802] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:29:13.192678] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-25T03:29:13.259215] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-25T03:29:13.325899] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:29:18.425881] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:29:18.475869] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:29:18.509535] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:29:18.559620] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:29:18.593320] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:29:18.726273] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:29:18.760286] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-25T03:29:25.790979] [PerformanceMonitoringService] High memory usage detected {"memory_mb":190.0}
flutter: 🐛 DEBUG [2025-06-25T03:29:28.526978] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-25T03:29:30.142722] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-25T03:29:40.790781] [PerformanceMonitoringService] High memory usage detected {"memory_mb":190.0}
flutter: 🐛 DEBUG [2025-06-25T03:29:45.560425] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-06-25T03:29:45.592767] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-25T03:29:55.790848] [PerformanceMonitoringService] High memory usage detected {"memory_mb":190.0}
flutter: ⚠️ WARNING [2025-06-25T03:30:10.790886] [PerformanceMonitoringService] High memory usage detected {"memory_mb":190.0}
flutter: ⚠️ WARNING [2025-06-25T03:30:25.791449] [PerformanceMonitoringService] High memory usage detected {"memory_mb":191.0}
flutter: ⚠️ WARNING [2025-06-25T03:30:40.791108] [PerformanceMonitoringService] High memory usage detected {"memory_mb":191.0}
flutter: ⚠️ WARNING [2025-06-25T03:30:55.791695] [PerformanceMonitoringService] High memory usage detected {"memory_mb":191.0}