nUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T19:32:48.292531] [PerformanceMonitoringService] High memory usage detected {"memory_mb":192.0}
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T19:33:03.293325] [PerformanceMonitoringService] High memory usage detected {"memory_mb":193.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T19:33:18.294519] [PerformanceMonitoringService] High memory usage detected {"memory_mb":194.0}
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T19:33:33.294799] [PerformanceMonitoringService] High memory usage detected {"memory_mb":194.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T19:33:48.294599] [PerformanceMonitoringService] High memory usage detected {"memory_mb":194.0}
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T19:34:03.294568] [PerformanceMonitoringService] High memory usage detected {"memory_mb":194.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T19:34:18.295840] [PerformanceMonitoringService] High memory usage detected {"memory_mb":195.0}
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T19:34:33.296668] [PerformanceMonitoringService] High memory usage detected {"memory_mb":196.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T19:34:48.296074] [PerformanceMonitoringService] High memory usage detected {"memory_mb":195.0}
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T19:35:03.297750] [PerformanceMonitoringService] High memory usage detected {"memory_mb":197.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: ⚠️ WARNING [2025-06-30T19:35:18.298329] [PerformanceMonitoringService] High memory usage detected {"memory_mb":198.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🐛 DEBUG [2025-06-30T19:35:28.958533] [PerformanceMonitoringService] Slow frame detected {"duration_ms":46}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 🐛 DEBUG [2025-06-30T19:35:28.979408] [PerformanceMonitoringService] Slow frame detected {"duration_ms":20}
flutter: 🐛 DEBUG [2025-06-30T19:35:29.591319] [PerformanceMonitoringService] Slow frame detected {"duration_ms":45}
flutter: 🐛 DEBUG [2025-06-30T19:35:29.613228] [PerformanceMonitoringService] Slow frame detected {"duration_ms":21}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T19:35:33.298414] [PerformanceMonitoringService] High memory usage detected {"memory_mb":198.0}
flutter: 🐛 DEBUG [2025-06-30T19:35:33.695862] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🐛 DEBUG [2025-06-30T19:35:38.763615] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T19:35:48.299107] [PerformanceMonitoringService] High memory usage detected {"memory_mb":199.0}
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🐛 DEBUG [2025-06-30T19:35:49.029740] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-06-30T19:35:49.063261] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 🐛 DEBUG [2025-06-30T19:35:53.614400] [PerformanceMonitoringService] Slow frame detected {"duration_ms":34}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T19:36:03.298979] [PerformanceMonitoringService] High memory usage detected {"memory_mb":198.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🐛 DEBUG [2025-06-30T19:36:07.996944] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🐛 DEBUG [2025-06-30T19:36:14.915006] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T19:36:18.298889] [PerformanceMonitoringService] High memory usage detected {"memory_mb":198.0}
flutter: ⚠️ Standard reload failed, using token-based verification only: type 'List<Object?>' is not a subtype of type 'PigeonUserInfo' in type cast
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
