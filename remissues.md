flutter run

The default interactive shell is now zsh.
To update your account to use zsh, please run `chsh -s /bin/zsh`.
For more details, please visit https://support.apple.com/kb/HT208050.
(base) Macs-MacBook-Pro:culture_connect mac$ flutter run
Launching lib/main.dart on iPhone 15 Pro Max in debug mode...
Running Xcode build...                                                  
 └─Compiling, linking and signing...                        76.7s
Xcode build done.                                           273.7s
no valid “aps-environment” entitlement string found for application
flutter: 🚀 Starting app initialization
flutter: ✅ SharedPreferences initialized in 105ms
flutter: ❌ Error preloading asset assets/images/splash.png: Unable to load asset: "assets/images/splash.png".
flutter: The asset does not exist or has empty data.
flutter: ✅ Preloaded asset: assets/animations/splash_animation.json (3741 bytes)
flutter: ✅ Critical assets preloaded in 145ms
flutter: ✅ Firebase core initialized in 636ms
flutter: ✅ App initialization completed in 671ms
flutter: ℹ️ INFO [2025-06-29T15:48:17.402962] [LoggingService] Logging service initialized successfully
flutter: ℹ️ INFO [2025-06-29T15:48:17.412805] [LoggingService] Device info: {name: iPhone 15 Pro Max, model: iPhone, systemName: iOS, systemVersion: 17.2, platform: ios}
flutter: ℹ️ INFO [2025-06-29T15:48:17.414048] [LoggingService] App info: Culture Connect 1.0.0+1
flutter: ✅ Preloaded asset: assets/images/onboarding_1.png (0 bytes)
flutter: ✅ Firebase full features initialized in 181ms
flutter: ℹ️ INFO [2025-06-29T15:48:17.566488] [ErrorHandlingService] Error handling service initialized
flutter: ✅ Preloaded asset: assets/images/onboarding_2.png (0 bytes)
flutter: ✅ Preloaded asset: assets/images/onboarding_3.png (0 bytes)
flutter: ✅ Non-critical assets preloaded in 193ms
flutter: ℹ️ INFO [2025-06-29T15:48:17.588148] [CrashReportingService] Crash reporting service initialized
flutter: ℹ️ INFO [2025-06-29T15:48:17.614292] [AnalyticsService] Analytics service initialized
flutter: 🐛 DEBUG [2025-06-29T15:48:17.623311] [AnalyticsService] Event: app_session_begin {"category":"engagement","parameters":{"timestamp":"2025-06-29T15:48:17.601741"}}
flutter: ℹ️ INFO [2025-06-29T15:48:17.631630] [PerformanceMonitoringService] Performance monitoring service initialized
flutter: ℹ️ INFO [2025-06-29T15:48:17.632246] [App] All services initialized successfully
flutter: 🐛 DEBUG [2025-06-29T15:48:24.845457] [PerformanceMonitoringService] Slow frame detected {"duration_ms":835}
flutter: 🐛 DEBUG [2025-06-29T15:48:24.892444] [PerformanceMonitoringService] Slow frame detected {"duration_ms":45}
flutter: 🐛 DEBUG [2025-06-29T15:48:24.941562] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:48:25.268178] [PerformanceMonitoringService] Slow frame detected {"duration_ms":119}
flutter: 🐛 DEBUG [2025-06-29T15:48:25.291809] [PerformanceMonitoringService] Slow frame detected {"duration_ms":30}
flutter: 🐛 DEBUG [2025-06-29T15:48:26.175517] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:48:26.248471] [PerformanceMonitoringService] Slow frame detected {"duration_ms":74}
flutter: 🐛 DEBUG [2025-06-29T15:48:26.274562] [PerformanceMonitoringService] Slow frame detected {"duration_ms":25}
flutter: 🐛 DEBUG [2025-06-29T15:48:26.626332] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:48:30.671920] [PerformanceMonitoringService] Slow frame detected {"duration_ms":46}
flutter: 🐛 DEBUG [2025-06-29T15:48:31.704331] [PerformanceMonitoringService] Slow frame detected {"duration_ms":978}
flutter: 🐛 DEBUG [2025-06-29T15:48:32.309581] [PerformanceMonitoringService] Slow frame detected {"duration_ms":658}
flutter: 🐛 DEBUG [2025-06-29T15:48:32.450274] [PerformanceMonitoringService] Slow frame detected {"duration_ms":142}
flutter: 🐛 DEBUG [2025-06-29T15:48:32.474478] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-06-29T15:48:32.791552] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-29T15:48:33.091961] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
Syncing files to device iPhone 15 Pro Max...                        6.1s

Flutter run key commands.
r Hot reload. 🔥🔥🔥
R Hot restart.
h List all available interactive commands.
d Detach (terminate "flutter run" but leave application running).
c Clear the screen
q Quit (terminate the application on the device).

A Dart VM Service on iPhone 15 Pro Max is available at: http://127.0.0.1:59960/tsjvUqhZZ8s=/
The Flutter DevTools debugger and profiler on iPhone 15 Pro Max is available at:
http://127.0.0.1:9101?uri=http://127.0.0.1:59960/tsjvUqhZZ8s=/
flutter: 🐛 DEBUG [2025-06-29T15:48:33.683747] [PerformanceMonitoringService] Slow frame detected {"duration_ms":375}
flutter: 🐛 DEBUG [2025-06-29T15:48:33.707802] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-06-29T15:48:35.407804] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:48:35.558460] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:48:36.902173] [PerformanceMonitoringService] Slow frame detected {"duration_ms":44}
flutter: 🐛 DEBUG [2025-06-29T15:48:36.925154] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: ⚠️ WARNING [2025-06-29T15:48:37.632180] [PerformanceMonitoringService] High memory usage detected {"memory_mb":181.0}
flutter: ⚠️ WARNING [2025-06-29T15:48:47.697967] [PerformanceMonitoringService] High memory usage detected {"memory_mb":197.0}
flutter: ⚠️ WARNING [2025-06-29T15:48:52.633778] [PerformanceMonitoringService] High memory usage detected {"memory_mb":181.0}
flutter: 🐛 DEBUG [2025-06-29T15:49:01.643461] [PerformanceMonitoringService] Slow frame detected {"duration_ms":23400}
flutter: ⚠️ WARNING [2025-06-29T15:49:07.630904] [PerformanceMonitoringService] High memory usage detected {"memory_mb":180.0}
flutter: 🐛 DEBUG [2025-06-29T15:49:15.073955] [PerformanceMonitoringService] Slow frame detected {"duration_ms":13433}
flutter: 🐛 DEBUG [2025-06-29T15:49:15.674127] [PerformanceMonitoringService] Slow frame detected {"duration_ms":583}
flutter: 🐛 DEBUG [2025-06-29T15:49:19.123866] [PerformanceMonitoringService] Slow frame detected {"duration_ms":3449}
flutter: 🐛 DEBUG [2025-06-29T15:49:21.873869] [PerformanceMonitoringService] Slow frame detected {"duration_ms":2750}
flutter: ⚠️ WARNING [2025-06-29T15:49:22.630513] [PerformanceMonitoringService] High memory usage detected {"memory_mb":180.0}
flutter: 🐛 DEBUG [2025-06-29T15:49:26.573803] [PerformanceMonitoringService] Slow frame detected {"duration_ms":4683}
flutter: 🐛 DEBUG [2025-06-29T15:49:27.825767] [PerformanceMonitoringService] Slow frame detected {"duration_ms":1249}
flutter: ⚠️ WARNING [2025-06-29T15:49:37.630427] [PerformanceMonitoringService] High memory usage detected {"memory_mb":180.0}
flutter: 🐛 DEBUG [2025-06-29T15:49:41.207043] [PerformanceMonitoringService] Slow frame detected {"duration_ms":13383}
flutter: 🐛 DEBUG [2025-06-29T15:49:41.991336] [PerformanceMonitoringService] Slow frame detected {"duration_ms":17}
flutter: 🐛 DEBUG [2025-06-29T15:49:43.142190] [PerformanceMonitoringService] Slow frame detected {"duration_ms":450}
flutter: 🐛 DEBUG [2025-06-29T15:49:47.523668] [PerformanceMonitoringService] Slow frame detected {"duration_ms":4383}
flutter: 🐛 DEBUG [2025-06-29T15:49:48.323950] [PerformanceMonitoringService] Slow frame detected {"duration_ms":800}
flutter: 🐛 DEBUG [2025-06-29T15:49:50.229109] [PerformanceMonitoringService] Slow frame detected {"duration_ms":38}
flutter: 🐛 DEBUG [2025-06-29T15:49:50.256789] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: ⚠️ WARNING [2025-06-29T15:49:52.630818] [PerformanceMonitoringService] High memory usage detected {"memory_mb":180.0}
flutter: 🐛 DEBUG [2025-06-29T15:49:54.473450] [PerformanceMonitoringService] Slow frame detected {"duration_ms":1433}
flutter: 🐛 DEBUG [2025-06-29T15:49:55.823372] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:50:01.141909] [PerformanceMonitoringService] Slow frame detected {"duration_ms":1650}
flutter: 🐛 DEBUG [2025-06-29T15:50:02.806845] [PerformanceMonitoringService] Slow frame detected {"duration_ms":1249}
flutter: 🐛 DEBUG [2025-06-29T15:50:04.273645] [PerformanceMonitoringService] Slow frame detected {"duration_ms":1433}
flutter: ⚠️ WARNING [2025-06-29T15:50:07.631315] [PerformanceMonitoringService] High memory usage detected {"memory_mb":181.0}
flutter: 🐛 DEBUG [2025-06-29T15:50:10.873311] [PerformanceMonitoringService] Slow frame detected {"duration_ms":5583}
flutter: 🐛 DEBUG [2025-06-29T15:50:15.073886] [PerformanceMonitoringService] Slow frame detected {"duration_ms":533}
flutter: ⚠️ WARNING [2025-06-29T15:50:22.630367] [PerformanceMonitoringService] High memory usage detected {"memory_mb":180.0}
flutter: 🐛 DEBUG [2025-06-29T15:50:26.322979] [PerformanceMonitoringService] Slow frame detected {"duration_ms":4633}
flutter: ⚠️ WARNING [2025-06-29T15:50:37.629464] [PerformanceMonitoringService] High memory usage detected {"memory_mb":179.0}
flutter: 🐛 DEBUG [2025-06-29T15:50:47.979250] [PerformanceMonitoringService] Slow frame detected {"duration_ms":124}
flutter: 🐛 DEBUG [2025-06-29T15:50:48.008508] [PerformanceMonitoringService] Slow frame detected {"duration_ms":61}
flutter: 🐛 DEBUG [2025-06-29T15:50:48.039334] [PerformanceMonitoringService] Slow frame detected {"duration_ms":30}
flutter: 🐛 DEBUG [2025-06-29T15:50:48.078442] [PerformanceMonitoringService] Slow frame detected {"duration_ms":39}
flutter: 🐛 DEBUG [2025-06-29T15:50:48.106164] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-06-29T15:50:48.206738] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:50:48.363720] [PerformanceMonitoringService] Slow frame detected {"duration_ms":41}
flutter: 🐛 DEBUG [2025-06-29T15:50:48.389407] [PerformanceMonitoringService] Slow frame detected {"duration_ms":25}
flutter: ⚠️ WARNING [2025-06-29T15:50:52.630521] [PerformanceMonitoringService] High memory usage detected {"memory_mb":180.0}
flutter: 🐛 DEBUG [2025-06-29T15:50:54.590290] [PerformanceMonitoringService] Slow frame detected {"duration_ms":250}
flutter: 🐛 DEBUG [2025-06-29T15:50:54.672939] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-29T15:50:54.709455] [PerformanceMonitoringService] Slow frame detected {"duration_ms":37}
flutter: 🐛 DEBUG [2025-06-29T15:50:54.739264] [PerformanceMonitoringService] Slow frame detected {"duration_ms":29}
flutter: 🐛 DEBUG [2025-06-29T15:50:54.789678] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:50:54.939473] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:03.489543] [PerformanceMonitoringService] Slow frame detected {"duration_ms":3800}
flutter: 🐛 DEBUG [2025-06-29T15:51:04.322635] [PerformanceMonitoringService] Slow frame detected {"duration_ms":833}
flutter: 🐛 DEBUG [2025-06-29T15:51:04.635006] [PerformanceMonitoringService] Slow frame detected {"duration_ms":300}
flutter: 🐛 DEBUG [2025-06-29T15:51:04.773495] [PerformanceMonitoringService] Slow frame detected {"duration_ms":149}
flutter: 🐛 DEBUG [2025-06-29T15:51:04.939794] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-29T15:51:07.630319] [PerformanceMonitoringService] High memory usage detected {"memory_mb":180.0}
flutter: 🐛 DEBUG [2025-06-29T15:51:13.222474] [PerformanceMonitoringService] Slow frame detected {"duration_ms":166}
flutter: 🐛 DEBUG [2025-06-29T15:51:17.273637] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-06-29T15:51:17.356957] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-06-29T15:51:18.290392] [PerformanceMonitoringService] Slow frame detected {"duration_ms":933}
flutter: 🐛 DEBUG [2025-06-29T15:51:18.405855] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:19.036306] [PerformanceMonitoringService] Slow frame detected {"duration_ms":64}
flutter: 🐛 DEBUG [2025-06-29T15:51:19.072284] [PerformanceMonitoringService] Slow frame detected {"duration_ms":35}
flutter: 🐛 DEBUG [2025-06-29T15:51:19.122488] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-29T15:51:19.172346] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-29T15:51:19.222238] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:19.271539] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:19.322312] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-29T15:51:19.389190] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-29T15:51:19.422444] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:19.472355] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:19.505612] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:19.572496] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-29T15:51:19.622256] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-29T15:51:22.629473] [PerformanceMonitoringService] High memory usage detected {"memory_mb":179.0}
flutter: 🐛 DEBUG [2025-06-29T15:51:29.088874] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:29.222719] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-29T15:51:29.255445] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:29.305568] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:29.356722] [PerformanceMonitoringService] Slow frame detected {"duration_ms":51}
flutter: 🐛 DEBUG [2025-06-29T15:51:29.422575] [PerformanceMonitoringService] Slow frame detected {"duration_ms":65}
flutter: 🐛 DEBUG [2025-06-29T15:51:29.456093] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:29.505717] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:29.538846] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:29.572860] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:29.605610] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:29.639357] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:29.672834] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:29.722714] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:29.756188] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:31.872572] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:34.605490] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:34.639801] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-29T15:51:37.629576] [PerformanceMonitoringService] High memory usage detected {"memory_mb":179.0}
flutter: 🐛 DEBUG [2025-06-29T15:51:44.405529] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:47.588830] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:51:47.706207] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: ⚠️ WARNING [2025-06-29T15:51:52.628092] [PerformanceMonitoringService] High memory usage detected {"memory_mb":177.0}
flutter: 🐛 DEBUG [2025-06-29T15:51:53.305253] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:52:02.338677] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:52:04.366037] [PerformanceMonitoringService] Slow frame detected {"duration_ms":44}
flutter: 🐛 DEBUG [2025-06-29T15:52:04.405302] [PerformanceMonitoringService] Slow frame detected {"duration_ms":38}
flutter: 🐛 DEBUG [2025-06-29T15:52:06.921635] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:52:07.006420] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-29T15:52:07.628065] [PerformanceMonitoringService] High memory usage detected {"memory_mb":177.0}
flutter: 🐛 DEBUG [2025-06-29T15:52:09.605267] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:52:13.905846] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-29T15:52:22.627906] [PerformanceMonitoringService] High memory usage detected {"memory_mb":177.0}
flutter: 🐛 DEBUG [2025-06-29T15:52:28.971738] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:52:29.038301] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-06-29T15:52:31.421472] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:52:31.971429] [PerformanceMonitoringService] Slow frame detected {"duration_ms":383}
flutter: 🐛 DEBUG [2025-06-29T15:52:32.221913] [PerformanceMonitoringService] Slow frame detected {"duration_ms":249}
flutter: 🐛 DEBUG [2025-06-29T15:52:34.021402] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:52:36.089033] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-29T15:52:37.627773] [PerformanceMonitoringService] High memory usage detected {"memory_mb":177.0}
flutter: 🐛 DEBUG [2025-06-29T15:52:45.288160] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:52:50.999806] [PerformanceMonitoringService] Slow frame detected {"duration_ms":150}
flutter: 🐛 DEBUG [2025-06-29T15:52:51.066133] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-29T15:52:51.249113] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-29T15:52:52.620019] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: 🐛 DEBUG [2025-06-29T15:52:54.427332] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:52:54.677129] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-29T15:52:54.760887] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-06-29T15:52:54.944029] [PerformanceMonitoringService] Slow frame detected {"duration_ms":183}
flutter: 🐛 DEBUG [2025-06-29T15:52:55.410163] [PerformanceMonitoringService] Slow frame detected {"duration_ms":466}
flutter: 🐛 DEBUG [2025-06-29T15:52:55.459615] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:52:55.625890] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:53:01.669214] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-29T15:53:07.604694] [PerformanceMonitoringService] High memory usage detected {"memory_mb":154.0}
flutter: ⚠️ WARNING [2025-06-29T15:53:27.597123] [PerformanceMonitoringService] High memory usage detected {"memory_mb":197.0}
flutter: 🐛 DEBUG [2025-06-29T15:53:29.923533] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-06-29T15:53:32.739693] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: Firebase Auth Exception: email-already-in-use - The email address is already in use by another account.
flutter: 🐛 DEBUG [2025-06-29T15:53:33.690438] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: ❌ ERROR [2025-06-29T15:53:33.727782] [FlutterError] A RenderFlex overflowed by 9.2 pixels on the bottom. A RenderFlex overflowed by 9.2 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #16     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #20     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #21     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #22     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #23     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #24     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #25     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #26     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #27     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #28     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #32     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #37     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #38     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #39     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #40     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #41     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #42     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #43     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #44     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #45     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #46     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #47     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #48     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #49     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #50     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #51     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #52     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #53     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #54     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #55     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #56     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #57     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #58     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #59     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #60     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #61     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #62     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #63     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #64     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #65     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #66     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #67     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #71     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #72     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #73     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #74     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #75     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #76     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #77     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #78     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #79     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #80     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #90     _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #91     _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #92     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #93     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #94     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #95     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #96     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #97     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #102    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #103    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #104    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #105    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #106    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #107    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #108    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #109    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #110    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #111    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #112    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #113    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #114    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #115    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #116    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #117    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #118    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #119    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #120    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #121    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #122    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #123    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #124    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #125    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #126    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #127    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #128    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #129    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #130    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #131    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #132    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #133    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #134    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #135    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #136    _invoke (dart:ui/hooks.dart:312:13)
flutter: #137    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #138    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-06-29T15:53:33.815454] [PerformanceMonitoringService] Slow frame detected {"duration_ms":126}
flutter: 🐛 DEBUG [2025-06-29T15:53:33.839980] [PerformanceMonitoringService] Slow frame detected {"duration_ms":23}
flutter: 🐛 DEBUG [2025-06-29T15:53:33.939565] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ⚠️ WARNING [2025-06-29T15:53:42.595110] [PerformanceMonitoringService] High memory usage detected {"memory_mb":195.0}
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ⚠️ WARNING [2025-06-29T15:53:57.594784] [PerformanceMonitoringService] High memory usage detected {"memory_mb":194.0}
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ⚠️ WARNING [2025-06-29T15:54:12.594350] [PerformanceMonitoringService] High memory usage detected {"memory_mb":194.0}
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: 🐛 DEBUG [2025-06-29T15:54:15.137666] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ⚠️ WARNING [2025-06-29T15:54:27.592965] [PerformanceMonitoringService] High memory usage detected {"memory_mb":192.0}
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ⚠️ WARNING [2025-06-29T15:54:42.593413] [PerformanceMonitoringService] High memory usage detected {"memory_mb":193.0}
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ⚠️ WARNING [2025-06-29T15:54:57.592770] [PerformanceMonitoringService] High memory usage detected {"memory_mb":192.0}
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: 🐛 DEBUG [2025-06-29T15:55:11.253917] [PerformanceMonitoringService] Slow frame detected {"duration_ms":34}
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ⚠️ WARNING [2025-06-29T15:55:12.592259] [PerformanceMonitoringService] High memory usage detected {"memory_mb":192.0}
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ⚠️ WARNING [2025-06-29T15:55:27.592534] [PerformanceMonitoringService] High memory usage detected {"memory_mb":192.0}
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ⚠️ WARNING [2025-06-29T15:55:42.592174] [PerformanceMonitoringService] High memory usage detected {"memory_mb":192.0}
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: 🐛 DEBUG [2025-06-29T15:55:54.157025] [PerformanceMonitoringService] Slow frame detected {"duration_ms":1321}
flutter: 🐛 DEBUG [2025-06-29T15:55:54.185523] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ⚠️ WARNING [2025-06-29T15:55:57.592101] [PerformanceMonitoringService] High memory usage detected {"memory_mb":191.0}
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ⚠️ WARNING [2025-06-29T15:56:12.592254] [PerformanceMonitoringService] High memory usage detected {"memory_mb":192.0}
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ⚠️ WARNING [2025-06-29T15:56:27.591897] [PerformanceMonitoringService] High memory usage detected {"memory_mb":191.0}
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ⚠️ WARNING [2025-06-29T15:56:42.591097] [PerformanceMonitoringService] High memory usage detected {"memory_mb":191.0}
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ⚠️ WARNING [2025-06-29T15:56:57.590251] [PerformanceMonitoringService] High memory usage detected {"memory_mb":190.0}
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ⚠️ WARNING [2025-06-29T15:57:12.590894] [PerformanceMonitoringService] High memory usage detected {"memory_mb":190.0}
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ❌ No user is currently signed in - checking auth state...
flutter: ❌ Still no user after waiting - user may need to re-authenticate
flutter: ⚠️ WARNING [2025-06-29T15:57:27.590424] [PerformanceMonitoringService] High memory usage detected {"memory_mb":190.0}
flutter: ❌ No user is currently signed in - checking auth state...
