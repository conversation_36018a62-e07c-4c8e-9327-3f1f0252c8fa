flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:27:01.464535] [PerformanceMonitoringService] High memory usage detected {"memory_mb":164.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:27:16.464800] [PerformanceMonitoringService] High memory usage detected {"memory_mb":164.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:27:31.464744] [PerformanceMonitoringService] High memory usage detected {"memory_mb":164.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:27:46.465734] [PerformanceMonitoringService] High memory usage detected {"memory_mb":165.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:28:01.466012] [PerformanceMonitoringService] High memory usage detected {"memory_mb":165.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:28:16.465303] [PerformanceMonitoringService] High memory usage detected {"memory_mb":165.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:28:31.466532] [PerformanceMonitoringService] High memory usage detected {"memory_mb":166.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:28:46.466714] [PerformanceMonitoringService] High memory usage detected {"memory_mb":166.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:29:01.467976] [PerformanceMonitoringService] High memory usage detected {"memory_mb":167.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:29:16.467713] [PerformanceMonitoringService] High memory usage detected {"memory_mb":167.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:29:31.467340] [PerformanceMonitoringService] High memory usage detected {"memory_mb":167.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:29:46.468736] [PerformanceMonitoringService] High memory usage detected {"memory_mb":168.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:29:55.786295] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:30:10.785328] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:30:25.785462] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:30:40.785198] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:30:55.785421] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:31:10.784407] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:31:25.785927] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:31:40.783911] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:31:55.784852] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:32:10.783755] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:32:25.783951] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:32:40.783348] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:32:55.784961] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:33:10.784459] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:33:25.783391] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:33:40.783092] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:33:55.783140] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:34:10.782503] [PerformanceMonitoringService] High memory usage detected {"memory_mb":182.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:34:25.783985] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:34:40.782953] [PerformanceMonitoringService] High memory usage detected {"memory_mb":182.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:34:55.783514] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:35:10.782982] [PerformanceMonitoringService] High memory usage detected {"memory_mb":182.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:35:25.781576] [PerformanceMonitoringService] High memory usage detected {"memory_mb":181.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:35:40.782903] [PerformanceMonitoringService] High memory usage detected {"memory_mb":182.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:35:55.782477] [PerformanceMonitoringService] High memory usage detected {"memory_mb":182.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:36:10.782328] [PerformanceMonitoringService] High memory usage detected {"memory_mb":182.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:36:25.782469] [PerformanceMonitoringService] High memory usage detected {"memory_mb":182.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:36:40.780916] [PerformanceMonitoringService] High memory usage detected {"memory_mb":180.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:36:55.780833] [PerformanceMonitoringService] High memory usage detected {"memory_mb":180.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:37:10.782182] [PerformanceMonitoringService] High memory usage detected {"memory_mb":182.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:37:25.780862] [PerformanceMonitoringService] High memory usage detected {"memory_mb":180.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:37:40.781388] [PerformanceMonitoringService] High memory usage detected {"memory_mb":181.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: ⚠️ WARNING [2025-06-30T20:37:55.781601] [PerformanceMonitoringService] High memory usage detected {"memory_mb":181.0}
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
